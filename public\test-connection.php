<?php
// XAMPP PHP Connection Test
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$testResults = [];
$overallStatus = 'success';

function addResult($name, $status, $message, $data = null) {
    global $testResults, $overallStatus;
    
    $testResults[] = [
        'test' => $name,
        'status' => $status,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ];
    
    if ($status === 'error') {
        $overallStatus = 'error';
    } elseif ($status === 'warning' && $overallStatus !== 'error') {
        $overallStatus = 'warning';
    }
}

// Test 1: PHP Environment
try {
    $phpInfo = [
        'version' => phpversion(),
        'mysqli_loaded' => extension_loaded('mysqli'),
        'pdo_mysql_loaded' => extension_loaded('pdo_mysql'),
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'http_host' => $_SERVER['HTTP_HOST'] ?? 'Unknown'
    ];
    
    if ($phpInfo['mysqli_loaded']) {
        addResult('PHP Environment', 'success', 'PHP environment is ready for MySQL', $phpInfo);
    } else {
        addResult('PHP Environment', 'error', 'MySQLi extension not loaded', $phpInfo);
    }
} catch (Exception $e) {
    addResult('PHP Environment', 'error', 'Failed to check PHP environment: ' . $e->getMessage());
}

// Test 2: Connection.php Test
try {
    if (file_exists('connection.php')) {
        addResult('Connection File', 'success', 'connection.php file exists');
        
        // Capture any output from connection.php
        ob_start();
        $connectionError = null;
        
        try {
            include 'connection.php';
        } catch (Exception $e) {
            $connectionError = $e->getMessage();
        }
        
        $includeOutput = ob_get_clean();
        
        if ($connectionError) {
            addResult('Connection Include', 'error', 'Error including connection.php: ' . $connectionError);
        } elseif (!empty($includeOutput)) {
            addResult('Connection Include', 'error', 'connection.php produced output (likely an error): ' . $includeOutput);
        } else {
            addResult('Connection Include', 'success', 'connection.php included without errors');
            
            // Test the connection object
            if (isset($conn) && $conn instanceof mysqli) {
                if ($conn->connect_error) {
                    addResult('MySQL Connection', 'error', 'MySQL connection failed: ' . $conn->connect_error, [
                        'host' => $servername ?? 'unknown',
                        'database' => $dbname ?? 'unknown',
                        'user' => $username ?? 'unknown'
                    ]);
                } else {
                    addResult('MySQL Connection', 'success', 'Successfully connected to MySQL', [
                        'host' => $servername ?? 'unknown',
                        'database' => $dbname ?? 'unknown',
                        'user' => $username ?? 'unknown',
                        'connection_id' => $conn->thread_id
                    ]);
                    
                    // Test 3: Database Information
                    try {
                        $result = $conn->query("SELECT DATABASE() as current_db, VERSION() as mysql_version, USER() as current_user");
                        if ($result && $row = $result->fetch_assoc()) {
                            addResult('Database Info', 'success', 'Database information retrieved', $row);
                        } else {
                            addResult('Database Info', 'warning', 'Could not retrieve database information');
                        }
                    } catch (Exception $e) {
                        addResult('Database Info', 'error', 'Database info query failed: ' . $e->getMessage());
                    }
                    
                    // Test 4: Check if facebook_db exists
                    try {
                        $result = $conn->query("SHOW DATABASES LIKE 'facebook_db'");
                        if ($result && $result->num_rows > 0) {
                            addResult('Database Exists', 'success', 'facebook_db database exists');
                            
                            // Test 5: Table Structure
                            $tables = ['members', 'pages', 'posts'];
                            $tableInfo = [];
                            
                            foreach ($tables as $table) {
                                try {
                                    $result = $conn->query("SHOW TABLES LIKE '$table'");
                                    $exists = $result && $result->num_rows > 0;
                                    $tableInfo[$table] = ['exists' => $exists];
                                    
                                    if ($exists) {
                                        // Get row count
                                        $countResult = $conn->query("SELECT COUNT(*) as count FROM $table");
                                        if ($countResult) {
                                            $count = $countResult->fetch_assoc()['count'];
                                            $tableInfo[$table]['count'] = $count;
                                        }
                                        
                                        // Get table structure
                                        $structResult = $conn->query("DESCRIBE $table");
                                        if ($structResult) {
                                            $columns = [];
                                            while ($col = $structResult->fetch_assoc()) {
                                                $columns[] = $col['Field'];
                                            }
                                            $tableInfo[$table]['columns'] = $columns;
                                        }
                                    }
                                } catch (Exception $e) {
                                    $tableInfo[$table] = ['error' => $e->getMessage()];
                                }
                            }
                            
                            $allTablesExist = $tableInfo['members']['exists'] && $tableInfo['pages']['exists'] && $tableInfo['posts']['exists'];
                            
                            if ($allTablesExist) {
                                addResult('Table Structure', 'success', 'All required tables exist', $tableInfo);
                            } else {
                                addResult('Table Structure', 'warning', 'Some tables are missing', $tableInfo);
                            }
                            
                            // Test 6: Data Operations Test
                            if ($allTablesExist) {
                                try {
                                    $testId = 'php_test_' . time();
                                    $testName = 'PHP XAMPP Test User';
                                    $testEmail = '<EMAIL>';
                                    $testRole = 'Tester';
                                    
                                    // Test INSERT
                                    $insertStmt = $conn->prepare("INSERT INTO members (id, name, email, role) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE name = ?");
                                    if ($insertStmt) {
                                        $insertStmt->bind_param("sssss", $testId, $testName, $testEmail, $testRole, $testName);
                                        
                                        if ($insertStmt->execute()) {
                                            addResult('Data Insert', 'success', 'Successfully inserted test data', [
                                                'test_id' => $testId,
                                                'affected_rows' => $insertStmt->affected_rows
                                            ]);
                                            
                                            // Test SELECT
                                            $selectStmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
                                            $selectStmt->bind_param("s", $testId);
                                            $selectStmt->execute();
                                            $selectResult = $selectStmt->get_result();
                                            
                                            if ($selectResult && $selectResult->num_rows > 0) {
                                                $userData = $selectResult->fetch_assoc();
                                                addResult('Data Select', 'success', 'Successfully retrieved test data', $userData);
                                                
                                                // Clean up
                                                $deleteStmt = $conn->prepare("DELETE FROM members WHERE id = ?");
                                                $deleteStmt->bind_param("s", $testId);
                                                if ($deleteStmt->execute()) {
                                                    addResult('Data Cleanup', 'success', 'Test data cleaned up');
                                                }
                                            } else {
                                                addResult('Data Select', 'error', 'Could not retrieve inserted test data');
                                            }
                                        } else {
                                            addResult('Data Insert', 'error', 'Failed to insert test data: ' . $insertStmt->error);
                                        }
                                    } else {
                                        addResult('Data Insert', 'error', 'Failed to prepare insert statement: ' . $conn->error);
                                    }
                                } catch (Exception $e) {
                                    addResult('Data Operations', 'error', 'Data operations test failed: ' . $e->getMessage());
                                }
                            }
                            
                        } else {
                            addResult('Database Exists', 'error', 'facebook_db database does not exist', [
                                'solution' => 'Create database: CREATE DATABASE facebook_db;'
                            ]);
                        }
                    } catch (Exception $e) {
                        addResult('Database Check', 'error', 'Failed to check database existence: ' . $e->getMessage());
                    }
                }
            } else {
                addResult('MySQL Connection', 'error', 'connection.php did not create a valid mysqli connection object');
            }
        }
    } else {
        addResult('Connection File', 'error', 'connection.php file does not exist');
    }
} catch (Exception $e) {
    addResult('Connection Test', 'error', 'Failed to test connection.php: ' . $e->getMessage());
}

// Test 7: XAMPP Environment Check
try {
    $xamppInfo = [
        'php_version' => phpversion(),
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
        'server_name' => $_SERVER['SERVER_NAME'] ?? 'Unknown',
        'server_port' => $_SERVER['SERVER_PORT'] ?? 'Unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
        'is_xampp' => strpos($_SERVER['SERVER_SOFTWARE'] ?? '', 'Apache') !== false,
        'mysql_extension' => extension_loaded('mysqli') ? 'Available' : 'Not Available',
        'current_time' => date('Y-m-d H:i:s')
    ];
    
    addResult('XAMPP Environment', 'success', 'XAMPP environment information collected', $xamppInfo);
} catch (Exception $e) {
    addResult('XAMPP Environment', 'warning', 'Could not collect XAMPP info: ' . $e->getMessage());
}

// Return results
$response = [
    'success' => $overallStatus !== 'error',
    'status' => $overallStatus,
    'total_tests' => count($testResults),
    'successful_tests' => count(array_filter($testResults, function($t) { return $t['status'] === 'success'; })),
    'warning_tests' => count(array_filter($testResults, function($t) { return $t['status'] === 'warning'; })),
    'error_tests' => count(array_filter($testResults, function($t) { return $t['status'] === 'error'; })),
    'tests' => $testResults,
    'timestamp' => date('Y-m-d H:i:s'),
    'recommendations' => []
];

// Add recommendations based on results
if ($overallStatus === 'success') {
    $response['recommendations'][] = '✅ PHP connection is working perfectly with XAMPP!';
    $response['recommendations'][] = '✅ You can use PHP for database operations alongside Node.js';
    $response['recommendations'][] = '✅ Consider using PHP for admin tasks or data imports';
} elseif ($overallStatus === 'warning') {
    $response['recommendations'][] = '⚠️ PHP connection works but some features may be limited';
    $response['recommendations'][] = '⚠️ Check the warnings and consider creating missing tables';
    $response['recommendations'][] = '⚠️ You can still use this for basic database operations';
} else {
    $response['recommendations'][] = '❌ PHP connection has issues with XAMPP setup';
    $response['recommendations'][] = '❌ Check if XAMPP MySQL service is running';
    $response['recommendations'][] = '❌ Verify database credentials in connection.php';
    $response['recommendations'][] = '❌ Consider using Node.js mysql2 approach as primary method';
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
