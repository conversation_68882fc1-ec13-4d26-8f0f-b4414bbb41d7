Chunk 1/1 completed
GET /api/config - Current settings:
- headlessMode: true
- chunkSize: 30
- parallelPages: 10
- minLoopDelay: 60000 ms
- maxLoopDelay: 120000 ms
- minSearchDelay: 20000 ms
- maxSearchDelay: 45000 ms
- metricsUpdateDelay: 5000 ms
- postsPerPage: 5
Serving 30 pages from pages.json
No Facebook cookies file found
No cookies found
New client connected
Serving 1 team members from team_members.json
Serving 30 posts from final_filtered_posts.json
Automation cycle completed
Saved scraping history: 1 total cycles
📊 Scraping cycle #1 completed
Next cycle in 1.1492166666666668 minutes
Starting automation cycle...
🔄 Reset ETA timing for new cycle (keeping posts count)
Processing chunk of 30 pages
🔄 Selected least-used proxy 79/100: uMbiga2K (0 requests, 67 available with this count)
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy uMbiga2K: 1 total requests
Setting up proxy authentication for new page...
📊 Using proxy for chunk: proxy.packetstream.io:31112 (session: wro6GGv6)
🔄 Processing chunk with 10 parallel pages
📦 Processing batch 1: 10 pages in parallel
🔄 Selected least-used proxy 87/100: JcZWXEAy (0 requests, 66 available with this count)
🔄 Pre-allocated proxy session 1/10: JcZWXEAy
🔄 Selected least-used proxy 82/99: p6HpvyAX (0 requests, 65 available with this count)
🔄 Pre-allocated proxy session 2/10: p6HpvyAX
🔄 Selected least-used proxy 30/98: ycsGF5Zk (0 requests, 64 available with this count)
🔄 Pre-allocated proxy session 3/10: ycsGF5Zk
🔄 Selected least-used proxy 40/97: HLAhIXoY (0 requests, 63 available with this count)
🔄 Pre-allocated proxy session 4/10: HLAhIXoY
🔄 Selected least-used proxy 46/96: zB1M3iL8 (0 requests, 62 available with this count)
🔄 Pre-allocated proxy session 5/10: zB1M3iL8
🔄 Selected least-used proxy 91/95: z7XIBuZF (0 requests, 61 available with this count)
🔄 Pre-allocated proxy session 6/10: z7XIBuZF
🔄 Selected least-used proxy 8/94: 2YeUEV6k (0 requests, 60 available with this count)
🔄 Pre-allocated proxy session 7/10: 2YeUEV6k
🔄 Selected least-used proxy 57/93: tsrIbQJ6 (0 requests, 59 available with this count)
🔄 Pre-allocated proxy session 8/10: tsrIbQJ6
🔄 Selected least-used proxy 42/92: RY5IAtZV (0 requests, 58 available with this count)
🔄 Pre-allocated proxy session 9/10: RY5IAtZV
🔄 Selected least-used proxy 42/91: FbxNUsEm (0 requests, 57 available with this count)
🔄 Pre-allocated proxy session 10/10: FbxNUsEm
🎯 Page 1 using pre-allocated proxy: JcZWXEAy
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy JcZWXEAy: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/sharqiyatv/
⏱️ Page 2 waiting 300ms for staggered start...
⏱️ Page 3 waiting 600ms for staggered start...
⏱️ Page 4 waiting 900ms for staggered start...
⏱️ Page 5 waiting 1200ms for staggered start...
⏱️ Page 6 waiting 1500ms for staggered start...
⏱️ Page 7 waiting 1800ms for staggered start...
⏱️ Page 8 waiting 2100ms for staggered start...
⏱️ Page 9 waiting 2400ms for staggered start...
⏱️ Page 10 waiting 2700ms for staggered start...
📊 Using proxy: proxy.packetstream.io:31112 (session: JcZWXEAy) for www.facebook.com
Navigating to: https://www.facebook.com/sharqiyatv/
🎯 Page 2 using pre-allocated proxy: p6HpvyAX
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy p6HpvyAX: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/Brothersirq/
📊 Using proxy: proxy.packetstream.io:31112 (session: p6HpvyAX) for www.facebook.com
Navigating to: https://www.facebook.com/Brothersirq/
🎯 Page 3 using pre-allocated proxy: ycsGF5Zk
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy ycsGF5Zk: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/AlArabiya.Iraq/
📊 Using proxy: proxy.packetstream.io:31112 (session: ycsGF5Zk) for www.facebook.com
Navigating to: https://www.facebook.com/AlArabiya.Iraq/
🎯 Page 4 using pre-allocated proxy: HLAhIXoY
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy HLAhIXoY: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/DiwanTV/
📊 Using proxy: proxy.packetstream.io:31112 (session: HLAhIXoY) for www.facebook.com
Navigating to: https://www.facebook.com/DiwanTV/
🎯 Page 5 using pre-allocated proxy: zB1M3iL8
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy zB1M3iL8: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/NaynawaAlghadTv/
📊 Using proxy: proxy.packetstream.io:31112 (session: zB1M3iL8) for www.facebook.com
Navigating to: https://www.facebook.com/NaynawaAlghadTv/
🎯 Page 6 using pre-allocated proxy: z7XIBuZF
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy z7XIBuZF: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/alawi1991
📊 Using proxy: proxy.packetstream.io:31112 (session: z7XIBuZF) for www.facebook.com
Navigating to: https://www.facebook.com/alawi1991
🎯 Page 7 using pre-allocated proxy: 2YeUEV6k
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy 2YeUEV6k: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/alforattv.net
📊 Using proxy: proxy.packetstream.io:31112 (session: 2YeUEV6k) for www.facebook.com
Navigating to: https://www.facebook.com/alforattv.net
🎯 Page 8 using pre-allocated proxy: tsrIbQJ6
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy tsrIbQJ6: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/AlHadath/
📊 Using proxy: proxy.packetstream.io:31112 (session: tsrIbQJ6) for www.facebook.com
Navigating to: https://www.facebook.com/AlHadath/
🎯 Page 9 using pre-allocated proxy: RY5IAtZV
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy RY5IAtZV: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/tshrin24/
🎯 Page 10 using pre-allocated proxy: FbxNUsEm
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy FbxNUsEm: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/IraQeenWBas/
📊 Using proxy: proxy.packetstream.io:31112 (session: RY5IAtZV) for www.facebook.com
Navigating to: https://www.facebook.com/tshrin24/
Error with dedicated browser for https://www.facebook.com/alforattv.net: Error: net::ERR_TUNNEL_CONNECTION_FAILED at https://www.facebook.com/alforattv.net
    at navigate (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:183:27)
    at async Deferred.race (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\node_modules\puppeteer-core\lib\cjs\puppeteer\util\Deferred.js:36:20)
    at async CdpFrame.goto (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Frame.js:149:25)
    at async CdpPage.goto (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\node_modules\puppeteer-core\lib\cjs\puppeteer\api\Page.js:574:20)
    at async processPageInTabWithSpecificProxy (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\server.js:6965:5)
    at async C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\server.js:8300:28
    at async Promise.all (index 6)
    at async processChunk (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\server.js:8344:28)
    at async Timeout.automationLoop [as _onTimeout] (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\server.js:10009:27)
📊 Using proxy: proxy.packetstream.io:31112 (session: FbxNUsEm) for www.facebook.com
Navigating to: https://www.facebook.com/IraQeenWBas/
🔄 Closed browser after error for: https://www.facebook.com/alforattv.net
⚠️ Network/proxy error for https://www.facebook.com/alforattv.net - marking proxy as failed and retrying
🔍 Error type: net::ERR_TUNNEL_CONNECTION_FAILED at https://www.facebook.com/alforattv.net...
❌ Permanently marked proxy as failed: proxy.packetstream.io:31112 (1 total failed)
🗑️ Removed proxy from active rotation: proxy.packetstream.io:31112 (99 remaining active)
💾 Saved 99 active proxies and 1 permanently failed proxies to file
💾 Saved proxy statistics for 100 proxies to file
⚠️ Proxy 2YeUEV6k already permanently failed (skipping duplicate failure)
💾 Saved proxy statistics for 100 proxies to file
🚫 Proxy permanently failed: 2YeUEV6k
🔄 [xz4mo6] Retrying https://www.facebook.com/alforattv.net with different proxy due to connection error...
🔄 Selected least-used proxy 26/99: YcTyaV2x (0 requests, 56 available with this count)
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy YcTyaV2x: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/alforattv.net
Navigating to: https://www.facebook.com/alforattv.net
Successfully navigated to https://www.facebook.com/sharqiyatv/
💾 Saved proxy statistics for 100 proxies to file
Successfully navigated to https://www.facebook.com/Brothersirq/
💾 Saved proxy statistics for 100 proxies to file
Successfully navigated to https://www.facebook.com/AlArabiya.Iraq/
💾 Saved proxy statistics for 100 proxies to file
Successfully navigated to https://www.facebook.com/DiwanTV/
💾 Saved proxy statistics for 100 proxies to file
Successfully navigated to https://www.facebook.com/NaynawaAlghadTv/
💾 Saved proxy statistics for 100 proxies to file
Successfully navigated to https://www.facebook.com/alforattv.net
Successfully navigated to https://www.facebook.com/IraQeenWBas/
💾 Saved proxy statistics for 100 proxies to file
Successfully navigated to https://www.facebook.com/AlHadath/
💾 Saved proxy statistics for 100 proxies to file
Successfully navigated to https://www.facebook.com/tshrin24/
💾 Saved proxy statistics for 100 proxies to file
Performing random, slow scroll event
Performing random, slow scroll event
Performing random, slow scroll event
Idling for 1362 ms after scrolling
Successfully navigated to https://www.facebook.com/alawi1991
💾 Saved proxy statistics for 100 proxies to file
Idling for 1580 ms after scrolling
Performing random, slow scroll event
Performing random, slow scroll event
Idling for 1783 ms after scrolling
Performing random, slow scroll event
Idling for 1582 ms after scrolling
Idling for 2962 ms after scrolling
Idling for 2228 ms after scrolling
Checking post: "الخوة النظيفة د.عثمان الجحيشي : أوصلنا جزء بسيط من" from https://www.facebook.com/Brothersirq/
Engagement metrics before saving: 0 likes, 0 comments, 0 shares
⚠️ DUPLICATE POST: "الخوة النظيفة د.عثمان الجحيشي : أوصلنا جزء بسيط من" from https://www.facebook.com/Brothersirq/ skipped
🔄 Closed dedicated browser for: https://www.facebook.com/Brothersirq/
⚠️ Proxy p6HpvyAX already at max success rate: 1/1 (skipping duplicate success)
✅ Proxy success recorded for: p6HpvyAX
Checking post: "العربية العراق - Al Arabiya Iraq وزارة النفط تكشف" from https://www.facebook.com/AlArabiya.Iraq/
Engagement metrics before saving: 0 likes, 0 comments, 0 shares
⚠️ DUPLICATE POST: "العربية العراق - Al Arabiya Iraq وزارة النفط تكشف" from https://www.facebook.com/AlArabiya.Iraq/ skipped
🔄 Closed dedicated browser for: https://www.facebook.com/AlArabiya.Iraq/
⚠️ Proxy ycsGF5Zk already at max success rate: 1/1 (skipping duplicate success)
✅ Proxy success recorded for: ycsGF5Zk
Performing random, slow scroll event
Checking post: "AlSharqiya تلفزيون الشرقية السوداني يقول إن حصر ال" from https://www.facebook.com/sharqiyatv/
Engagement metrics before saving: 0 likes, 0 comments, 0 shares
⚠️ DUPLICATE POST: "AlSharqiya تلفزيون الشرقية السوداني يقول إن حصر ال" from https://www.facebook.com/sharqiyatv/ skipped
Checking post: "قناة ديوان الفضائية - Diwan TV مواقيت الصلاة لمدين" from https://www.facebook.com/DiwanTV/
Engagement metrics before saving: 0 likes, 0 comments, 0 shares
⚠️ DUPLICATE POST: "قناة ديوان الفضائية - Diwan TV مواقيت الصلاة لمدين" from https://www.facebook.com/DiwanTV/ skipped
🔄 Closed dedicated browser for: https://www.facebook.com/sharqiyatv/
⚠️ Proxy JcZWXEAy already at max success rate: 1/1 (skipping duplicate success)
✅ Proxy success recorded for: JcZWXEAy
🔄 Closed dedicated browser for: https://www.facebook.com/DiwanTV/
⚠️ Proxy HLAhIXoY already at max success rate: 1/1 (skipping duplicate success)
✅ Proxy success recorded for: HLAhIXoY
Performing random, slow scroll event
Idling for 2811 ms after scrolling
Performing random, slow scroll event
Idling for 1969 ms after scrolling
No post content found at https://www.facebook.com/alforattv.net
Idling for 1524 ms after scrolling
🔄 Closed dedicated browser for: https://www.facebook.com/alforattv.net
🔄 [lp3nac] Retrying https://www.facebook.com/alforattv.net with different proxy due to no content found...
🔄 Selected least-used proxy 54/99: zJlc5Bsm (0 requests, 55 available with this count)
💾 Saved proxy statistics for 100 proxies to file
📊 Recorded request for proxy zJlc5Bsm: 1 total requests
🔄 Creating dedicated browser with fresh proxy for: https://www.facebook.com/alforattv.net
Navigating to: https://www.facebook.com/alforattv.net
Performing random, slow scroll event
Checking post: "Naynawa Alghad - نينوى الغد يُرفع الآن أذان الفجر" from https://www.facebook.com/NaynawaAlghadTv/
Engagement metrics before saving: 0 likes, 0 comments, 0 shares
⚠️ DUPLICATE POST: "Naynawa Alghad - نينوى الغد يُرفع الآن أذان الفجر" from https://www.facebook.com/NaynawaAlghadTv/ skipped
🔄 Closed dedicated browser for: https://www.facebook.com/NaynawaAlghadTv/
⚠️ Proxy zB1M3iL8 already at max success rate: 1/1 (skipping duplicate success)
✅ Proxy success recorded for: zB1M3iL8
Idling for 1573 ms after scrolling
Checking post: "تشرين 24 #مونكته – عركة "چلاب النگرة" بين مهند الع" from https://www.facebook.com/tshrin24/
Engagement metrics before saving: 0 likes, 0 comments, 0 shares
⚠️ DUPLICATE POST: "تشرين 24 #مونكته – عركة "چلاب النگرة" بين مهند الع" from https://www.facebook.com/tshrin24/ skipped
🔄 Closed dedicated browser for: https://www.facebook.com/tshrin24/
⚠️ Proxy RY5IAtZV already at max success rate: 1/1 (skipping duplicate success)
✅ Proxy success recorded for: RY5IAtZV
Checking post: "قناة الحدث Al Hadath بعد مباحثات ليبية أوروبية حول" from https://www.facebook.com/AlHadath/
Engagement metrics before saving: 0 likes, 0 comments, 0 shares
⚠️ DUPLICATE POST: "قناة الحدث Al Hadath بعد مباحثات ليبية أوروبية حول" from https://www.facebook.com/AlHadath/ skipped
🔄 Closed dedicated browser for: https://www.facebook.com/AlHadath/
⚠️ Proxy tsrIbQJ6 already at max success rate: 1/1 (skipping duplicate success)
✅ Proxy success recorded for: tsrIbQJ6
Checking post: "عراقيين وبس - Iraqeen W Bas الســـــوداني: مشروع ت" from https://www.facebook.com/IraQeenWBas/
Engagement metrics before saving: 0 likes, 0 comments, 0 shares
⚠️ DUPLICATE POST: "عراقيين وبس - Iraqeen W Bas الســـــوداني: مشروع ت" from https://www.facebook.com/IraQeenWBas/ skipped
🔄 Closed dedicated browser for: https://www.facebook.com/IraQeenWBas/
⚠️ Proxy FbxNUsEm already at max success rate: 1/1 (skipping duplicate success)
✅ Proxy success recorded for: FbxNUsEm
Checking post: "علاوي الاعلامي الآن تم صرف رواتب منتسبي وزارة الدف" from https://www.facebook.com/alawi1991
Engagement metrics before saving: 0 likes, 0 comments, 0 shares
⚠️ DUPLICATE POST: "علاوي الاعلامي الآن تم صرف رواتب منتسبي وزارة الدف" from https://www.facebook.com/alawi1991 skipped
🔄 Closed dedicated browser for: https://www.facebook.com/alawi1991
⚠️ Proxy z7XIBuZF already at max success rate: 1/1 (skipping duplicate success)
✅ Proxy success recorded for: z7XIBuZF
Stopping scraping loop...
Saved scraping history: 0 total cycles
🔄 Scraping cycle count reset to 0
Cleared automation timer
Browser closed gracefully
Browser reference cleared
Scraping loop stopped
Force closing browser due to timeout
Successfully navigated to https://www.facebook.com/alforattv.net
Performing random, slow scroll event
Idling for 1174 ms after scrolling
Checking post: "قناة الفرات - Alforat TV ناديتك يا حسين وأنا مكسور" from https://www.facebook.com/alforattv.net
Engagement metrics before saving: 0 likes, 0 comments, 0 shares
⚠️ DUPLICATE POST: "قناة الفرات - Alforat TV ناديتك يا حسين وأنا مكسور" from https://www.facebook.com/alforattv.net skipped
🔄 Closed dedicated browser for: https://www.facebook.com/alforattv.net
✅ [lp3nac] Content retry successful for https://www.facebook.com/alforattv.net
✅ [xz4mo6] Connection retry successful for https://www.facebook.com/alforattv.net
Progress: 10/30 pages completed (batch: 10 successful, 0 failed)
🧹 Cleaning up batch 1 resources...
Waiting 29.097 seconds before next batch...
Scraper has been stopped, aborting remaining pages
Error closing page: Error: Protocol error: Connection closed.
    at Connection._rawSend (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Connection.js:97:35)
    at Connection.send (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Connection.js:90:21)
    at CdpPage.close (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\node_modules\puppeteer-core\lib\cjs\puppeteer\cdp\Page.js:842:34)
    at async processChunk (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\server.js:8410:9)
    at async Timeout.automationLoop (C:\Users\<USER>\Desktop\projects\facebook_telegram_bot\server.js:10009:27)
Chunk 1/1 completed
Automation cycle completed
Saved scraping history: 1 total cycles
📊 Scraping cycle #1 completed
Next cycle in 1.42185 minutes