// Test proxy authentication
const axios = require('axios');
const { HttpsProxyAgent } = require('https-proxy-agent');
const proxyManager = require('./proxy-manager');

async function testProxyAuth() {
  console.log('🧪 Testing proxy authentication...\n');
  
  // Get a proxy from the manager
  const proxy = proxyManager.getNextProxy();
  if (!proxy) {
    console.log('❌ No proxies available');
    return;
  }
  
  console.log(`🔍 Testing proxy: ${proxy.split(':')[0]}:${proxy.split(':')[1]}:${proxy.split(':')[2]}:****`);
  
  try {
    // Parse proxy
    const proxyParts = proxy.split(':');
    const host = proxyParts[0];
    const port = proxyParts[1];
    const username = proxyParts[2];
    const password = proxyParts[3];
    
    // Create proxy URL
    const proxyUrl = `http://${username}:${password}@${host}:${port}`;
    console.log(`📡 Proxy URL: http://${username}:****@${host}:${port}`);
    
    // Create proxy agent
    const agent = new HttpsProxyAgent(proxyUrl);
    
    // Test with a simple HTTP request
    console.log('🌐 Testing HTTP request through proxy...');
    const response = await axios.get('https://httpbin.org/ip', {
      httpsAgent: agent,
      timeout: 10000
    });
    
    console.log('✅ Proxy authentication successful!');
    console.log('📍 Response IP:', response.data.origin);
    
    // Test with Facebook (just to check if it reaches)
    console.log('\n🔍 Testing Facebook access through proxy...');
    try {
      const fbResponse = await axios.get('https://www.facebook.com', {
        httpsAgent: agent,
        timeout: 15000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      console.log('✅ Facebook access successful through proxy!');
      console.log('📊 Response status:', fbResponse.status);
      
    } catch (fbError) {
      console.log('⚠️ Facebook access failed:', fbError.message);
      if (fbError.response) {
        console.log('📊 Response status:', fbError.response.status);
      }
    }
    
  } catch (error) {
    console.log('❌ Proxy authentication failed!');
    console.log('🔍 Error details:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Connection refused - proxy server might be down');
    } else if (error.code === 'ENOTFOUND') {
      console.log('💡 Host not found - check proxy hostname');
    } else if (error.code === 'ECONNRESET') {
      console.log('💡 Connection reset - possible authentication failure');
    } else if (error.response && error.response.status === 407) {
      console.log('💡 HTTP 407 - Proxy authentication required (wrong credentials)');
    }
  }
}

// Test multiple proxies
async function testMultipleProxies() {
  console.log('🔄 Testing multiple proxies...\n');
  
  for (let i = 0; i < 3; i++) {
    console.log(`\n--- Test ${i + 1} ---`);
    await testProxyAuth();
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds between tests
  }
}

testMultipleProxies().catch(console.error);
