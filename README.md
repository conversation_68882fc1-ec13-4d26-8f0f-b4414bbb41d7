# Facebook Marketing Team Monitoring System

A comprehensive **Facebook Posts Monitoring and Analytics Platform** designed to track marketing team performance across multiple Facebook pages with advanced reverse engineering capabilities, real-time notifications, and detailed analytics.

## 🌟 Overview

This system provides complete oversight of your marketing team's Facebook activities, automatically scraping posts, tracking engagement metrics, and generating detailed performance reports. Built with sophisticated reverse engineering techniques to reliably extract data from Facebook's complex and frequently changing DOM structure.

## 🚀 Key Features

### 📊 **Advanced Facebook Scraping**
- **Reverse-engineered Facebook DOM parsing** with obfuscated CSS class detection
- **Multi-post extraction** from pages with configurable limits (1-50 posts per page)
- **Smart content filtering** and text normalization
- **Media URL extraction** for images and videos
- **Live stream detection** and video duration parsing
- **Anti-detection mechanisms** using Puppeteer Stealth plugin

### 👥 **Team Management System**
- **Individual team member assignment** to specific Facebook pages
- **Performance tracking** per team member with detailed analytics
- **Bulk and individual post scraping** for team members
- **Role-based access control** and permissions
- **Team member metrics updates** with progress tracking

### 📈 **Engagement Metrics Tracking**
- **Real-time metrics extraction**: Likes, Comments, Shares, Views
- **Intelligent post type detection** (Video posts don't have shares)
- **Bulk metrics updates** with configurable delays (1-300 seconds)
- **Custom URL support** for manual post tracking
- **Automatic timestamp extraction** from complex Facebook structures

### 🤖 **Telegram Bot Integration**
- **Arabic interface** for easy team management
- **Real-time notifications** when new posts are discovered
- **Remote control** of scraping operations
- **User authorization system** with access control
- **Configuration management** via chat commands

### 🌐 **Modern Web Dashboard**
- **Real-time post display** with live updates via Socket.IO
- **Team member filtering** and post assignment views
- **Interactive post management** (edit URLs, delete posts, update metrics)
- **Admin panel** with comprehensive configuration options
- **Export functionality** in multiple formats (JSON/CSV)

### 🔧 **Advanced Configuration**
- **Headless/Visible browser modes** for debugging and production
- **Configurable scraping delays** and chunk sizes
- **Facebook authentication management** with cookie persistence
- **Auto-export scheduling** with cron jobs
- **Error handling** and retry mechanisms

## 🏗️ System Architecture

### **Core Components**
1. **Electron Desktop App** (`main.js`) - Cross-platform desktop application
2. **Express Web Server** (`server.js`) - Backend API and web interface
3. **Telegram Bot** (`bot.js`) - Real-time notifications and remote control
4. **Web Interface** (`public/`) - Modern dashboard for monitoring
5. **Data Storage** - JSON files for posts, team members, pages, and configuration

### **Data Flow**
```
Facebook Pages → Puppeteer Scraper → Data Processing → Storage → Web Dashboard
                                                    ↓
                                              Telegram Bot → Notifications
```

## 🔍 Reverse Engineering Capabilities

### **Facebook DOM Analysis**
- **Obfuscated CSS class detection** (`xu06os2.x1ok221b`, `html-div xdj266r x14z9mp`)
- **Dynamic content parsing** as Facebook loads content asynchronously
- **Multiple extraction strategies** with fallback mechanisms
- **Pattern recognition** for engagement metrics in various formats

### **Anti-Detection Techniques**
- **Stealth browser automation** to bypass Facebook's bot detection
- **Randomized user agents** and viewport sizes
- **Human-like interaction patterns** with realistic delays
- **Cookie-based session management** for authenticated access

### **Content Structure Analysis**
- **Video vs Picture post differentiation** with specific handling
- **Timestamp extraction** from complex nested span structures
- **URL structure analysis** and cleaning for optimal access
- **Multi-language support** (Arabic/English) with intelligent detection

## 📦 Installation & Setup

### **Prerequisites**
- Node.js (v14 or higher)
- npm or yarn package manager
- Telegram Bot Token (for notifications)

### **Installation Steps**

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd facebook-telegram-bot
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   Create a `.env` file in the root directory:
   ```env
   TOKEN=your_telegram_bot_token
   CHAT_ID=your_telegram_chat_id
   PORT=3000
   ```

4. **Initial Setup**
   ```bash
   # Start the application
   npm start

   # Or run individual components
   npm run server    # Web server only
   npm run dev       # Development mode with auto-reload
   ```

## ⚙️ Configuration

### **Scraper Settings**
- **Headless Mode**: Enable/disable browser visibility
- **Chunk Size**: Number of pages processed per batch (1-50)
- **Cycle Delays**: Random delays between scraping cycles (1-60 minutes)
- **Search Delays**: Delays between page visits (1-60 seconds)
- **Posts Per Page**: Number of posts to extract per page (1-50)

### **Metrics Configuration**
- **Update Delay**: Time between metrics updates (1-300 seconds)
- **Bulk Processing**: Queue management for large-scale updates
- **Retry Logic**: Automatic retry on failed extractions

### **Auto-Export Settings**
- **Scheduled Exports**: Daily, weekly, or monthly automatic exports
- **Export Formats**: JSON and CSV support
- **Export Time**: Configurable time for automatic exports (24-hour format)

## 🎯 Usage Guide

### **Getting Started**

1. **Launch the Application**
   ```bash
   npm start
   ```
   The desktop app will open with the web dashboard at `http://localhost:3000`

2. **Facebook Authentication**
   - Click "Login to Facebook" in the Admin Panel
   - Complete manual authentication (including CAPTCHAs)
   - Save session for future use

3. **Add Facebook Pages**
   - Navigate to Admin Panel → Pages Management
   - Add Facebook page URLs you want to monitor
   - Assign pages to team members

4. **Configure Team Members**
   - Go to Team Management (`/team.html`)
   - Add team members with names, emails, and roles
   - Assign specific Facebook pages to each member

5. **Start Monitoring**
   - Click "Start Scraper" to begin automated monitoring
   - Monitor progress in real-time via the dashboard
   - Receive Telegram notifications for new posts

### **Team Management**

#### **Adding Team Members**
```javascript
// Example team member structure
{
  "id": "1748727159568",
  "name": "احمد",
  "email": "<EMAIL>",
  "role": "Content Manager",
  "assignedPages": [
    {
      "name": "العربية",
      "link": "https://www.facebook.com/AlHadath/"
    }
  ],
  "createdAt": "2025-05-31T21:32:39.568Z"
}
```

#### **Individual Member Scraping**
- Navigate to team member details
- Click "Scrape Posts" to extract posts for specific member
- Configure number of posts to extract (1-50)
- Monitor progress with real-time updates

### **Metrics Management**

#### **Bulk Metrics Update**
- Click "Update All Metrics" to refresh all post engagement data
- Configure delay between updates (default: 30 seconds)
- Monitor progress with live percentage updates
- Stop process anytime if needed

#### **Individual Post Updates**
- Click "Update Metrics" on any post card
- System automatically detects video vs picture posts
- Updates likes, comments, shares (pictures) or views (videos)
- Extracts and updates post timestamps

## 🤖 Telegram Bot Commands

### **Arabic Interface Commands**
- `عرض الصفحات` - View monitored Facebook pages
- `إضافة صفحة` - Add new Facebook page to monitoring
- `حذف صفحة` - Remove page from monitoring
- `تشغيل المراقبة` - Start automated scraping
- `إيقاف المراقبة` - Stop scraping operations
- `ضبط الوقت` - Configure scraping cycle delays
- `ضبط وقت البحث` - Set search delays between pages
- `إدارة المستخدمين` - Manage authorized bot users

### **User Authorization**
```javascript
// Authorized users are stored in authorized_users.json
[
  "username1",
  "username2",
  "randomiz643"  // Default admin user
]
```

### **Real-time Notifications**
The bot sends formatted messages for new posts:
```
📑 New Post from قناة الحدث Al Hadath
⏰ Posted: 2 minutes ago
🎬 Video Duration: 1:42

Content: ترامبولين فوق الركام وأرجوحة بين الأطلال.. أطفال غزة ينتزعون لحظات فرح

👍 2 | 💬 0 | 🔄 0
```

## 📊 API Documentation

### **Posts Management**
```javascript
// Get all posts
GET /api/posts
Response: Array of post objects with engagement metrics

// Update post metrics
POST /api/posts/update-metrics
Body: { postId: "hash", targetUrl: "facebook_url" }

// Delete post
DELETE /api/posts/delete
Body: { postId: "normalized_hash" }

// Bulk metrics update
POST /api/posts/update-all-metrics
Response: { success: true, totalPosts: 150, estimatedTime: "75 minutes" }
```

### **Team Management**
```javascript
// Get team members
GET /api/team
Response: Array of team member objects

// Add team member
POST /api/team
Body: { name: "Name", email: "<EMAIL>", role: "Manager" }

// Update team member
PUT /api/team/:id
Body: { name: "Updated Name", assignedPages: [...] }

// Get member posts
GET /api/team/:id/posts
Response: Filtered posts for specific team member
```

### **Scraper Control**
```javascript
// Start scraper
POST /api/scraper/start
Response: { success: true, message: "Scraper started" }

// Stop scraper
POST /api/scraper/stop
Response: { success: true, message: "Scraper stopped" }

// Get scraper status
GET /api/status
Response: { isRunning: true, isPaused: false, currentPage: "..." }
```

### **Configuration Management**
```javascript
// Get current configuration
GET /api/config
Response: {
  headlessMode: true,
  chunkSize: 10,
  minLoopDelay: 60000,
  maxLoopDelay: 600000,
  postsPerPage: 10
}

// Update configuration
POST /api/config
Body: { headlessMode: false, chunkSize: 15, ... }
```

## 📈 Data Structures

### **Post Object Structure**
```javascript
{
  "pageUrl": "https://www.facebook.com/AlHadath/",
  "normalizedTextHash": "قناة الحدث Al Hadath ترامبولين فوق الركام",
  "pageName": "قناة الحدث Al Hadath",
  "postTime": "1m",
  "finalFilteredText": "ترامبولين فوق الركام وأرجوحة بين الأطلال..",
  "timestamp": "2025-06-08T13:10:08.148Z",
  "postUrl": "https://www.facebook.com/AlHadath/videos/642269288847248/",
  "customUrl": "https://custom-tracking-url.com", // Optional
  "mediaUrls": ["https://scontent.fbgw62-1.fna.fbcdn.net/..."],
  "videoDuration": "0:00 / 1:42",
  "isLive": false,
  "engagement": {
    "likes": 2,
    "comments": 0,
    "shares": 0,
    "views": 1500  // Video posts only
  },
  "lastMetricsUpdate": "2025-06-08T13:11:49.194Z"
}
```

### **Team Member Structure**
```javascript
{
  "id": "1748727159568",
  "name": "احمد",
  "email": "<EMAIL>",
  "role": "Content Manager",
  "assignedPages": [
    {
      "name": "العربية",
      "link": "https://www.facebook.com/AlHadath/"
    }
  ],
  "createdAt": "2025-05-31T21:32:39.568Z",
  "updatedAt": "2025-06-08T11:49:09.904Z"
}
```

## 📤 Export & Reporting

### **Export Formats**

#### **JSON Export**
```javascript
{
  "teamMember": {
    "id": "1748727159568",
    "name": "احمد",
    "email": "<EMAIL>",
    "role": "Content Manager",
    "assignedPages": [...]
  },
  "exportInfo": {
    "exportDate": "2025-06-08T13:20:00.000Z",
    "exportVersion": "2.0",
    "dataSource": "Facebook Posts Scraper"
  },
  "analytics": {
    "totalPosts": 45,
    "totalEngagement": {
      "likes": 15420,
      "comments": 892,
      "shares": 234,
      "views": 45600
    },
    "averageEngagement": 362.3,
    "topPerformingPost": {...},
    "recentPosts": 12,
    "averageEngagementRate": 4.2
  },
  "posts": [...]
}
```

#### **CSV Export**
Includes columns: Post ID, Text, Page Name, Page URL, Post URL, Custom URL, Timestamp, Post Time, Video Duration, Is Live, Likes, Comments, Shares, Views, Total Engagement, Last Metrics Update

### **Automated Exports**
- **Daily**: Exports at specified time every day
- **Weekly**: Exports every Sunday at specified time
- **Monthly**: Exports on the 1st day of each month
- **Manual**: Trigger immediate export via dashboard or API

## 🔧 Advanced Features

### **Browser Management**
- **Multiple browser instances** for parallel processing
- **Session persistence** with cookie management
- **Automatic browser cleanup** on application exit
- **Debug mode** with visible browser windows

### **Error Handling**
- **Automatic retry logic** for failed extractions
- **Graceful degradation** when Facebook changes structure
- **Comprehensive logging** for debugging and monitoring
- **Fallback extraction methods** for reliability

### **Performance Optimization**
- **Chunk-based processing** to handle large page lists
- **Queue management** for bulk operations
- **Memory management** with browser instance recycling
- **Configurable delays** to respect rate limits

## 🛡️ Security & Ethics

### **Ethical Usage**
- **Legitimate business use** - monitoring your own team's content
- **Public data only** - no private or unauthorized access
- **Rate limiting** - respects Facebook's servers with configurable delays
- **Transparent operations** - all activities are logged and auditable

### **Security Features**
- **User authorization** system for Telegram bot access
- **Session management** with secure cookie storage
- **Input validation** and sanitization
- **Error handling** without exposing sensitive information

## 🐛 Troubleshooting

### **Common Issues**

#### **Facebook Authentication Failed**
```bash
# Solution: Clear saved session and re-authenticate
1. Click "Clear Saved Session" in Admin Panel
2. Click "Login to Facebook"
3. Complete manual authentication including CAPTCHAs
4. Click "Save Session & Close Browser"
```

#### **Scraper Not Finding Posts**
```bash
# Check configuration:
1. Verify Facebook page URLs are correct
2. Ensure headless mode is appropriate for your setup
3. Check if Facebook has changed their DOM structure
4. Review browser console for JavaScript errors
```

#### **Metrics Update Failing**
```bash
# Troubleshooting steps:
1. Verify post URLs are accessible
2. Check if Facebook requires authentication
3. Try updating individual posts first
4. Review metrics extraction logs
```

#### **Telegram Bot Not Responding**
```bash
# Verify configuration:
1. Check TOKEN and CHAT_ID in .env file
2. Ensure bot is added to the correct chat
3. Verify user is in authorized_users.json
4. Check bot permissions in Telegram
```

### **Debug Mode**
Enable debug mode by setting `metricsHeadlessMode: false` to see browser windows during operation.

## 📁 File Structure

```
facebook-telegram-bot/
├── main.js                 # Electron main process
├── server.js              # Express web server & API
├── bot.js                 # Telegram bot integration
├── preload.js             # Electron preload script
├── package.json           # Dependencies and scripts
├── .env                   # Environment variables
├── public/                # Web interface files
│   ├── index.html         # Main dashboard
│   ├── team.html          # Team management page
│   ├── app.js             # Frontend JavaScript
│   ├── team.js            # Team management JS
│   └── styles.css         # Styling
├── exports/               # Generated export files
├── error_screenshots/     # Debug screenshots
├── elements/              # HTML element templates
├── final_filtered_posts.json    # Main posts database
├── team_members.json      # Team member data
├── pages.json             # Monitored Facebook pages
├── automation_config.json # Scraper configuration
├── facebook_cookies.json  # Saved Facebook session
└── authorized_users.json  # Telegram bot users
```

## 🔄 Updates & Maintenance

### **Regular Maintenance**
- **Monitor Facebook DOM changes** - Update selectors if extraction fails
- **Review export files** - Ensure data quality and completeness
- **Update dependencies** - Keep packages current for security
- **Clean old data** - Archive or remove outdated posts as needed

### **Backup Recommendations**
- **Export data regularly** - Use built-in export functionality
- **Backup configuration files** - Save team_members.json, pages.json, etc.
- **Document custom modifications** - Keep track of any code changes

## 📞 Support & Contributing

### **Getting Help**
- Review this README for comprehensive documentation
- Check the troubleshooting section for common issues
- Enable debug mode to investigate browser-related problems
- Review application logs for detailed error information

### **Contributing**
- Follow existing code style and patterns
- Test thoroughly with various Facebook page types
- Document any new features or configuration options
- Consider backward compatibility when making changes

## 📄 License

MIT License - See LICENSE file for details

---

**Built with ❤️ for marketing team performance monitoring and analytics**