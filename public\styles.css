:root {
  --primary-color: #4267B2;
  --secondary-color: #E9EBEE;
  --accent-color: #1877F2;
  --text-color: #1C1E21;
  --light-text: #65676B;
  --white: #FFFFFF;
  --dark-bg: #18191A;
  --dark-card: #242526;
  --dark-secondary: #3A3B3C;
  --dark-text: #E4E6EB;
  --dark-light-text: #B0B3B8;
  --shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
  --danger: #e74c3c;
  --success: #2ecc71;
}

/* Left Sidebar Navigation */
.left-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 250px;
  height: 100vh;
  background: var(--white);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.dark-mode .left-sidebar {
  background: var(--dark-card);
  border-right-color: rgba(255, 255, 255, 0.1);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  font-size: 18px;
  color: var(--primary-color);
}

.dark-mode .sidebar-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text);
}

.sidebar-header i {
  font-size: 24px;
}

.sidebar-menu {
  list-style: none;
  padding: 20px 0;
  margin: 0;
  flex: 1;
}

.sidebar-menu li {
  margin: 0;
}

.sidebar-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  color: var(--text-color);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.dark-mode .sidebar-link {
  color: var(--dark-text);
}

.sidebar-link:hover {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
}

.sidebar-link.active {
  background: rgba(59, 130, 246, 0.15);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
  font-weight: 600;
}

.sidebar-link i {
  font-size: 18px;
  width: 20px;
  text-align: center;
}

/* Responsive design for sidebar */
@media (max-width: 768px) {
  .left-sidebar {
    width: 200px;
  }
}

@media (max-width: 640px) {
  .left-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .left-sidebar.open {
    transform: translateX(0);
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: var(--secondary-color);
  color: var(--text-color);
  transition: var(--transition);
}

body.dark-mode {
  background-color: var(--dark-bg);
  color: var(--dark-text);
}

.app-container {
  max-width: none;
  width: calc(100vw - 270px);
  margin: 0;
  padding: 0 1.5rem;
  box-sizing: border-box;
}

/* Header */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  gap: 0.75rem;
  min-height: 60px;
}

.dark-mode header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  flex-shrink: 0;
}

.logo i {
  color: var(--primary-color);
  font-size: 1.4rem;
}

.logo h1 {
  font-size: 1.1rem;
  font-weight: 600;
  white-space: nowrap;
}

/* Header Controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: nowrap;
  margin-left: auto;
  flex-shrink: 0;
}

.theme-toggle {
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  background-color: var(--secondary-color);
  transition: var(--transition);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.dark-mode .theme-toggle {
  background-color: var(--dark-secondary);
}

.theme-toggle i {
  font-size: 1.2rem;
}

/* Scraper Controls */
.scraper-controls {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  flex-wrap: wrap;
  margin-right: auto;
}

.scraper-controls .btn {
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
  white-space: nowrap;
}

/* Make header buttons more compact */
.header-controls > .btn {
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
  white-space: nowrap;
}

/* Filter Bar */
.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1.5rem 0;
  flex-wrap: wrap;
  gap: 1rem;
}

/* Metrics Controls */
.metrics-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.metrics-controls .btn {
  font-size: 0.9rem;
  padding: 0.4rem 0.8rem;
}



/* Export Dropdown */
.export-dropdown {
  position: relative;
  display: inline-block;
}

.export-options {
  display: none;
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--light-bg);
  border: 1px solid var(--light-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 180px;
  margin-top: 2px;
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.2s ease, transform 0.2s ease;
  pointer-events: none;
}

.dark-mode .export-options {
  background: var(--dark-card-bg);
  border-color: var(--dark-border);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.export-dropdown.active .export-options {
  display: block;
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* Add a small invisible bridge to prevent dropdown from disappearing */
.export-dropdown::after {
  content: '';
  position: absolute;
  top: 100%;
  right: 0;
  width: 100%;
  height: 6px;
  background: transparent;
  z-index: 999;
}

.export-option {
  display: block;
  width: 100%;
  padding: 0.6rem 1rem;
  border: none;
  background: transparent;
  color: var(--light-text);
  text-align: left;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.dark-mode .export-option {
  color: var(--dark-text);
}

.export-option:hover {
  background: var(--light-hover);
}

.dark-mode .export-option:hover {
  background: var(--dark-hover);
}

.export-option:first-child {
  border-radius: 8px 8px 0 0;
}

.export-option:last-child {
  border-radius: 0 0 8px 8px;
}

.export-option i {
  margin-right: 0.5rem;
  width: 16px;
}

.search-container {
  display: flex;
  align-items: center;
  background-color: var(--white);
  border-radius: 20px;
  padding: 0.5rem 1rem;
  width: 300px;
  max-width: 100%;
  box-shadow: var(--shadow);
}

.dark-mode .search-container {
  background-color: var(--dark-card);
}

.search-container i {
  color: var(--light-text);
  margin-right: 0.5rem;
}

.dark-mode .search-container i {
  color: var(--dark-light-text);
}

#search-input {
  border: none;
  outline: none;
  background: transparent;
  width: 100%;
  color: var(--text-color);
}

.dark-mode #search-input {
  color: var(--dark-text);
}

#search-input::placeholder {
  color: var(--light-text);
}

.dark-mode #search-input::placeholder {
  color: var(--dark-light-text);
}

.filter-buttons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-buttons button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 20px;
  background-color: var(--white);
  color: var(--text-color);
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  box-shadow: var(--shadow);
}

.dark-mode .filter-buttons button {
  background-color: var(--dark-card);
  color: var(--dark-text);
}

.filter-buttons button.active {
  background-color: var(--accent-color);
  color: var(--white);
}

/* Team Filter Dropdown */
.team-filter-dropdown {
  position: relative;
  display: inline-block;
}

.team-dropdown-trigger {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 20px;
  background-color: var(--white);
  color: var(--text-color);
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 140px;
  justify-content: space-between;
}

.dark-mode .team-dropdown-trigger {
  background-color: var(--dark-card);
  color: var(--dark-text);
}

.team-dropdown-trigger:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.dark-mode .team-dropdown-trigger:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.team-dropdown-trigger.active {
  background-color: var(--accent-color);
  color: var(--white);
}

.dropdown-arrow {
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.team-dropdown-trigger.open .dropdown-arrow {
  transform: rotate(180deg);
}

.team-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--white);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 0.5rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  max-height: 300px;
  overflow-y: auto;
}

.dark-mode .team-dropdown-menu {
  background: var(--dark-card);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.team-dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.team-dropdown-option {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: var(--text-color);
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 0.9rem;
  border-radius: 0;
}

.dark-mode .team-dropdown-option {
  color: var(--dark-text);
}

.team-dropdown-option:hover {
  background-color: rgba(66, 103, 178, 0.1);
}

.dark-mode .team-dropdown-option:hover {
  background-color: rgba(66, 103, 178, 0.2);
}

.team-dropdown-option.active {
  background-color: var(--accent-color);
  color: var(--white);
}

.team-dropdown-option:first-child {
  border-radius: 12px 12px 0 0;
}

.team-dropdown-option:last-child {
  border-radius: 0 0 12px 12px;
}

.team-dropdown-option:only-child {
  border-radius: 12px;
}

/* Custom scrollbar for dropdown */
.team-dropdown-menu::-webkit-scrollbar {
  width: 6px;
}

.team-dropdown-menu::-webkit-scrollbar-track {
  background: transparent;
}

.team-dropdown-menu::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.dark-mode .team-dropdown-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

/* Posts Container */
.posts-container {
  margin: 1.5rem 0 2rem;
  width: 100%;
  box-sizing: border-box;
}

/* Posts Grid */
.posts-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.25rem;
  margin: 1.5rem 0;
  width: 100%;
  box-sizing: border-box;
}

.loading {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

.dark-mode .spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left-color: var(--primary-color);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Post Card */
.post-card {
  background-color: var(--white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  transform: translateY(0);
  margin-bottom: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .post-card {
  background-color: var(--dark-card);
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.post-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.dark-mode .post-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

/* Post header updates */
.post-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .post-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.avatar-container {
  margin-right: 12px;
}

.source-avatar {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.header-content {
  flex: 1;
}

.header-top-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

/* Post time row for aligning badge with time */
.post-time-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Align badge with post time */
.post-header .link-updated-badge {
  margin-left: 0;
  margin-top: 0;
}

.page-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-color);
}

.dark-mode .page-name {
  color: var(--dark-text);
}

.post-original-time {
  font-size: 0.85rem;
  color: var(--light-text);
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.dark-mode .post-original-time {
  color: var(--dark-light-text);
  background-color: rgba(255, 255, 255, 0.1);
}

.post-time {
  font-size: 0.75rem;
  color: var(--light-text);
  display: block;
}

.dark-mode .post-time {
  color: var(--dark-light-text);
}

/* Team member label styles */
.team-member-label {
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: #e3f2fd;
  color: #1976d2;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid #bbdefb;
}

.dark-mode .team-member-label {
  background-color: rgba(25, 118, 210, 0.2);
  color: #90caf9;
  border-color: rgba(25, 118, 210, 0.3);
}

/* Video information styles */
.video-duration {
  display: inline-block;
  margin-bottom: 12px;
  padding: 4px 10px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6px;
  font-size: 0.8rem;
  color: var(--light-text);
  font-weight: 500;
}

.dark-mode .video-duration {
  background: rgba(255, 255, 255, 0.1);
  color: var(--dark-light-text);
}

.video-duration i {
  margin-right: 5px;
  color: #FF0000;
}

.live-badge {
  display: inline-block;
  margin-bottom: 12px;
  padding: 4px 10px;
  background: rgba(220, 20, 60, 0.1);
  border-radius: 6px;
  font-size: 0.8rem;
  color: crimson;
  font-weight: bold;
}

.dark-mode .live-badge {
  background: rgba(220, 20, 60, 0.2);
}

.live-icon {
  font-size: 0.7rem;
  margin-right: 5px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

/* Link Updated Badge */
.link-updated-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #28a745, #20c997);
  border: none;
  border-radius: 50%;
  color: white;
  margin-left: 8px;
  cursor: help;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.link-updated-badge::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  transition: all 0.5s ease;
  opacity: 0;
}

.link-updated-badge:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.link-updated-badge:hover::before {
  opacity: 1;
  animation: shimmer 0.6s ease-in-out;
}

.dark-mode .link-updated-badge {
  background: linear-gradient(135deg, #4caf50, #66bb6a);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
}

.dark-mode .link-updated-badge:hover {
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.5);
}

.link-updated-badge i {
  font-size: 0.7rem;
  z-index: 1;
  position: relative;
}

/* Shimmer animation for the badge */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

/* Animation for the badge when it appears */
@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.post-actions i {
  cursor: pointer;
  color: var(--light-text);
}

.dark-mode .post-actions i {
  color: var(--dark-light-text);
}

/* Post Content */
.post-content {
  padding: 1rem;
  font-size: 0.95rem;
  line-height: 1.6;
}

.post-content p {
  margin-bottom: 1rem;
  white-space: pre-line;
}

/* Engagement metrics */
.post-engagement {
  display: flex;
  padding: 0.5rem 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  font-size: 0.9rem;
  color: var(--light-text);
  gap: 1rem;
}

.dark-mode .post-engagement {
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  color: var(--dark-light-text);
}

.engagement-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.engagement-item i {
  font-size: 1rem;
}

.engagement-item.likes i {
  color: #4267B2; /* Facebook blue */
}

.engagement-item.comments i {
  color: #65676B; /* Gray */
}

.engagement-item.shares i {
  color: #45BD62; /* Facebook green */
}

.dark-mode .engagement-item.likes i {
  color: #5890ff; /* Lighter blue for dark mode */
}

.dark-mode .engagement-item.comments i {
  color: #B0B3B8; /* Lighter gray for dark mode */
}

.dark-mode .engagement-item.shares i {
  color: #79e092; /* Lighter green for dark mode */
}

/* Post Footer */
.post-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0.8rem 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background-color: rgba(0, 0, 0, 0.02);
}

.dark-mode .post-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  background-color: rgba(255, 255, 255, 0.02);
}

.view-source-btn {
  color: var(--accent-color);
  text-decoration: none;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.2s ease;
  background-color: rgba(24, 119, 242, 0.08);
}

.view-source-btn:hover {
  background-color: rgba(24, 119, 242, 0.15);
}

.dark-mode .view-source-btn {
  background-color: rgba(24, 119, 242, 0.15);
}

.dark-mode .view-source-btn:hover {
  background-color: rgba(24, 119, 242, 0.25);
}

/* Change Link Button */
.change-link-btn {
  color: var(--light-text);
  background: none;
  border: none;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.2s ease;
  cursor: pointer;
  margin-left: 0.5rem;
}

.change-link-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-color);
}

.dark-mode .change-link-btn {
  color: var(--dark-light-text);
}

.dark-mode .change-link-btn:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--dark-text);
}

/* Update Metrics Button */
.update-metrics-btn {
  color: var(--light-text);
  background: none;
  border: none;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.2s ease;
  cursor: pointer;
  margin-left: 0.5rem;
}

.update-metrics-btn:hover {
  background-color: rgba(0, 150, 0, 0.1);
  color: #28a745;
}

.update-metrics-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.update-metrics-btn.loading {
  color: #007bff;
}

.dark-mode .update-metrics-btn {
  color: var(--dark-light-text);
}

.dark-mode .update-metrics-btn:hover {
  background-color: rgba(0, 150, 0, 0.1);
  color: #28a745;
}

/* Delete Post Button */
.delete-post-btn {
  color: var(--light-text);
  background: none;
  border: none;
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.3rem;
  padding: 5px 10px;
  border-radius: 5px;
  transition: all 0.2s ease;
  cursor: pointer;
  margin-left: 0.5rem;
}

.delete-post-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.dark-mode .delete-post-btn {
  color: var(--dark-light-text);
}

.dark-mode .delete-post-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

/* Pulse animation for updated metrics */
@keyframes pulse {
  0% {
    transform: scale(1);
    background-color: rgba(40, 167, 69, 0.1);
  }
  50% {
    transform: scale(1.02);
    background-color: rgba(40, 167, 69, 0.2);
  }
  100% {
    transform: scale(1);
    background-color: transparent;
  }
}

/* Footer */
footer {
  text-align: center;
  padding: 1.5rem 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  color: var(--light-text);
  font-size: 0.9rem;
}

.dark-mode footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--dark-light-text);
}

/* Responsiveness */
@media (max-width: 1600px) {
  .posts-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1200px) {
  .posts-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .app-container {
    width: calc(100vw - 260px);
    padding: 0 1rem;
  }
}

@media (max-width: 900px) {
  .app-container {
    width: calc(100vw - 250px);
    padding: 0 0.75rem;
  }

  .posts-grid {
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .app-container {
    width: calc(100vw - 220px);
    padding: 0 0.5rem;
  }

  .filter-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    width: 100%;
  }

  .posts-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

@media (max-width: 640px) {
  .app-container {
    width: 100vw;
    margin-left: 0;
    padding: 0 0.5rem;
  }
}

  /* Header responsive adjustments */
  header {
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.75rem 0;
  }

  .header-controls {
    gap: 0.5rem;
    justify-content: flex-end;
    width: 100%;
    margin-top: 0.5rem;
  }

  .scraper-controls {
    gap: 0.25rem;
  }

  .scraper-controls .btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }

  .logo h1 {
    font-size: 1rem;
  }

  .logo i {
    font-size: 1.2rem;
  }
}

/* Animation for new posts */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.new-post {
  animation: fadeIn 0.5s ease-out;
}

/* Notification */
.notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1000;
}

.notification.show {
  transform: translateY(0);
  opacity: 1;
}

.notification-content {
  background-color: var(--accent-color);
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification-content i {
  font-size: 1.2rem;
}

/* Error and no results messages */
.error-message,
.no-results {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
  color: var(--light-text);
  text-align: center;
}

.dark-mode .error-message,
.dark-mode .no-results {
  color: var(--dark-light-text);
}

.error-message i,
.no-results i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.7;
}

/* Header updates - removed duplicate, using the one defined earlier */

.scraper-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* Button styles */
.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: var(--transition);
}

.btn.primary {
  background-color: var(--accent-color);
  color: white;
}

.btn.primary:hover {
  background-color: #0b5ed7;
}

.btn.danger {
  background-color: #dc3545;
  color: white;
}

.btn.danger:hover {
  background-color: #bb2d3b;
}

.btn.warning {
  background-color: #ffc107;
  color: #212529;
}

.btn.warning:hover {
  background-color: #e0a800;
}

.btn.success {
  background-color: #28a745;
  color: white;
}

.btn.success:hover {
  background-color: #218838;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.full-width {
  width: 100%;
  margin: 1rem 0;
}

.admin-btn {
  margin-right: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dark-mode .admin-btn {
  background-color: var(--dark-secondary);
  color: var(--dark-text);
}

/* Status bar */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 1rem;
  background-color: var(--white);
  border-radius: 8px;
  margin: 1rem 0;
  box-shadow: var(--shadow);
  flex-wrap: wrap;
  gap: 0.8rem;
}

.dark-mode .status-bar {
  background-color: var(--dark-card);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.status-dot.online {
  background-color: #28a745;
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
}

.status-dot.offline {
  background-color: #6c757d;
}

.status-dot.error {
  background-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}

.status-dot.paused {
  background-color: #ffc107;
  box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.2);
}

.status-text {
  font-size: 0.85rem;
  font-weight: 500;
}

/* Navigation control */
.navigation-control {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
  max-width: 450px;
}

.navigation-control.hidden {
  display: none;
}

.navigate-input {
  flex: 1;
  padding: 0.5rem;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  background-color: var(--white);
  min-width: 250px;
}

.dark-mode .navigate-input {
  background-color: var(--dark-secondary);
  color: var(--dark-text);
  border-color: rgba(255, 255, 255, 0.2);
}

/* Enhanced Progress bar - Multiple Operations Support */
.progress-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: 500px;
  margin: 0 1rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 12px;
  border-radius: 6px;
  backdrop-filter: blur(5px);
  gap: 6px;
}

.progress-container.hidden {
  display: none;
}

.progress-operations-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.progress-operation-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-height: 24px;
}

.progress-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 120px;
}

.progress-operation {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-color);
}

.progress-stats {
  font-size: 0.7rem;
  color: var(--light-text);
}

.progress-bar {
  height: 6px;
  background-color: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  flex: 1;
  min-width: 100px;
}

.dark-mode .progress-bar {
  background-color: var(--dark-secondary);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4285f4, #34a853);
  transition: width 0.4s ease;
}

.progress-text {
  font-size: 0.75rem;
  min-width: 36px;
  text-align: right;
  font-weight: 600;
  color: var(--accent-color);
}

/* Dark mode support */
.dark-mode .progress-container {
  background: rgba(36, 37, 38, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-mode .progress-operation {
  color: var(--dark-text);
}

.dark-mode .progress-stats {
  color: var(--dark-light-text);
}

.dark-mode .progress-text {
  color: var(--accent-color);
}

.dark-mode .progress-operation-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-mode .progress-operation-item:last-child {
  border-bottom: none;
}

/* Next cycle */
.next-cycle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
}

.next-cycle.hidden {
  display: none;
}

.next-cycle i {
  color: var(--accent-color);
}

/* Add responsive adjustments */
@media (max-width: 768px) {
  /* Header controls already handled in main responsive section */
  
  .status-bar {
    flex-direction: column;
    gap: 0.8rem;
    align-items: flex-start;
  }
  
  .progress-container {
    width: 100%;
    max-width: none;
    margin: 0;
  }
  
  .next-cycle {
    width: 100%;
    justify-content: flex-end;
  }
}

/* Notification updates */
.notification.error .notification-content {
  background-color: #dc3545;
}

.notification.warning .notification-content {
  background-color: #ffc107;
  color: #343a40;
}

.notification.success .notification-content {
  background-color: #28a745;
}

/* Post images styles */
.post-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.post-image {
  width: calc(50% - 4px);
  max-height: 200px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
}

.post-image:hover {
  transform: scale(1.02);
}

.more-images {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(50% - 4px);
  height: 100px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 8px;
  font-weight: bold;
}

/* Image Modal */
.image-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  overflow: auto;
}

.modal-content {
  display: block;
  margin: auto;
  max-width: 90%;
  max-height: 90%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.close-modal {
  position: absolute;
  top: 20px;
  right: 30px;
  color: #f1f1f1;
  font-size: 40px;
  font-weight: bold;
  cursor: pointer;
  z-index: 1001;
}

/* Dark mode adjustments for images */
.dark-mode .post-image {
  border: 1px solid #444;
}

.dark-mode .more-images {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Admin Sidebar */
.admin-sidebar {
  position: fixed;
  top: 0;
  right: -350px;
  width: 350px;
  height: 100vh;
  background-color: var(--white);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
  transition: right 0.3s ease;
}

.dark-mode .admin-sidebar {
  background-color: var(--dark-card);
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
}

.admin-sidebar.open {
  right: 0;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-mode .sidebar-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.sidebar-close-btn {
  background: transparent;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--text-color);
}

.dark-mode .sidebar-close-btn {
  color: var(--dark-text);
}

.sidebar-content {
  padding: 1rem;
}

.sidebar-content h4 {
  margin: 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-mode .sidebar-content h4 {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-control {
  margin-bottom: 1rem;
}

.admin-control label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.admin-control input[type="number"],
.admin-control input[type="text"] {
  width: 100%;
  padding: 0.5rem;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  background-color: var(--white);
}

.dark-mode .admin-control input[type="number"],
.dark-mode .admin-control input[type="text"] {
  background-color: var(--dark-secondary);
  color: var(--dark-text);
  border-color: rgba(255, 255, 255, 0.2);
}

.range-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Auto-export specific styles */
.admin-control input[type="time"],
.admin-control select {
  width: 100%;
  padding: 0.5rem;
  border-radius: 5px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  background-color: var(--white);
  color: var(--text-color);
}

.dark-mode .admin-control input[type="time"],
.dark-mode .admin-control select {
  background-color: var(--dark-secondary);
  color: var(--dark-text);
  border-color: rgba(255, 255, 255, 0.2);
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: normal;
}

.checkbox-item input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.status-text {
  font-weight: 500;
  color: var(--accent-color);
}

.dark-mode .status-text {
  color: var(--accent-color);
}

.btn.secondary {
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-mode .btn.secondary {
  background-color: var(--dark-secondary);
  color: var(--dark-text);
  border-color: rgba(255, 255, 255, 0.1);
}

.btn.secondary:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark-mode .btn.secondary:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.range-inputs input {
  width: 100%;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 26px;
  margin-right: 8px;
}

.toggle-switch input {
  opacity: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  margin: 0;
  padding: 0;
  cursor: pointer;
}

.toggle-slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
  z-index: 1;
  pointer-events: none;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
  z-index: 2;
  pointer-events: none;
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:focus + .toggle-slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-label {
  font-size: 0.85rem;
  font-weight: 500;
}

.toggle-label.enabled {
  color: var(--primary-color);
}

.toggle-label.disabled {
  color: var(--light-text);
}

.dark-mode .toggle-label.enabled {
  color: #5c81c9;
}

.dark-mode .toggle-label.disabled {
  color: var(--dark-light-text);
}

.toggle-help {
  color: var(--light-text);
  cursor: help;
}

.dark-mode .toggle-help {
  color: var(--dark-light-text);
}

/* Pages List */
.pages-list {
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  margin: 1rem 0;
  padding: 0.5rem;
}

.dark-mode .pages-list {
  border-color: rgba(255, 255, 255, 0.1);
}

.loading-pages {
  padding: 1rem;
  text-align: center;
  color: var(--light-text);
}

.dark-mode .loading-pages {
  color: var(--dark-light-text);
}

.page-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .page-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.page-item:last-child {
  border-bottom: none;
}

.page-info {
  overflow: hidden;
}

.page-info .page-name {
  font-weight: 500;
  font-size: 0.9rem;
}

.page-info .page-url {
  font-size: 0.8rem;
  color: var(--light-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark-mode .page-info .page-url {
  color: var(--dark-light-text);
}

.delete-page {
  padding: 0.3rem;
  min-width: auto;
  color: var(--danger);
  background-color: transparent;
}

.delete-page:hover {
  background-color: rgba(231, 76, 60, 0.1);
}

.dark-mode .delete-page {
  color: #e74c3c;
}

.dark-mode .delete-page:hover {
  background-color: rgba(231, 76, 60, 0.2);
}

.add-page-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Overlay when sidebar is open */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: none;
}

.sidebar-overlay.show {
  display: block;
}

/* Mobile responsiveness for sidebar */
@media (max-width: 576px) {
  .admin-sidebar {
    width: 300px;
  }
}

/* Facebook Authentication Styles */
.auth-status {
  margin-bottom: 1rem;
  padding: 0.75rem;
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.05);
  font-size: 0.9rem;
  line-height: 1.4;
}

.dark-mode .auth-status {
  background-color: rgba(255, 255, 255, 0.05);
}

.auth-status.authenticated {
  background-color: rgba(46, 204, 113, 0.1);
  color: var(--success);
}

.dark-mode .auth-status.authenticated {
  background-color: rgba(46, 204, 113, 0.2);
}

.auth-status.not-authenticated {
  background-color: rgba(231, 76, 60, 0.1);
  color: var(--danger);
}

.dark-mode .auth-status.not-authenticated {
  background-color: rgba(231, 76, 60, 0.2);
}

.auth-status i {
  margin-right: 5px;
}

.auth-info {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  opacity: 0.8;
  color: var(--light-text);
}

.dark-mode .auth-info {
  color: var(--dark-text);
}

.auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.auth-buttons button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
}

.auth-buttons button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auth-note {
  font-size: 0.8rem;
  color: var(--light-secondary);
  margin-top: 0.5rem;
  padding: 0.5rem;
  border-left: 3px solid var(--light-border);
  background-color: rgba(0, 0, 0, 0.02);
}

.dark-mode .auth-note {
  color: var(--dark-secondary);
  border-left-color: var(--dark-border);
  background-color: rgba(255, 255, 255, 0.02);
}

.btn.success {
  background-color: var(--success);
  color: white;
}

.btn.success:hover {
  background-color: #27ae60;
}

.btn.success:disabled {
  background-color: rgba(46, 204, 113, 0.5);
}

/* Change Link Modal */
.change-link-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.change-link-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.change-link-modal .modal-content {
  background-color: var(--white);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.dark-mode .change-link-modal .modal-content {
  background-color: var(--dark-card);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-mode .modal-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
  margin: 0;
  color: var(--text-color);
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dark-mode .modal-header h3 {
  color: var(--dark-text);
}

.modal-header .close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--light-text);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-header .close-modal:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-color);
}

.dark-mode .modal-header .close-modal {
  color: var(--dark-light-text);
}

.dark-mode .modal-header .close-modal:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--dark-text);
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.dark-mode .form-group label {
  color: var(--dark-text);
}

.readonly-input,
.url-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  background-color: var(--white);
  color: var(--text-color);
}

.dark-mode .readonly-input,
.dark-mode .url-input {
  background-color: var(--dark-secondary);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text);
}

.readonly-input {
  background-color: rgba(0, 0, 0, 0.05);
  cursor: not-allowed;
}

.dark-mode .readonly-input {
  background-color: rgba(255, 255, 255, 0.05);
}

.url-input:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(24, 119, 242, 0.1);
}

.help-text {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.8rem;
  color: var(--light-text);
}

.dark-mode .help-text {
  color: var(--dark-light-text);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-mode .modal-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn.secondary {
  background-color: var(--secondary-color);
  color: var(--text-color);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.btn.secondary:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark-mode .btn.secondary {
  background-color: var(--dark-secondary);
  color: var(--dark-text);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .btn.secondary:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Delete Post Modal */
.delete-post-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.delete-post-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-post-modal .modal-content {
  background-color: var(--white);
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.dark-mode .delete-post-modal .modal-content {
  background-color: var(--dark-card);
}

.delete-post-modal .modal-header h3 {
  color: #dc3545;
}

.warning-message {
  background-color: rgba(220, 53, 69, 0.1);
  border: 1px solid rgba(220, 53, 69, 0.2);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  text-align: center;
}

.warning-message i {
  color: #dc3545;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  display: block;
}

.warning-message p {
  margin: 0.5rem 0;
  color: var(--text-color);
}

.dark-mode .warning-message p {
  color: var(--dark-text);
}

.warning-message strong {
  color: #dc3545;
}

.post-preview {
  margin-top: 1rem;
}

.preview-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.dark-mode .preview-label {
  color: var(--dark-text);
}

.preview-content {
  background-color: var(--bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  max-height: 200px;
  overflow-y: auto;
  font-size: 0.9rem;
  color: var(--light-text);
}

.dark-mode .preview-content {
  background-color: var(--dark-secondary);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-light-text);
}

.preview-post-header {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 0.5rem;
}

.dark-mode .preview-post-header {
  color: var(--dark-text);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.preview-post-text {
  color: var(--light-text);
  line-height: 1.4;
}

.dark-mode .preview-post-text {
  color: var(--dark-light-text);
}

/* Metrics Updated Indicator Styling */
.metrics-updated-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 0.75em;
  color: #17a2b8;
  background: rgba(23, 162, 184, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  border: 1px solid rgba(23, 162, 184, 0.2);
}

.metrics-updated-indicator .time-ago {
  font-weight: 500;
  white-space: nowrap;
}

.metrics-updated-indicator i {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Dark mode styling for metrics indicator */
.dark-mode .metrics-updated-indicator {
  background: rgba(23, 162, 184, 0.15);
  border-color: rgba(23, 162, 184, 0.3);
}

/* Dynamic time indicator enhancements */
.metrics-updated-indicator.never-updated {
  background: rgba(108, 117, 125, 0.1);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.2);
}

.dark-mode .metrics-updated-indicator.never-updated {
  background: rgba(108, 117, 125, 0.15);
  color: #adb5bd;
  border-color: rgba(108, 117, 125, 0.3);
}

.dynamic-time {
  transition: all 0.3s ease;
}

.dynamic-time-text {
  transition: color 0.3s ease, font-weight 0.3s ease;
}

.dynamic-time:hover {
  transform: scale(1.05);
}

.metrics-updated-indicator {
  transition: all 0.3s ease;
}

/* Notification System */
.notification-container {
  position: relative;
  display: inline-block;
}

.notification-bell {
  position: relative;
  background: none;
  border: none;
  font-size: 1.2rem;
  color: var(--text-color);
  cursor: pointer;
  padding: 10px;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
}

.dark-mode .notification-bell {
  color: var(--dark-text);
}

.notification-bell:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(1.05);
}

.dark-mode .notification-bell:hover {
  background: rgba(255, 255, 255, 0.05);
}

.notification-bell.has-notifications {
  color: var(--primary-color);
  animation: bellShake 0.5s ease-in-out;
}

.notification-badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  font-size: 0.7rem;
  font-weight: bold;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--white);
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
}

.dark-mode .notification-badge {
  border-color: var(--dark-bg);
}

.notification-badge.show {
  opacity: 1;
  transform: scale(1);
}

.notification-badge.pulse {
  animation: badgePulse 1s ease-in-out infinite;
}

@keyframes bellShake {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-10deg); }
  75% { transform: rotate(10deg); }
}

@keyframes badgePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.notification-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--white);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  width: 380px;
  max-height: 500px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  overflow: hidden;
}

.dark-mode .notification-dropdown {
  background: var(--dark-card);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.notification-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.notification-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--secondary-color);
}

.dark-mode .notification-header {
  background: var(--dark-secondary);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.notification-header h3 {
  margin: 0;
  font-size: 1rem;
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.dark-mode .notification-header h3 {
  color: var(--dark-text);
}

.notification-actions {
  display: flex;
  gap: 8px;
}

.btn-clear-all,
.btn-close-dropdown {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 6px;
  cursor: pointer;
  color: var(--light-text);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.dark-mode .btn-clear-all,
.dark-mode .btn-close-dropdown {
  color: var(--dark-light-text);
}

.btn-clear-all:hover {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.btn-close-dropdown:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-color);
}

.dark-mode .btn-close-dropdown:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--dark-text);
}

.notification-list {
  max-height: 350px;
  overflow-y: auto;
  padding: 8px 0;
}

.notification-item {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: background-color 0.2s ease;
  cursor: pointer;
  position: relative;
}

.dark-mode .notification-item {
  border-bottom-color: rgba(255, 255, 255, 0.05);
}

.notification-item:hover {
  background: rgba(0, 0, 0, 0.02);
}

.dark-mode .notification-item:hover {
  background: rgba(255, 255, 255, 0.02);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: rgba(66, 103, 178, 0.05);
  border-left: 3px solid var(--primary-color);
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.notification-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  margin-top: 2px;
}

.notification-icon.success {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.notification-icon.info {
  background: rgba(23, 162, 184, 0.1);
  color: #17a2b8;
}

.notification-icon.warning {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.notification-icon.error {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.notification-text {
  flex: 1;
  min-width: 0;
}

.notification-message {
  font-size: 0.9rem;
  color: var(--text-color);
  margin: 0 0 4px 0;
  line-height: 1.4;
  word-wrap: break-word;
}

.dark-mode .notification-message {
  color: var(--dark-text);
}

.notification-time {
  font-size: 0.75rem;
  color: var(--light-text);
  margin: 0;
}

.dark-mode .notification-time {
  color: var(--dark-light-text);
}

.no-notifications {
  text-align: center;
  padding: 40px 20px;
  color: var(--light-text);
}

.dark-mode .no-notifications {
  color: var(--dark-light-text);
}

.no-notifications i {
  font-size: 2rem;
  margin-bottom: 12px;
  opacity: 0.5;
}

.no-notifications p {
  margin: 0 0 4px 0;
  font-weight: 500;
}

.no-notifications small {
  opacity: 0.7;
}

.notification-footer {
  padding: 12px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  background: var(--secondary-color);
}

.dark-mode .notification-footer {
  background: var(--dark-secondary);
  border-top-color: rgba(255, 255, 255, 0.1);
}

.btn-view-all {
  width: 100%;
  background: none;
  border: 1px solid var(--primary-color);
  color: var(--primary-color);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-view-all:hover {
  background: var(--primary-color);
  color: white;
}

/* All Notifications Modal */
.all-notifications-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.all-notifications-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.large-modal {
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.notifications-filter {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: var(--secondary-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.dark-mode .notifications-filter {
  background: var(--dark-secondary);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 6px 12px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  background: transparent;
  color: var(--text-color);
  border-radius: 16px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.2s ease;
}

.dark-mode .filter-btn {
  border-color: rgba(255, 255, 255, 0.2);
  color: var(--dark-text);
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.notification-stats {
  font-size: 0.85rem;
  color: var(--light-text);
  font-weight: 500;
}

.dark-mode .notification-stats {
  color: var(--dark-light-text);
}

.all-notifications-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
  min-height: 300px;
}

.no-notifications-modal {
  text-align: center;
  padding: 60px 20px;
  color: var(--light-text);
}

.dark-mode .no-notifications-modal {
  color: var(--dark-light-text);
}

.no-notifications-modal i {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.3;
}

.no-notifications-modal p {
  margin: 0 0 8px 0;
  font-weight: 500;
  font-size: 1.1rem;
}

.no-notifications-modal small {
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-dropdown {
    width: 320px;
    right: -20px;
  }

  .large-modal {
    width: 95%;
    max-height: 95vh;
  }

  .notifications-filter {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-buttons {
    justify-content: center;
  }

  .notification-stats {
    text-align: center;
  }
}

/* Date Filter Modal Styles */
#date-filter-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

#date-filter-modal .modal-content {
  background-color: var(--white);
  margin: 5% auto;
  padding: 0;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
  max-height: 90vh;
  overflow-y: auto;
}

.dark-mode #date-filter-modal .modal-content {
  background-color: var(--dark-card);
}

.date-filter-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 20px 0;
}

.filter-option {
  position: relative;
}

.filter-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.filter-option label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 15px;
  border: 2px solid var(--light-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--light-bg);
}

.filter-option label:hover {
  border-color: var(--primary-color);
  background: rgba(24, 119, 242, 0.05);
}

.filter-option input[type="radio"]:checked + label {
  border-color: var(--primary-color);
  background: rgba(24, 119, 242, 0.1);
}

.filter-option label i {
  color: var(--primary-color);
  font-size: 1.2rem;
  margin-top: 2px;
  flex-shrink: 0;
}

.filter-option label > div,
.filter-option label > span {
  flex: 1;
}

.filter-option label span {
  font-weight: 600;
  color: var(--text-color);
  display: block;
  margin-bottom: 4px;
}

.filter-option label small {
  color: var(--light-text);
  font-size: 0.85rem;
  display: block;
}

.filter-option label input[type="number"] {
  width: 60px;
  padding: 2px 6px;
  border: 1px solid var(--light-border);
  border-radius: 4px;
  font-size: 0.9rem;
  margin: 0 4px;
}

.date-range-section {
  margin-top: 15px;
  padding: 15px;
  background: var(--light-bg);
  border-radius: 8px;
  border: 1px solid var(--light-border);
}

.date-input-group {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.date-input-group:last-child {
  margin-bottom: 0;
}

.date-input-group label {
  min-width: 50px;
  font-weight: 600;
  color: var(--text-color);
}

.date-input-group input[type="datetime-local"] {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--light-border);
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  color: var(--text-color);
}

.filter-preview {
  margin-top: 20px;
  padding: 12px 15px;
  background: rgba(24, 119, 242, 0.1);
  border: 1px solid rgba(24, 119, 242, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-preview i {
  color: var(--primary-color);
}

.filter-preview span {
  color: var(--text-color);
  font-size: 0.9rem;
}

.modal-description {
  color: var(--light-text);
  margin-bottom: 10px;
  line-height: 1.5;
}

/* Dark mode styles for date filter modal */
.dark-mode .filter-option label {
  background: var(--dark-bg-secondary);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

.dark-mode .filter-option label:hover {
  background: rgba(24, 119, 242, 0.1);
}

.dark-mode .filter-option input[type="radio"]:checked + label {
  background: rgba(24, 119, 242, 0.15);
}

.dark-mode .filter-option label span {
  color: var(--dark-text);
}

.dark-mode .filter-option label small {
  color: var(--dark-light-text);
}

.dark-mode .filter-option label input[type="number"] {
  background: var(--dark-bg-secondary);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

.dark-mode .date-range-section {
  background: var(--dark-bg-secondary);
  border-color: var(--dark-border);
}

.dark-mode .date-input-group label {
  color: var(--dark-text);
}

.dark-mode .date-input-group input[type="datetime-local"] {
  background: var(--dark-bg-secondary);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

.dark-mode .filter-preview {
  background: rgba(24, 119, 242, 0.15);
  border-color: rgba(24, 119, 242, 0.3);
}

.dark-mode .filter-preview span {
  color: var(--dark-text);
}

.dark-mode .modal-description {
  color: var(--dark-light-text);
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  padding: 15px 20px;
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  border: 1px solid rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
}

.dark-mode .pagination-container {
  background: var(--dark-card);
  border-color: rgba(255, 255, 255, 0.1);
}

.page-info {
  font-size: 14px;
  color: var(--light-text);
  font-weight: 500;
}

.dark-mode .page-info {
  color: var(--dark-light-text);
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: var(--white);
  color: var(--text-color);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 40px;
  justify-content: center;
}

.page-btn:hover {
  background: var(--secondary-color);
  border-color: var(--accent-color);
  transform: translateY(-1px);
}

.page-btn.active {
  background: var(--accent-color);
  color: var(--white);
  border-color: var(--accent-color);
  box-shadow: 0 2px 8px rgba(24, 119, 242, 0.3);
}

.dark-mode .page-btn {
  background: var(--dark-secondary);
  color: var(--dark-text);
  border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .page-btn:hover {
  background: var(--dark-bg);
  border-color: var(--accent-color);
}

.dark-mode .page-btn.active {
  background: var(--accent-color);
  color: var(--white);
  border-color: var(--accent-color);
}

.page-dots {
  padding: 8px 4px;
  color: var(--light-text);
  font-weight: bold;
}

.dark-mode .page-dots {
  color: var(--dark-light-text);
}

/* Responsive pagination */
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }

  .page-btn {
    padding: 6px 10px;
    font-size: 13px;
    min-width: 36px;
  }
}