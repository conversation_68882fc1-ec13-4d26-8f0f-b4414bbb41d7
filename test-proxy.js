// Test script for proxy functionality
require('dotenv').config();
const proxyManager = require('./proxy-manager');
const { createProxyAxios, testProxyConnectivity, getPuppeteerProxyOptions } = require('./proxy-helpers');

async function testProxyIntegration() {
  console.log('🧪 Testing Proxy Integration...\n');
  
  // Test 1: Check if proxies are loaded
  console.log('1. Checking proxy configuration...');
  const stats = proxyManager.getProxyStats();
  const proxyCount = Object.keys(stats).length;
  
  if (proxyCount === 0) {
    console.log('❌ No proxies configured. Please add PACKETSTREAM_PROXY to your .env file');
    console.log('   Format: proxy.packetstream.io:31111:username:password_session-sessionid');
    return;
  }
  
  console.log(`✅ Found ${proxyCount} configured proxy(s)`);
  
  // Test 2: Test proxy connectivity
  console.log('\n2. Testing proxy connectivity...');
  try {
    const connectivityResult = await testProxyConnectivity();
    if (connectivityResult.success) {
      console.log(`✅ Proxy connectivity successful! Current IP: ${connectivityResult.ip}`);
    } else {
      console.log(`❌ Proxy connectivity failed: ${connectivityResult.error}`);
    }
  } catch (error) {
    console.log(`❌ Proxy connectivity test error: ${error.message}`);
  }
  
  // Test 3: Test Axios with proxy
  console.log('\n3. Testing Axios with proxy...');
  try {
    const proxyAxios = createProxyAxios();
    const response = await proxyAxios.get('https://httpbin.org/ip', { timeout: 10000 });
    console.log(`✅ Axios proxy request successful! IP: ${response.data.origin}`);
  } catch (error) {
    console.log(`❌ Axios proxy request failed: ${error.message}`);
  }
  
  // Test 4: Test Puppeteer proxy configuration
  console.log('\n4. Testing Puppeteer proxy configuration...');
  try {
    const { launchOptions, proxyAuth } = getPuppeteerProxyOptions();
    console.log('✅ Puppeteer proxy configuration generated successfully');
    console.log(`   - Launch args: ${launchOptions.args?.length || 0} arguments`);
    console.log(`   - Proxy auth: ${proxyAuth ? 'configured' : 'not configured'}`);
    
    if (launchOptions.args) {
      const proxyArg = launchOptions.args.find(arg => arg.startsWith('--proxy-server='));
      if (proxyArg) {
        console.log(`   - Proxy server: ${proxyArg.split('=')[1]}`);
      }
    }
  } catch (error) {
    console.log(`❌ Puppeteer proxy configuration failed: ${error.message}`);
  }
  
  // Test 5: Test all configured proxies
  console.log('\n5. Testing all configured proxies...');
  try {
    const results = await proxyManager.testAllProxies();
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log(`✅ Tested ${results.length} proxies:`);
    console.log(`   - Successful: ${successful}`);
    console.log(`   - Failed: ${failed}`);
    
    results.forEach((result, index) => {
      const proxyParts = result.proxy.split(':');
      const proxyId = `${proxyParts[0]}:${proxyParts[1]}`;
      const status = result.success ? '✅' : '❌';
      const time = result.responseTime ? `${result.responseTime}ms` : 'N/A';
      console.log(`   ${status} ${proxyId} (${time})`);
    });
  } catch (error) {
    console.log(`❌ Proxy testing failed: ${error.message}`);
  }
  
  // Test 6: Display current proxy statistics
  console.log('\n6. Current proxy statistics:');
  const currentStats = proxyManager.getProxyStats();
  Object.entries(currentStats).forEach(([proxyId, stat]) => {
    console.log(`   📊 ${proxyId}:`);
    console.log(`      - Requests: ${stat.requests}`);
    console.log(`      - Success Rate: ${stat.successRate}`);
    console.log(`      - Avg Response Time: ${stat.avgResponseTime ? Math.round(stat.avgResponseTime) + 'ms' : 'N/A'}`);
    console.log(`      - Status: ${stat.isFailed ? 'Failed' : 'Active'}`);
    console.log(`      - Last Used: ${stat.lastUsed ? new Date(stat.lastUsed).toLocaleString() : 'Never'}`);
  });
  
  console.log('\n🎉 Proxy integration test completed!');
  console.log('\n💡 Tips:');
  console.log('   - Access proxy manager UI at: http://localhost:3000/proxy-manager.html');
  console.log('   - All scraping operations will now use proxies automatically');
  console.log('   - Metrics updates will also use proxies');
  console.log('   - Proxy rotation is enabled by default');
}

// Run the test
if (require.main === module) {
  testProxyIntegration().catch(console.error);
}

module.exports = { testProxyIntegration };
