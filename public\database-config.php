<?php
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$action = $_GET['action'] ?? $_POST['action'] ?? 'get';

try {
    switch ($action) {
        case 'get':
            // Get current configuration
            $config = DatabaseConfig::getConfig();
            echo json_encode([
                'success' => true,
                'data' => $config
            ]);
            break;
            
        case 'save':
            // Save new configuration
            $newConfig = [
                'DB_HOST' => $_POST['db_host'] ?? 'localhost',
                'DB_PORT' => $_POST['db_port'] ?? '3306',
                'DB_NAME' => $_POST['db_name'] ?? 'facebook_db',
                'DB_USER' => $_POST['db_user'] ?? 'root',
                'DB_PASSWORD' => $_POST['db_password'] ?? '',
                'DB_SSL' => $_POST['db_ssl'] ?? 'false',
                'AUTO_SYNC_ENABLED' => $_POST['auto_sync'] ?? 'true',
                'AUTO_SYNC_INTERVAL' => (int)($_POST['sync_interval'] ?? 5) * 60000, // Convert minutes to milliseconds
                'DATABASE_API_URL' => 'http://localhost/facebook-scraper/public/database_api.php',
                'USE_DATABASE' => 'true'
            ];
            
            if (DatabaseConfig::saveConfig($newConfig)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Configuration saved successfully'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Failed to save configuration'
                ]);
            }
            break;
            
        case 'test':
            // Test database connection
            $result = DatabaseConfig::testConnection();
            echo json_encode($result);
            break;
            
        case 'setup':
            // Setup database and tables
            try {
                $pdo = DatabaseConfig::getConnection();
                $details = [];
                
                // Create database
                $config = DatabaseConfig::getConfig();
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['DB_NAME']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                $pdo->exec("USE `{$config['DB_NAME']}`");
                $details[] = "✅ Database '{$config['DB_NAME']}' created/verified";
                
                // Initialize tables
                if (initializeDatabaseTables()) {
                    $details[] = "✅ Database tables initialized";
                } else {
                    $details[] = "⚠️ Some tables may already exist";
                }
                
                // Run migration for media_id
                $migrationFile = __DIR__ . '/add-media-id-migration.sql';
                if (file_exists($migrationFile)) {
                    $sql = file_get_contents($migrationFile);
                    $sql = preg_replace('/USE\s+\w+;/i', '', $sql);
                    
                    $statements = array_filter(array_map('trim', explode(';', $sql)));
                    foreach ($statements as $statement) {
                        if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                            try {
                                $pdo->exec($statement);
                            } catch (PDOException $e) {
                                if (strpos($e->getMessage(), 'already exists') === false && 
                                    strpos($e->getMessage(), 'Duplicate column') === false) {
                                    throw $e;
                                }
                            }
                        }
                    }
                    $details[] = "✅ Media ID migration applied";
                }
                
                // Test data insertion
                try {
                    $pdo->exec("INSERT IGNORE INTO members (id, name, email, role) VALUES 
                        ('test_admin', 'Test Admin', '<EMAIL>', 'Admin')");
                    $details[] = "✅ Test data inserted";
                } catch (Exception $e) {
                    $details[] = "⚠️ Test data insertion: " . $e->getMessage();
                }
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Database setup completed successfully',
                    'details' => $details
                ]);
                
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Database setup failed: ' . $e->getMessage()
                ]);
            }
            break;
            
        case 'status':
            // Get database status
            try {
                $pdo = DatabaseConfig::getConnection();
                
                // Count records
                $postsCount = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
                $pagesCount = $pdo->query("SELECT COUNT(*) FROM pages")->fetchColumn();
                $membersCount = $pdo->query("SELECT COUNT(*) FROM members")->fetchColumn();
                
                // Check if media_id column exists
                $columns = $pdo->query("SHOW COLUMNS FROM posts LIKE 'media_id'")->fetchAll();
                $hasMediaId = count($columns) > 0;
                
                // Get latest post
                $latestPost = $pdo->query("SELECT extraction_timestamp FROM posts ORDER BY extraction_timestamp DESC LIMIT 1")->fetchColumn();
                
                echo json_encode([
                    'success' => true,
                    'data' => [
                        'posts_count' => (int)$postsCount,
                        'pages_count' => (int)$pagesCount,
                        'members_count' => (int)$membersCount,
                        'has_media_id' => $hasMediaId,
                        'latest_post' => $latestPost,
                        'database_ready' => true
                    ]
                ]);
                
            } catch (Exception $e) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Could not get database status: ' . $e->getMessage(),
                    'data' => [
                        'database_ready' => false
                    ]
                ]);
            }
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'message' => 'Invalid action'
            ]);
            break;
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
