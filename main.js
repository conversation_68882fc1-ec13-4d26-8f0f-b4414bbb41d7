const { app, BrowserWindow, ipc<PERSON><PERSON>, <PERSON>u, Tray, nativeImage } = require('electron');
const path = require('path');
const url = require('url');
const { spawn } = require('child_process');
const isDev = require('electron-is-dev');
const fs = require('fs');

// Keep a global reference of the window object and server process
let mainWindow;
let serverProcess;
let tray = null;
let isQuitting = false;

// Server details
const PORT = process.env.PORT || 3000;
const SERVER_URL = `http://localhost:${PORT}`;

// Create a minimal icon file if it doesn't exist
function createMinimalIcon() {
  const iconPath = path.join(__dirname, 'public/favicon.ico');
  
  try {
    console.log('Checking for icon file at:', iconPath);
    
    // Only create if it doesn't exist or is too small
    if (!fs.existsSync(iconPath) || fs.statSync(iconPath).size < 100) {
      console.log('Creating minimal icon file');
      
      // Create a 16x16 white square icon - simplest valid .ico format
      const headerSize = 22; // Standard ICO header size
      const dibSize = 40;    // Standard DIB header size
      const width = 16;
      const height = 16;
      const bpp = 32;        // 32 bits per pixel (RGBA)
      const dataSize = width * height * 4; // 4 bytes per pixel (RGBA)
      const fileSize = headerSize + dibSize + dataSize;
      
      const buffer = Buffer.alloc(fileSize);
      
      // ICO Header (22 bytes)
      buffer.writeUInt16LE(0, 0);     // Reserved, must be 0
      buffer.writeUInt16LE(1, 2);     // Image type (1 = ICO)
      buffer.writeUInt16LE(1, 4);     // Number of images
      
      // Icon Directory Entry (16 bytes)
      buffer.writeUInt8(width, 6);    // Width
      buffer.writeUInt8(height, 7);   // Height
      buffer.writeUInt8(0, 8);        // Color palette size (0 for true color)
      buffer.writeUInt8(0, 9);        // Reserved, must be 0
      buffer.writeUInt16LE(1, 10);    // Color planes
      buffer.writeUInt16LE(bpp, 12);  // Bits per pixel
      buffer.writeUInt32LE(dibSize + dataSize, 14); // Image data size
      buffer.writeUInt32LE(headerSize, 18); // Offset to image data
      
      // DIB Header (40 bytes)
      buffer.writeUInt32LE(dibSize, 22); // DIB header size
      buffer.writeInt32LE(width, 26);    // Width
      buffer.writeInt32LE(height * 2, 30); // Height (doubled for ICO format)
      buffer.writeUInt16LE(1, 34);      // Planes
      buffer.writeUInt16LE(bpp, 36);    // Bits per pixel
      buffer.writeUInt32LE(0, 38);      // Compression (0 = none)
      buffer.writeUInt32LE(dataSize, 42); // Image size
      buffer.writeInt32LE(0, 46);       // X pixels per meter
      buffer.writeInt32LE(0, 50);       // Y pixels per meter
      buffer.writeUInt32LE(0, 54);      // Colors used
      buffer.writeUInt32LE(0, 58);      // Important colors
      
      // Fill the rest with white pixels (RGBA)
      for (let i = 0; i < width * height; i++) {
        const offset = headerSize + dibSize + (i * 4);
        buffer.writeUInt8(255, offset);     // B
        buffer.writeUInt8(255, offset + 1); // G
        buffer.writeUInt8(255, offset + 2); // R
        buffer.writeUInt8(255, offset + 3); // A
      }
      
      fs.writeFileSync(iconPath, buffer);
      console.log('Minimal icon file created successfully, size:', buffer.length, 'bytes');
      return iconPath;
    } else {
      console.log('Icon file already exists, size:', fs.statSync(iconPath).size);
      return iconPath;
    }
  } catch (err) {
    console.error('Failed to create minimal icon:', err);
    
    // Create an empty icon as fallback
    try {
      const emptyIconPath = path.join(__dirname, 'empty-icon.ico');
      const emptyBuffer = Buffer.from([
        0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x10, 0x10, 0x00, 0x00, 0x01, 0x00,
        0x20, 0x00, 0x68, 0x04, 0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x28, 0x00,
        0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x01, 0x00,
        0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0x00
      ]);
      fs.writeFileSync(emptyIconPath, emptyBuffer);
      console.log('Created empty fallback icon at:', emptyIconPath);
      return emptyIconPath;
    } catch (fallbackErr) {
      console.error('Failed to create fallback icon:', fallbackErr);
      return null;
    }
  }
}

// Get icon path or use a default
function getIconPath() {
  try {
    // First, ensure we have a minimal icon file
    const minimalIconPath = createMinimalIcon();
    if (minimalIconPath) {
      return minimalIconPath;
    }
    
    // Check for multiple possible icon file paths
    const iconPaths = [
      path.join(__dirname, 'public/icon.png'),
      path.join(__dirname, 'public/favicon.ico')
    ];
    
    for (const iconPath of iconPaths) {
      if (fs.existsSync(iconPath) && fs.statSync(iconPath).size > 10) {
        return iconPath;
      }
    }
    
    // If no valid icons found, return empty image
    console.warn('No valid icon found, using empty image');
    return nativeImage.createEmpty();
  } catch (error) {
    console.error('Error getting icon path:', error);
    return nativeImage.createEmpty();
  }
}

// Call this function to ensure the tray icon is loaded properly
function ensureTrayIcon() {
  createMinimalIcon();
}

function createWindow() {
  // Ensure we have a valid icon before creating the window
  ensureTrayIcon();
  
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    icon: getIconPath(),
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      preload: path.join(__dirname, 'preload.js')
    },
    show: false, // Don't show the window until it's ready
    title: 'Facebook Posts App'
  });

  // Start the Express server as a child process
  startServer();

  // Wait for the server to start before loading the URL
  setTimeout(() => {
    // Load the app
    mainWindow.loadURL(SERVER_URL);

    // Show window when ready to avoid flashing blank content
    mainWindow.once('ready-to-show', () => {
      mainWindow.show();
      
      // Open DevTools automatically in development mode
      // Commented out to prevent DevTools from opening automatically
      // if (isDev) {
      //   mainWindow.webContents.openDevTools();
      // }
    });
  }, 1000); // Give the server a second to start

  // Create system tray
  createTray();

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle close event (minimize to tray instead of closing)
  mainWindow.on('close', (event) => {
    if (!isQuitting) {
      event.preventDefault();
      mainWindow.hide();
      return false;
    }
    return true;
  });
}

function startServer() {
  // Start the Express server
  serverProcess = spawn('node', ['server.js'], {
    stdio: 'pipe', // Pipe the output
    detached: false // Keep tied to parent process
  });

  // Log server output
  serverProcess.stdout.on('data', (data) => {
    console.log(`Server: ${data}`);
  });

  serverProcess.stderr.on('data', (data) => {
    console.error(`Server error: ${data}`);
  });

  serverProcess.on('close', (code) => {
    console.log(`Server process exited with code ${code}`);
  });
}

function createTray() {
  try {
    // Check if favicon.ico exists
    const iconPath = path.join(__dirname, 'public/favicon.ico');
    if (!fs.existsSync(iconPath) || fs.statSync(iconPath).size < 10) {
      console.log('Favicon.ico not found or invalid, skipping tray creation');
      return; // Skip tray creation if icon doesn't exist
    }
    
    // Create an empty icon if the file doesn't exist
    const icon = getIconPath();
    tray = new Tray(icon);
    
    const contextMenu = Menu.buildFromTemplate([
      { 
        label: 'Show App', 
        click: () => {
          if (mainWindow) {
            mainWindow.show();
          }
        } 
      },
      { 
        label: 'Start Scraper', 
        click: () => {
          if (mainWindow) {
            mainWindow.webContents.send('start-scraper');
          }
        } 
      },
      { 
        label: 'Stop Scraper', 
        click: () => {
          if (mainWindow) {
            mainWindow.webContents.send('stop-scraper');
          }
        } 
      },
      { type: 'separator' },
      { 
        label: 'Quit', 
        click: () => {
          isQuitting = true;
          app.quit();
        } 
      }
    ]);
    
    tray.setToolTip('Facebook Posts App');
    tray.setContextMenu(contextMenu);
    
    // Show app on double click
    tray.on('double-click', () => {
      if (mainWindow) {
        mainWindow.show();
      }
    });
  } catch (error) {
    console.error('Failed to create tray icon:', error);
    // Continue without tray icon
  }
}

// Create window when app is ready
app.whenReady().then(createWindow);

// Quit when all windows are closed (except on macOS)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Clean up before quitting
app.on('before-quit', () => {
  isQuitting = true;
  
  // Kill the server process
  if (serverProcess) {
    try {
      if (process.platform === 'win32') {
        // Windows-specific process termination
        require('child_process').exec(`taskkill /pid ${serverProcess.pid} /T /F`);
      } else {
        process.kill(-serverProcess.pid);
      }
    } catch (error) {
      console.error('Error killing server process:', error);
    }
  }
});

// IPC events from renderer
ipcMain.on('minimize-to-tray', () => {
  if (mainWindow) {
    mainWindow.hide();
  }
});

// Show the window when requested from the renderer
ipcMain.on('show-window', () => {
  if (mainWindow) {
    mainWindow.show();
  }
});

// Handle errors
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
}); 