// Test script to verify failed proxy deletion functionality
const axios = require('axios');

async function testFailedProxyDeletion() {
  try {
    console.log('🧪 Testing Failed Proxy Deletion...\n');

    // First, get current failed proxies
    console.log('1. Getting current failed proxies...');
    const failedResponse = await axios.get('http://localhost:3000/api/proxy/failed');
    
    if (!failedResponse.data.success) {
      console.log('❌ Failed to get failed proxies');
      return;
    }
    
    const failedProxies = failedResponse.data.failedProxies;
    console.log(`   Found ${failedProxies.length} failed proxies`);
    
    if (failedProxies.length === 0) {
      console.log('   No failed proxies to test deletion with');
      return;
    }
    
    // Show first few failed proxies
    console.log('\n   Sample failed proxies:');
    failedProxies.slice(0, 3).forEach((proxy, index) => {
      const sessionId = proxy.proxy.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown';
      console.log(`   ${index + 1}. Session ${sessionId} - Failures: ${proxy.failures}`);
    });

    // Test single proxy deletion (if we have at least 2 failed proxies)
    if (failedProxies.length >= 2) {
      console.log('\n2. Testing single proxy deletion...');
      const proxyToDelete = failedProxies[0].proxy;
      const sessionId = proxyToDelete.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown';
      
      console.log(`   Deleting proxy with session: ${sessionId}`);
      
      const deleteResponse = await axios.post('http://localhost:3000/api/proxy/delete-failed', {
        proxy: proxyToDelete
      });
      
      if (deleteResponse.data.success) {
        console.log(`   ✅ Successfully deleted proxy: ${deleteResponse.data.message}`);
        
        // Verify it's gone
        const verifyResponse = await axios.get('http://localhost:3000/api/proxy/failed');
        const newFailedCount = verifyResponse.data.failedProxies.length;
        console.log(`   ✅ Failed proxy count reduced from ${failedProxies.length} to ${newFailedCount}`);
      } else {
        console.log(`   ❌ Failed to delete proxy: ${deleteResponse.data.error}`);
      }
    }

    // Test delete all failed proxies
    console.log('\n3. Testing delete all failed proxies...');
    
    const deleteAllResponse = await axios.post('http://localhost:3000/api/proxy/delete-all-failed');
    
    if (deleteAllResponse.data.success) {
      console.log(`   ✅ Successfully deleted all failed proxies: ${deleteAllResponse.data.message}`);
      console.log(`   ✅ Deleted count: ${deleteAllResponse.data.deletedCount}`);
      
      // Verify all are gone
      const finalVerifyResponse = await axios.get('http://localhost:3000/api/proxy/failed');
      const finalFailedCount = finalVerifyResponse.data.failedProxies.length;
      console.log(`   ✅ Final failed proxy count: ${finalFailedCount}`);
      
      if (finalFailedCount === 0) {
        console.log('   🎉 All failed proxies successfully deleted!');
      } else {
        console.log(`   ⚠️ Still have ${finalFailedCount} failed proxies remaining`);
      }
    } else {
      console.log(`   ❌ Failed to delete all failed proxies: ${deleteAllResponse.data.error}`);
    }

    // Check final stats
    console.log('\n4. Final proxy statistics...');
    const statsResponse = await axios.get('http://localhost:3000/api/proxy/stats');
    if (statsResponse.data.success) {
      console.log(`   Total Proxies: ${statsResponse.data.totalProxies}`);
      console.log(`   Active Proxies: ${statsResponse.data.activeProxies}`);
      console.log(`   Failed Proxies: ${statsResponse.data.failedProxies}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.error || error.message);
  }
}

// Run the test
testFailedProxyDeletion();
