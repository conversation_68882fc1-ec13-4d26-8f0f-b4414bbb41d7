// Proxy Manager for PacketStream Integration
const { HttpsProxyAgent } = require('https-proxy-agent');
const { SocksProxyAgent } = require('socks-proxy-agent');
const fs = require('fs');
const path = require('path');

class ProxyManager {
  constructor() {
    this.proxies = [];
    this.currentIndex = 0;
    this.failedProxies = new Set();
    this.permanentlyFailedProxies = new Set(); // New: permanently failed proxies
    this.proxyStats = new Map();
    this.reservedProxies = new Set(); // ✅ Track temporarily reserved proxies during pre-allocation
    this.rotationEnabled = true;
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second
    this.proxyFile = 'proxies.json'; // File to persist proxies
    this.statsFile = path.join(__dirname, 'proxy_statistics.json'); // File to persist statistics

    // Load persistent statistics first
    this.loadStatistics();

    // Load proxies from environment or config
    this.loadProxies();
  }

  /**
   * Load proxies from environment variables or configuration
   */
  loadProxies() {
    // First, try to load from saved file
    this.loadFromFile();

    // Load from environment variable (comma-separated list)
    const envProxies = process.env.PACKETSTREAM_PROXIES;
    if (envProxies) {
      const envProxyList = envProxies.split(',').map(proxy => proxy.trim());
      envProxyList.forEach(proxy => {
        if (!this.proxies.includes(proxy)) {
          this.proxies.push(proxy);
        }
      });
    }

    // Load from individual environment variables
    const singleProxy = process.env.PACKETSTREAM_PROXY;
    if (singleProxy && !this.proxies.includes(singleProxy)) {
      this.proxies.push(singleProxy);
    }

    // Default PacketStream proxy format if none provided
    if (this.proxies.length === 0) {
      console.warn('⚠️ No proxies configured. Add PACKETSTREAM_PROXIES to your .env file');
      console.warn('Format: proxy.packetstream.io:31111:username:password_session-sessionid');
    }

    console.log(`🔄 Loaded ${this.proxies.length} proxies`);
    this.initializeStats();

    // Save to file after loading
    this.saveToFile();
  }

  /**
   * Load proxies from file
   */
  loadFromFile() {
    try {
      const fs = require('fs');
      if (fs.existsSync(this.proxyFile)) {
        const data = fs.readFileSync(this.proxyFile, 'utf8');
        const savedData = JSON.parse(data);
        this.proxies = savedData.proxies || [];

        // Load permanently failed proxies
        if (savedData.permanentlyFailedProxies) {
          this.permanentlyFailedProxies = new Set(savedData.permanentlyFailedProxies);
          console.log(`📁 Loaded ${this.proxies.length} active proxies and ${this.permanentlyFailedProxies.size} permanently failed proxies from file`);
        } else {
          console.log(`📁 Loaded ${this.proxies.length} proxies from file`);
        }
      }
    } catch (error) {
      console.warn(`⚠️ Could not load proxies from file: ${error.message}`);
    }
  }

  /**
   * Save proxies to file
   */
  saveToFile() {
    try {
      const fs = require('fs');
      const data = {
        proxies: this.proxies,
        permanentlyFailedProxies: Array.from(this.permanentlyFailedProxies),
        lastUpdated: new Date().toISOString()
      };
      fs.writeFileSync(this.proxyFile, JSON.stringify(data, null, 2));
      console.log(`💾 Saved ${this.proxies.length} active proxies and ${this.permanentlyFailedProxies.size} permanently failed proxies to file`);
    } catch (error) {
      console.warn(`⚠️ Could not save proxies to file: ${error.message}`);
    }
  }

  /**
   * Initialize statistics for each proxy (only for new proxies)
   */
  initializeStats() {
    let newProxiesCount = 0;
    this.proxies.forEach(proxy => {
      // Only initialize if proxy doesn't already have stats (to preserve loaded statistics)
      if (!this.proxyStats.has(proxy)) {
        this.proxyStats.set(proxy, {
          requests: 0,
          successes: 0,
          failures: 0,
          lastUsed: null,
          avgResponseTime: 0,
          totalResponseTime: 0
        });
        newProxiesCount++;
      }
    });

    if (newProxiesCount > 0) {
      console.log(`📊 Initialized statistics for ${newProxiesCount} new proxies`);
      this.saveStatistics(); // Save the new statistics
    }

    // Clean up statistics for proxies that are no longer active
    this.cleanupOrphanedStats();
  }

  /**
   * Parse PacketStream proxy format
   * Format: proxy.packetstream.io:31111:username:password_session-sessionid
   */
  parseProxy(proxyString) {
    const parts = proxyString.split(':');
    if (parts.length !== 4) {
      throw new Error(`Invalid proxy format: ${proxyString}`);
    }

    return {
      host: parts[0],
      port: parseInt(parts[1]),
      username: parts[2],
      password: parts[3],
      url: `http://${parts[2]}:${parts[3]}@${parts[0]}:${parts[1]}`
    };
  }

  /**
   * Get the current proxy without advancing rotation
   */
  getCurrentProxy() {
    if (this.proxies.length === 0) {
      return null;
    }

    // Filter out failed proxies
    const availableProxies = this.proxies.filter(proxy => !this.failedProxies.has(proxy));

    if (availableProxies.length === 0) {
      return this.proxies[0];
    }

    if (!this.rotationEnabled) {
      return availableProxies[0];
    }

    // Get current proxy without advancing index
    const currentIndex = this.currentIndex === 0 ? availableProxies.length - 1 : this.currentIndex - 1;
    return availableProxies[currentIndex % availableProxies.length];
  }

  /**
   * Get the next available proxy
   */
  getNextProxy() {
    if (this.proxies.length === 0) {
      return null;
    }

    // Filter out temporarily failed and temporarily reserved proxies (permanently failed ones are already removed from this.proxies)
    let availableProxies = this.proxies.filter(proxy =>
      !this.failedProxies.has(proxy) &&
      !this.reservedProxies.has(proxy) // ✅ Exclude temporarily reserved proxies
    );

    // If more than 80% of remaining proxies are temporarily failed, reset the temporary failed list
    if (availableProxies.length < (this.proxies.length * 0.2)) {
      console.warn(`⚠️ ${this.failedProxies.size}/${this.proxies.length} proxies temporarily failed, resetting temporary failed list for better distribution`);
      this.failedProxies.clear();
      availableProxies = this.proxies;
    }

    if (availableProxies.length === 0) {
      console.warn('⚠️ All remaining proxies have temporarily failed, resetting temporary failed list');
      this.failedProxies.clear();
      return this.proxies[0];
    }

    if (!this.rotationEnabled) {
      return availableProxies[0];
    }

    // Ensure currentIndex is within bounds
    if (this.currentIndex >= availableProxies.length) {
      this.currentIndex = 0;
    }

    // Smart load balancing: find ALL proxies with the minimum request count
    let minRequests = Infinity;

    // First pass: find the minimum request count
    for (const proxy of availableProxies) {
      const requestCount = this.getProxyRequestCount(proxy);
      if (requestCount < minRequests) {
        minRequests = requestCount;
      }
    }

    // Second pass: collect all proxies with the minimum request count
    const leastUsedProxies = [];
    for (const proxy of availableProxies) {
      const requestCount = this.getProxyRequestCount(proxy);
      if (requestCount === minRequests) {
        leastUsedProxies.push(proxy);
      }
    }

    // Randomly select from the least-used proxies to ensure even distribution
    const randomIndex = Math.floor(Math.random() * leastUsedProxies.length);
    const selectedProxy = leastUsedProxies[randomIndex];

    // Find the index for logging
    const proxyIndex = availableProxies.indexOf(selectedProxy) + 1;
    const sessionId = selectedProxy.split(':')[3]?.split('_session-')[1] || selectedProxy.split(':')[3]?.substring(-8) || 'unknown';

    console.log(`🔄 Selected least-used proxy ${proxyIndex}/${availableProxies.length}: ${sessionId} (${minRequests} requests, ${leastUsedProxies.length} available with this count)`);

    // ✅ FIXED: DON'T count selection as request - only count actual HTTP usage!
    // this.recordProxyRequest(selectedProxy); // REMOVED!

    // ✅ TEMPORARILY RESERVE this proxy to prevent duplicate selection during pre-allocation
    this.reservedProxies.add(selectedProxy);

    // Auto-release reservation after 30 seconds (in case it's not used)
    setTimeout(() => {
      this.reservedProxies.delete(selectedProxy);
    }, 30000);

    return selectedProxy;
  }

  /**
   * Get the number of requests made by a specific proxy
   */
  getProxyRequestCount(proxy) {
    const stats = this.proxyStats.get(proxy);
    return stats ? (stats.requests || 0) : 0;
  }

  /**
   * Clean up statistics for proxies that are no longer active
   */
  cleanupOrphanedStats() {
    const allActiveProxies = new Set([...this.proxies, ...this.permanentlyFailedProxies]);
    let removedCount = 0;

    // Find statistics for proxies that are no longer in any list
    const orphanedStats = [];
    this.proxyStats.forEach((stats, proxy) => {
      if (!allActiveProxies.has(proxy)) {
        orphanedStats.push(proxy);
      }
    });

    // Remove orphaned statistics
    orphanedStats.forEach(proxy => {
      this.proxyStats.delete(proxy);
      removedCount++;
    });

    if (removedCount > 0) {
      console.log(`🧹 Cleaned up ${removedCount} orphaned proxy statistics`);
      this.saveStatistics(); // Save the cleaned statistics
    }
  }

  /**
   * Reset all proxy statistics to 0 (fresh start)
   */
  resetAllStats() {
    console.log(`🔄 Resetting all proxy statistics to 0...`);
    this.proxies.forEach(proxy => {
      this.proxyStats.set(proxy, {
        requests: 0,
        successes: 0,
        failures: 0,
        lastUsed: null,
        avgResponseTime: 0,
        totalResponseTime: 0
      });
    });
    this.saveStatistics();
    console.log(`✅ Reset statistics for ${this.proxies.length} proxies`);
  }

  /**
   * Get proxy agent for HTTP requests (Axios, fetch, etc.)
   */
  getProxyAgent(proxyString = null) {
    const proxy = proxyString || this.getNextProxy();
    if (!proxy) {
      return null;
    }

    try {
      const proxyConfig = this.parseProxy(proxy);
      return new HttpsProxyAgent(proxyConfig.url);
    } catch (error) {
      console.error(`❌ Error creating proxy agent for ${proxy}:`, error.message);
      this.markProxyAsFailed(proxy);
      return null;
    }
  }

  /**
   * Get proxy configuration for Puppeteer
   */
  getPuppeteerProxyConfig(proxyString = null) {
    const proxy = proxyString || this.getNextProxy();
    if (!proxy) {
      return {};
    }

    try {
      const proxyConfig = this.parseProxy(proxy);

      // ✅ FIXED: Count actual Puppeteer usage (when browser actually uses proxy)
      this.recordProxyRequest(proxy);

      return {
        args: [
          `--proxy-server=${proxyConfig.host}:${proxyConfig.port}`,
          '--no-sandbox',
          '--disable-setuid-sandbox'
        ],
        proxyAuth: {
          username: proxyConfig.username,
          password: proxyConfig.password
        },
        proxyString: proxy // ✅ Return the proxy string for tracking
      };
    } catch (error) {
      console.error(`❌ Error creating Puppeteer proxy config for ${proxy}:`, error.message);
      this.markProxyAsFailed(proxy);
      return {};
    }
  }

  /**
   * Mark a proxy as failed (permanently)
   */
  markProxyAsFailed(proxy) {
    // ✅ FIXED: Only mark as failed if not already permanently failed (prevents multiple failure increments)
    if (this.permanentlyFailedProxies.has(proxy)) {
      const sessionId = proxy.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown';
      console.log(`⚠️ Proxy ${sessionId} already permanently failed (skipping duplicate failure)`);
      return;
    }

    this.failedProxies.add(proxy);
    const stats = this.proxyStats.get(proxy);
    if (stats) {
      stats.failures++;
    }

    // ✅ ENSURE CONSISTENCY: Always add to permanently failed list first
    this.permanentlyFailedProxies.add(proxy);

    console.warn(`❌ Permanently marked proxy as failed: ${proxy.split(':')[0]}:${proxy.split(':')[1]} (${this.permanentlyFailedProxies.size} total failed)`);

    // Remove from main proxy list
    const index = this.proxies.indexOf(proxy);
    if (index > -1) {
      this.proxies.splice(index, 1);
      console.log(`🗑️ Removed proxy from active rotation: ${proxy.split(':')[0]}:${proxy.split(':')[1]} (${this.proxies.length} remaining active)`);

      // Save updated proxy list and statistics
      this.saveToFile();
      this.saveStatistics();
    }

    // Auto-reset temporary failed proxies if too many accumulate (but keep permanently failed ones)
    if (this.failedProxies.size > (this.proxies.length * 0.6)) {
      console.log(`🔄 Auto-resetting temporary failed proxies for better distribution (${this.failedProxies.size}/${this.proxies.length} were temporarily failed)`);
      // Only clear temporary failures, not permanent ones
      const tempFailedProxies = new Set();
      this.failedProxies.forEach(proxy => {
        if (!this.permanentlyFailedProxies.has(proxy)) {
          tempFailedProxies.add(proxy);
        }
      });
      tempFailedProxies.forEach(proxy => this.failedProxies.delete(proxy));
    }
  }

  /**
   * Mark a proxy as successful (without incrementing requests - that's done elsewhere)
   */
  markProxyAsSuccessful(proxy, responseTime = 0) {
    this.failedProxies.delete(proxy);
    const stats = this.proxyStats.get(proxy);
    if (stats) {
      // ✅ FIXED: Don't increment successes beyond request count (prevents 200% success rate)
      if (stats.successes < stats.requests) {
        stats.successes++;
        stats.lastUsed = new Date();
        stats.totalResponseTime += responseTime;
        stats.avgResponseTime = stats.totalResponseTime / stats.successes;

        // ✅ FIXED: Save statistics immediately to ensure persistence
        this.saveStatistics();

        const sessionId = proxy.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown';
        console.log(`✅ Marked proxy ${sessionId} as successful: ${stats.successes}/${stats.requests} success rate`);
      } else {
        const sessionId = proxy.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown';
        console.log(`⚠️ Proxy ${sessionId} already at max success rate: ${stats.successes}/${stats.requests} (skipping duplicate success)`);
      }
    }
  }

  /**
   * Record a proxy request (for tracking usage)
   */
  recordProxyRequest(proxy) {
    const stats = this.proxyStats.get(proxy);
    if (stats) {
      stats.requests++;
      stats.lastUsed = new Date();

      // ✅ RELEASE RESERVATION: Proxy is now actually being used
      this.reservedProxies.delete(proxy);

      // ✅ FIXED: Save statistics immediately to ensure persistence
      this.saveStatistics();

      const sessionId = proxy.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown';
      console.log(`📊 Recorded request for proxy ${sessionId}: ${stats.requests} total requests`);
    }
  }

  /**
   * Automatically detect and mark failed proxies (only after multiple failures)
   */
  autoDetectFailedProxies() {
    const proxiesToFail = [];

    this.proxyStats.forEach((stats, proxy) => {
      const isActive = this.proxies.includes(proxy);
      const isPermanentlyFailed = this.permanentlyFailedProxies.has(proxy);

      // Only mark as failed if proxy has multiple requests with 0% success rate AND has actual failures recorded
      if (isActive && !isPermanentlyFailed && stats.requests >= 3 && stats.successes === 0 && stats.failures > 0) {
        proxiesToFail.push(proxy);
      }
    });

    // Mark detected failed proxies
    if (proxiesToFail.length > 0) {
      console.log(`🔍 Auto-detected ${proxiesToFail.length} failed proxies with consistent failures`);
      proxiesToFail.forEach(proxy => {
        const sessionId = proxy.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown';
        const stats = this.proxyStats.get(proxy);
        console.log(`🚨 Auto-marking failed proxy: ${sessionId} (${stats.requests} requests, ${stats.failures} failures, 0 successes)`);

        // Mark as permanently failed
        this.markProxyAsFailed(proxy);
      });
    }
  }

  /**
   * Update proxy statistics (DEPRECATED - use recordProxyRequest + markProxyAsSuccessful instead)
   */
  updateProxyStats(proxy, success, responseTime = 0) {
    const stats = this.proxyStats.get(proxy);
    if (stats) {
      // ✅ FIXED: Don't increment requests here (already done in getNextProxy)
      if (success) {
        stats.successes++;
        stats.totalResponseTime += responseTime;
        stats.avgResponseTime = stats.totalResponseTime / stats.successes;
        this.failedProxies.delete(proxy);
      } else {
        stats.failures++;
        this.markProxyAsFailed(proxy);
      }
      stats.lastUsed = new Date();

      // Save statistics immediately
      this.saveStatistics();
    }
  }

  /**
   * Get proxy statistics
   */
  getProxyStats() {
    const stats = {};
    this.proxyStats.forEach((stat, proxy) => {
      const proxyParts = proxy.split(':');
      // Use the session ID to make each proxy unique
      const sessionId = proxyParts[3] ? proxyParts[3].split('_session-')[1] : 'unknown';
      const proxyId = `${proxyParts[0]}:${proxyParts[1]}:${proxyParts[2]}:session-${sessionId}`;
      stats[proxyId] = {
        ...stat,
        fullProxy: proxy, // Store the full proxy string for reference
        host: proxyParts[0],
        port: proxyParts[1],
        username: proxyParts[2],
        sessionId: sessionId,
        successRate: stat.requests > 0 ? (stat.successes / stat.requests * 100).toFixed(2) + '%' : '0%',
        isFailed: this.failedProxies.has(proxy)
      };
    });
    return stats;
  }

  /**
   * Reset all proxy failures
   */
  resetFailedProxies() {
    this.failedProxies.clear();
    console.log('🔄 Reset all failed proxies');
  }

  /**
   * Enable/disable proxy rotation
   */
  setRotationEnabled(enabled) {
    this.rotationEnabled = enabled;
    console.log(`🔄 Proxy rotation ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Add a new proxy
   */
  addProxy(proxyString) {
    try {
      this.parseProxy(proxyString); // Validate format
      if (!this.proxies.includes(proxyString)) {
        this.proxies.push(proxyString);

        // ✅ FORCE FRESH STATISTICS: Always create new stats with 0 requests
        this.proxyStats.set(proxyString, {
          requests: 0,
          successes: 0,
          failures: 0,
          lastUsed: null,
          avgResponseTime: 0,
          totalResponseTime: 0
        });

        // ✅ ENSURE CLEAN STATE: Remove from any failed lists
        this.failedProxies.delete(proxyString);
        this.permanentlyFailedProxies.delete(proxyString);

        console.log(`✅ Added new proxy: ${proxyString.split(':')[0]}:${proxyString.split(':')[1]} (Total: ${this.proxies.length})`);
        console.log(`🆕 Fresh statistics created: 0 requests, 0 successes, 0 failures`);

        this.saveToFile(); // Persist to file
        this.saveStatistics(); // ✅ SAVE FRESH STATISTICS
        return true;
      } else {
        console.log(`⚠️ Proxy already exists: ${proxyString.split(':')[0]}:${proxyString.split(':')[1]}`);
        return false;
      }
    } catch (error) {
      console.error(`❌ Invalid proxy format: ${proxyString} - ${error.message}`);
      throw error;
    }
  }

  /**
   * Remove a proxy
   */
  removeProxy(proxyString) {
    const index = this.proxies.indexOf(proxyString);
    if (index > -1) {
      this.proxies.splice(index, 1);
      this.proxyStats.delete(proxyString);
      this.failedProxies.delete(proxyString);
      this.permanentlyFailedProxies.delete(proxyString); // Also remove from permanently failed
      console.log(`🗑️ Removed proxy: ${proxyString.split(':')[0]}:${proxyString.split(':')[1]}`);
      console.log(`🧹 Deleted ALL records for proxy: statistics, failed status, permanent failed status`);
      this.saveToFile(); // Persist proxy list to file
      this.saveStatistics(); // ✅ FIXED: Also persist updated statistics
    }
  }

  /**
   * ✅ NUCLEAR OPTION: Delete ALL proxies and ALL records
   */
  deleteAllProxies() {
    console.log(`🚨 DELETING ALL PROXIES AND RECORDS...`);
    console.log(`📊 Before: ${this.proxies.length} proxies, ${this.proxyStats.size} statistics, ${this.failedProxies.size} failed, ${this.permanentlyFailedProxies.size} permanently failed`);

    // Clear everything
    this.proxies = [];
    this.proxyStats.clear();
    this.failedProxies.clear();
    this.permanentlyFailedProxies.clear();
    this.currentIndex = 0;

    // Save empty state to files
    this.saveToFile();
    this.saveStatistics();

    console.log(`✅ COMPLETE DELETION: All proxies, statistics, and records deleted`);
    console.log(`📊 After: ${this.proxies.length} proxies, ${this.proxyStats.size} statistics, ${this.failedProxies.size} failed, ${this.permanentlyFailedProxies.size} permanently failed`);
  }

  /**
   * Remove all proxies
   */
  removeAllProxies() {
    const count = this.proxies.length;
    this.proxies = [];
    this.proxyStats.clear();
    this.failedProxies.clear();
    this.permanentlyFailedProxies.clear(); // Also clear permanently failed
    this.currentIndex = 0;
    console.log(`🗑️ Removed all ${count} proxies`);
    this.saveToFile(); // Persist proxy list to file
    this.saveStatistics(); // ✅ FIXED: Also persist cleared statistics
    return count;
  }

  /**
   * Restore a permanently failed proxy back to active rotation
   */
  restoreProxy(proxyString) {
    if (this.permanentlyFailedProxies.has(proxyString)) {
      // Remove from permanently failed list
      this.permanentlyFailedProxies.delete(proxyString);

      // Add back to active proxies
      if (!this.proxies.includes(proxyString)) {
        this.proxies.push(proxyString);
      }

      // Remove from failed proxies set
      this.failedProxies.delete(proxyString);

      // Reset stats for the restored proxy
      this.proxyStats.set(proxyString, {
        requests: 0,
        successes: 0,
        failures: 0,
        lastUsed: null,
        avgResponseTime: 0,
        totalResponseTime: 0
      });

      console.log(`🔄 Restored proxy to active rotation: ${proxyString.split(':')[0]}:${proxyString.split(':')[1]}`);
      this.saveToFile(); // Persist changes
      return true;
    }
    return false;
  }

  /**
   * Permanently delete a proxy from all lists
   */
  deleteProxy(proxyString) {
    let deleted = false;

    // Remove from active proxies
    const activeIndex = this.proxies.indexOf(proxyString);
    if (activeIndex > -1) {
      this.proxies.splice(activeIndex, 1);
      deleted = true;
    }

    // Remove from permanently failed proxies
    if (this.permanentlyFailedProxies.has(proxyString)) {
      this.permanentlyFailedProxies.delete(proxyString);
      deleted = true;
    }

    // Remove from failed proxies set
    this.failedProxies.delete(proxyString);

    // Remove stats
    this.proxyStats.delete(proxyString);

    if (deleted) {
      console.log(`🗑️ Permanently deleted proxy: ${proxyString.split(':')[0]}:${proxyString.split(':')[1]}`);
      this.saveToFile(); // Persist changes
    }

    return deleted;
  }

  /**
   * Test a proxy connection
   */
  async testProxy(proxyString, testUrl = 'https://httpbin.org/ip') {
    const startTime = Date.now();
    try {
      const agent = this.getProxyAgent(proxyString);
      if (!agent) {
        throw new Error('Failed to create proxy agent');
      }

      const axios = require('axios');
      const response = await axios.get(testUrl, {
        httpsAgent: agent,
        timeout: 10000
      });

      const responseTime = Date.now() - startTime;
      this.updateProxyStats(proxyString, true, responseTime);
      
      console.log(`✅ Proxy test successful: ${proxyString.split(':')[0]}:${proxyString.split(':')[1]} (${responseTime}ms)`);
      return { success: true, responseTime, data: response.data };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.updateProxyStats(proxyString, false, responseTime);
      
      console.error(`❌ Proxy test failed: ${proxyString.split(':')[0]}:${proxyString.split(':')[1]} - ${error.message}`);
      return { success: false, error: error.message, responseTime };
    }
  }

  /**
   * Test all proxies
   */
  async testAllProxies() {
    console.log('🧪 Testing all proxies...');
    const results = [];
    
    for (const proxy of this.proxies) {
      const result = await this.testProxy(proxy);
      results.push({ proxy, ...result });
      
      // Add delay between tests to avoid overwhelming the proxies
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return results;
  }

  /**
   * Load persistent proxy statistics from file
   */
  loadStatistics() {
    try {
      if (fs.existsSync(this.statsFile)) {
        const statsData = fs.readFileSync(this.statsFile, 'utf8');
        const parsedStats = JSON.parse(statsData);

        // Convert plain object back to Map with proper structure
        this.proxyStats = new Map();
        for (const [proxy, stats] of Object.entries(parsedStats)) {
          this.proxyStats.set(proxy, {
            requests: stats.requests || 0,
            successes: stats.successes || 0,
            failures: stats.failures || 0,
            totalResponseTime: stats.totalResponseTime || 0,
            avgResponseTime: stats.avgResponseTime || 0,
            lastUsed: stats.lastUsed ? new Date(stats.lastUsed) : new Date()
          });
        }

        console.log(`📊 Loaded proxy statistics for ${this.proxyStats.size} proxies from file`);
      } else {
        console.log('📊 No existing proxy statistics file found, starting fresh');
      }
    } catch (error) {
      console.error('❌ Error loading proxy statistics:', error.message);
      this.proxyStats = new Map(); // Reset to empty map on error
    }
  }

  /**
   * Save proxy statistics to file
   */
  saveStatistics() {
    try {
      // Convert Map to plain object for JSON serialization
      const statsObject = {};
      for (const [proxy, stats] of this.proxyStats.entries()) {
        statsObject[proxy] = {
          requests: stats.requests,
          successes: stats.successes,
          failures: stats.failures,
          totalResponseTime: stats.totalResponseTime,
          avgResponseTime: stats.avgResponseTime,
          lastUsed: stats.lastUsed ? stats.lastUsed.toISOString() : null
        };
      }

      fs.writeFileSync(this.statsFile, JSON.stringify(statsObject, null, 2));
      console.log(`💾 Saved proxy statistics for ${this.proxyStats.size} proxies to file`);
    } catch (error) {
      console.error('❌ Error saving proxy statistics:', error.message);
    }
  }

  /**
   * Get proxy statistics for display
   */
  getProxyStatistics() {
    const stats = [];
    for (const [proxy, data] of this.proxyStats) {
      const isPermanentlyFailed = this.permanentlyFailedProxies.has(proxy);
      const isActive = this.proxies.includes(proxy);

      // ✅ FIXED: Only show in active list if actually active AND not permanently failed
      if (isActive && !isPermanentlyFailed) {
        stats.push({
          proxy,
          ...data,
          isActive: true,
          isFailed: this.failedProxies.has(proxy),
          isPermanentlyFailed: false
        });
      }
    }
    return stats;
  }

  /**
   * Get permanently failed proxies list
   */
  getPermanentlyFailedProxies() {
    // ✅ FIXED: Include all proxies that have stats but are not in active rotation
    const failedProxies = new Set();

    // Add explicitly permanently failed proxies
    this.permanentlyFailedProxies.forEach(proxy => failedProxies.add(proxy));

    // ✅ FIND ORPHANED PROXIES: Add proxies that have stats but are not in active rotation and not already in permanently failed
    this.proxyStats.forEach((stats, proxy) => {
      const isActive = this.proxies.includes(proxy);
      const isPermanentlyFailed = this.permanentlyFailedProxies.has(proxy);

      // If proxy has stats but is not active and not already marked as permanently failed, it's orphaned
      if (!isActive && !isPermanentlyFailed && stats.failures > 0) {
        console.log(`🔍 Found orphaned failed proxy: ${proxy.split(':')[0]}:${proxy.split(':')[1]} (${stats.failures} failures)`);
        failedProxies.add(proxy);
        // Add to permanently failed list to prevent future orphaning
        this.permanentlyFailedProxies.add(proxy);
      }
    });

    return Array.from(failedProxies).map(proxy => {
      const stats = this.proxyStats.get(proxy) || {
        requests: 0,
        successes: 0,
        failures: 0,
        averageResponseTime: 0,
        lastUsed: null
      };

      return {
        proxy,
        ...stats,
        isActive: false,
        isFailed: true,
        isPermanentlyFailed: true
      };
    });
  }

  /**
   * Diagnose and fix orphaned proxies (proxies with stats but not in any list)
   */
  diagnoseAndFixOrphanedProxies() {
    console.log('🔍 Diagnosing proxy state...');

    const activeProxies = new Set(this.proxies);
    const permanentlyFailed = new Set(this.permanentlyFailedProxies);
    const orphanedProxies = [];

    // Check all proxies with statistics
    this.proxyStats.forEach((stats, proxy) => {
      const isActive = activeProxies.has(proxy);
      const isPermanentlyFailed = permanentlyFailed.has(proxy);

      if (!isActive && !isPermanentlyFailed) {
        orphanedProxies.push({ proxy, stats });
      }
    });

    console.log(`📊 Proxy State Summary:`);
    console.log(`   Active Proxies: ${this.proxies.length}`);
    console.log(`   Permanently Failed: ${this.permanentlyFailedProxies.size}`);
    console.log(`   Orphaned Proxies: ${orphanedProxies.length}`);
    console.log(`   Total with Stats: ${this.proxyStats.size}`);

    if (orphanedProxies.length > 0) {
      console.log(`🔧 Fixing ${orphanedProxies.length} orphaned proxies...`);
      orphanedProxies.forEach(({ proxy, stats }) => {
        // If proxy has failures, mark it as permanently failed
        if (stats.failures > 0) {
          this.permanentlyFailedProxies.add(proxy);
          console.log(`   ✅ Moved to permanently failed: ${proxy.split(':')[0]}:${proxy.split(':')[1]} (${stats.failures} failures)`);
        } else {
          // If no failures, restore to active rotation
          this.proxies.push(proxy);
          console.log(`   ✅ Restored to active: ${proxy.split(':')[0]}:${proxy.split(':')[1]} (no failures)`);
        }
      });

      // Save changes
      this.saveToFile();
      this.saveStatistics();
      console.log(`💾 Saved proxy state changes`);
    }

    return {
      activeProxies: this.proxies.length,
      permanentlyFailed: this.permanentlyFailedProxies.size,
      orphanedFixed: orphanedProxies.length,
      totalWithStats: this.proxyStats.size
    };
  }
}

// Create singleton instance
const proxyManager = new ProxyManager();

module.exports = proxyManager;
