const mysql = require('mysql2/promise');

async function testConnection() {
    console.log('🔍 Testing database connection...');
    
    const dbConfig = {
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'facebook_db',
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0
    };
    
    try {
        const db = mysql.createPool(dbConfig);
        console.log('✅ Database pool created');
        
        // Test connection
        const connection = await db.getConnection();
        console.log('✅ Got database connection');
        
        // Check if database exists
        const [databases] = await connection.execute("SHOW DATABASES LIKE 'facebook_db'");
        console.log('📊 Database check:', databases.length > 0 ? 'EXISTS' : 'NOT FOUND');
        
        if (databases.length > 0) {
            // Check tables
            const [tables] = await connection.execute("SHOW TABLES");
            console.log('📋 Tables found:', tables.length);
            tables.forEach(table => {
                console.log('  - ' + Object.values(table)[0]);
            });
            
            // Check table structures
            const tableNames = ['members', 'pages', 'posts'];
            for (const tableName of tableNames) {
                try {
                    const [columns] = await connection.execute(`DESCRIBE ${tableName}`);
                    console.log(`\n🔧 Table '${tableName}' structure:`);
                    columns.forEach(col => {
                        console.log(`  - ${col.Field} (${col.Type})`);
                    });
                    
                    const [count] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName}`);
                    console.log(`📊 Records in '${tableName}': ${count[0].count}`);
                } catch (err) {
                    console.log(`❌ Table '${tableName}' error:`, err.message);
                }
            }
        }
        
        connection.release();
        await db.end();
        console.log('✅ Test completed');
        
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        console.error('Error code:', error.code);
        console.error('Error number:', error.errno);
    }
}

testConnection();
