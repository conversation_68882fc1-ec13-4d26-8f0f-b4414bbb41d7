// Proxy Helper Functions for Different HTTP Clients
const proxyManager = require('./proxy-manager');
const axios = require('axios');

/**
 * Create an Axios instance with proxy support
 */
function createProxyAxios(customProxy = null) {
  const agent = proxyManager.getProxyAgent(customProxy);
  
  const axiosConfig = {
    timeout: 30000,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
  };

  if (agent) {
    axiosConfig.httpsAgent = agent;
    axiosConfig.httpAgent = agent;
  }

  const axiosInstance = axios.create(axiosConfig);

  // Add request interceptor to track proxy usage
  axiosInstance.interceptors.request.use(
    (config) => {
      config.metadata = { startTime: Date.now() };
      return config;
    },
    (error) => Promise.reject(error)
  );

  // Add response interceptor to update proxy stats
  axiosInstance.interceptors.response.use(
    (response) => {
      const responseTime = Date.now() - response.config.metadata.startTime;
      if (agent && customProxy) {
        // ✅ FIXED: Don't count request again (already counted in getPuppeteerProxyConfig)
        // proxyManager.recordProxyRequest(customProxy); // REMOVED!
        proxyManager.markProxyAsSuccessful(customProxy, responseTime);
      }
      return response;
    },
    (error) => {
      const responseTime = Date.now() - (error.config?.metadata?.startTime || Date.now());
      if (agent && customProxy) {
        // ✅ FIXED: Don't count request again (already counted in getPuppeteerProxyConfig)
        // proxyManager.recordProxyRequest(customProxy); // REMOVED!
        proxyManager.markProxyAsFailed(customProxy);
      }
      return Promise.reject(error);
    }
  );

  return axiosInstance;
}

/**
 * Enhanced fetch function with proxy support
 */
async function proxyFetch(url, options = {}) {
  const agent = proxyManager.getProxyAgent();
  
  if (agent) {
    options.agent = agent;
  }

  // Add default headers
  if (!options.headers) {
    options.headers = {};
  }
  
  if (!options.headers['User-Agent']) {
    options.headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
  }

  const startTime = Date.now();
  
  try {
    const response = await fetch(url, options);
    const responseTime = Date.now() - startTime;
    
    if (agent) {
      const currentProxy = proxyManager.getNextProxy();
      proxyManager.markProxyAsSuccessful(currentProxy, responseTime);
    }
    
    return response;
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    if (agent) {
      const currentProxy = proxyManager.getNextProxy();
      proxyManager.markProxyAsFailed(currentProxy);
    }
    
    throw error;
  }
}

/**
 * Get Puppeteer launch options with proxy
 */
function getPuppeteerProxyOptions(customProxy = null, additionalArgs = []) {
  const proxyConfig = proxyManager.getPuppeteerProxyConfig(customProxy);
  
  const baseArgs = [
    '--no-sandbox',
    '--disable-setuid-sandbox',
    '--disable-infobars',
    '--window-position=0,0',
    '--ignore-certifcate-errors',
    '--ignore-certifcate-errors-spki-list',
    '--disable-background-timer-throttling',
    '--disable-backgrounding-occluded-windows',
    '--disable-renderer-backgrounding',
    ...additionalArgs
  ];

  const launchOptions = {
    args: [...baseArgs, ...(proxyConfig.args || [])],
    defaultViewport: null
  };

  return { launchOptions, proxyAuth: proxyConfig.proxyAuth };
}

/**
 * Set proxy authentication for Puppeteer page
 */
async function setPuppeteerProxyAuth(page, proxyAuth) {
  if (proxyAuth && proxyAuth.username && proxyAuth.password) {
    await page.authenticate({
      username: proxyAuth.username,
      password: proxyAuth.password
    });
  }
}

/**
 * Download image with proxy support
 */
async function downloadImageWithProxy(imageUrl, localPath, customProxy = null) {
  const axiosInstance = createProxyAxios(customProxy);
  
  try {
    console.log(`📥 Downloading with proxy: ${imageUrl.substring(0, 80)}...`);
    
    const response = await axiosInstance({
      method: 'GET',
      url: imageUrl,
      responseType: 'stream',
      timeout: 30000
    });

    const fs = require('fs');
    const writer = fs.createWriteStream(localPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log(`✅ Downloaded with proxy: ${localPath}`);
        resolve(localPath);
      });
      writer.on('error', (error) => {
        console.error(`❌ Error downloading with proxy:`, error.message);
        reject(error);
      });
    });
  } catch (error) {
    console.error(`❌ Proxy download failed for ${imageUrl}:`, error.message);
    throw error;
  }
}



/**
 * Make HTTP request with automatic proxy retry
 */
async function requestWithProxyRetry(url, options = {}, maxRetries = 3) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const axiosInstance = createProxyAxios();
      const response = await axiosInstance(url, options);
      
      console.log(`✅ Request successful on attempt ${attempt}`);
      return response;
    } catch (error) {
      lastError = error;
      console.warn(`⚠️ Request attempt ${attempt} failed:`, error.message);
      
      if (attempt < maxRetries) {
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
  }
  
  console.error(`❌ All ${maxRetries} attempts failed`);
  throw lastError;
}

/**
 * Test proxy connectivity
 */
async function testProxyConnectivity() {
  console.log('🧪 Testing proxy connectivity...');
  
  try {
    const axiosInstance = createProxyAxios();
    const response = await axiosInstance.get('https://httpbin.org/ip', { timeout: 10000 });
    
    console.log('✅ Proxy connectivity test successful');
    console.log('📍 Current IP:', response.data.origin);
    return { success: true, ip: response.data.origin };
  } catch (error) {
    console.error('❌ Proxy connectivity test failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Get current proxy information
 */
function getCurrentProxyInfo() {
  const currentProxy = proxyManager.getNextProxy();
  if (!currentProxy) {
    return null;
  }
  
  const parts = currentProxy.split(':');
  return {
    host: parts[0],
    port: parts[1],
    username: parts[2],
    masked: `${parts[0]}:${parts[1]}:${parts[2]}:****`
  };
}

/**
 * Rotate to next proxy manually
 */
function rotateProxy() {
  const nextProxy = proxyManager.getNextProxy();
  console.log(`🔄 Rotated to next proxy: ${nextProxy ? nextProxy.split(':')[0] + ':' + nextProxy.split(':')[1] : 'none'}`);
  return nextProxy;
}

module.exports = {
  createProxyAxios,
  proxyFetch,
  getPuppeteerProxyOptions,
  setPuppeteerProxyAuth,
  downloadImageWithProxy,
  requestWithProxyRetry,
  testProxyConnectivity,
  getCurrentProxyInfo,
  rotateProxy,
  proxyManager
};
