const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose ipc<PERSON>enderer to the window object
window.ipcRenderer = ipcRenderer;

// Listen for start/stop scraper events from main process
ipcRenderer.on('start-scraper', () => {
  // Call the startScraper function in the renderer
  if (window.startScraper) {
    window.startScraper();
  }
});

ipcRenderer.on('stop-scraper', () => {
  // Call the stopScraper function in the renderer
  if (window.stopScraper) {
    window.stopScraper();
  }
}); 