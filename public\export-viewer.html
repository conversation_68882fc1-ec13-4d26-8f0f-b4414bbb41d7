<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Viewer</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --success-color: #4CAF50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f1f5f9;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-accent: #667eea;
            --border-color: #e2e8f0;
            --shadow-light: rgba(0, 0, 0, 0.1);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-accent: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .dark-mode {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f1f5f9;
            --text-secondary: #cbd5e1;
            --border-color: #475569;
            --shadow-light: rgba(0, 0, 0, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            transition: all 0.3s ease;
        }

        .container {
            max-width: 100%;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            background: var(--bg-secondary);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px var(--shadow-light);
            border: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .header h1 {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.8rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--shadow-light);
        }

        .theme-toggle {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 10px 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            color: var(--text-primary);
        }

        .theme-toggle:hover {
            background: var(--accent-color);
            color: white;
        }

        .file-info {
            background: var(--bg-secondary);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px var(--shadow-light);
            border: 1px solid var(--border-color);
        }

        .file-info h2 {
            color: var(--text-primary);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            background: var(--bg-tertiary);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid var(--border-color);
        }

        .info-label {
            font-size: 0.85rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .content-viewer {
            background: var(--bg-secondary);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px var(--shadow-light);
            border: 1px solid var(--border-color);
            min-height: 400px;
        }

        /* AI Chat Styles */
        .ai-chat-section {
            background: var(--bg-secondary);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px var(--shadow-light);
            border: 1px solid var(--border-color);
            max-height: 600px;
            display: flex;
            flex-direction: column;
        }

        .ai-chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .ai-chat-header h2 {
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 1.3rem;
        }

        .ai-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .language-toggle {
            display: flex;
            background: var(--bg-tertiary);
            border-radius: 20px;
            padding: 2px;
            border: 1px solid var(--border-color);
        }

        .lang-btn {
            background: transparent;
            border: none;
            padding: 6px 12px;
            border-radius: 18px;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--text-secondary);
        }

        .lang-btn.active {
            background: var(--accent-color);
            color: white;
            box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
        }

        .lang-btn:hover:not(.active) {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .ai-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ai-status-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            background: #10b981;
            color: white;
        }

        .ai-status-indicator.thinking {
            background: #f59e0b;
            animation: pulse 1.5s infinite;
        }

        .ai-status-indicator.error {
            background: #ef4444;
        }

        .btn-clear-chat {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-secondary);
            padding: 6px 10px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-clear-chat:hover {
            background: var(--error-color);
            color: white;
        }

        .ai-chat-container {
            display: flex;
            flex-direction: column;
            height: 450px;
        }

        .ai-chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px 0;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .ai-message {
            display: flex;
            gap: 12px;
            align-items: flex-start;
        }

        .user-message {
            flex-direction: row-reverse;
        }

        .ai-avatar, .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .ai-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .user-avatar {
            background: var(--accent-color);
            color: white;
        }

        .ai-message-content, .user-message-content {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            line-height: 1.5;
        }

        .ai-message-content {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        .user-message-content {
            background: var(--accent-color);
            color: white;
        }

        .ai-welcome {
            border: 2px dashed var(--border-color);
            padding: 15px;
            border-radius: 12px;
            background: var(--bg-tertiary);
        }

        .ai-quick-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 12px;
        }

        .quick-action-btn {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quick-action-btn:hover {
            background: var(--accent-color);
            color: white;
            transform: translateY(-1px);
        }

        .ai-chat-input-container {
            border-top: 1px solid var(--border-color);
            padding-top: 15px;
        }

        .ai-chat-input-wrapper {
            display: flex;
            gap: 10px;
            align-items: center;
            position: relative;
            z-index: 100;
        }

        #ai-chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 25px;
            background: var(--bg-tertiary) !important;
            color: var(--text-primary) !important;
            font-size: 0.95rem;
            outline: none;
            transition: all 0.2s ease;
            pointer-events: auto !important;
            user-select: text !important;
            -webkit-user-select: text !important;
            -moz-user-select: text !important;
            -ms-user-select: text !important;
            cursor: text !important;
            z-index: 1000;
            position: relative;
            opacity: 1 !important;
            visibility: visible !important;
        }

        #ai-chat-input:disabled {
            opacity: 0.5 !important;
            cursor: not-allowed !important;
        }

        #ai-chat-input:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        #ai-send-btn {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            border: none;
            background: var(--accent-color);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        #ai-send-btn:hover:not(:disabled) {
            background: #5a67d8;
            transform: scale(1.05);
        }

        #ai-send-btn:disabled {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            cursor: not-allowed;
        }

        .ai-input-info {
            margin-top: 8px;
            text-align: center;
        }

        .ai-input-info small {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        /* Chat scrollbar styling */
        .ai-chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .ai-chat-messages::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
            border-radius: 3px;
        }

        .ai-chat-messages::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        .ai-chat-messages::-webkit-scrollbar-thumb:hover {
            background: var(--text-secondary);
        }

        /* Typing indicator animation */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Mobile responsiveness for chat */
        @media (max-width: 768px) {
            .ai-chat-section {
                max-height: 500px;
                padding: 15px;
            }

            .ai-chat-container {
                height: 400px;
            }

            .ai-message-content, .user-message-content {
                max-width: 90%;
                padding: 10px 14px;
                font-size: 0.9rem;
            }

            .ai-avatar, .user-avatar {
                width: 32px;
                height: 32px;
                font-size: 0.9rem;
            }

            .quick-action-btn {
                font-size: 0.8rem;
                padding: 5px 10px;
            }

            #ai-chat-input {
                font-size: 0.9rem;
                padding: 10px 14px;
            }

            #ai-send-btn {
                width: 40px;
                height: 40px;
            }
        }

        /* Arabic RTL Support - Only for chat content, not header */
        .ai-chat-header {
            direction: ltr !important;
        }

        .ai-controls {
            direction: ltr !important;
        }

        .language-toggle {
            direction: ltr !important;
        }

        /* RTL only for chat messages and input */
        .ai-chat-section.rtl .ai-chat-container {
            direction: rtl;
        }

        .ai-chat-section.rtl .ai-message {
            flex-direction: row-reverse;
        }

        .ai-chat-section.rtl .user-message {
            flex-direction: row;
        }

        .ai-chat-section.rtl .ai-chat-input-wrapper {
            direction: rtl;
        }

        .ai-chat-section.rtl #ai-chat-input {
            text-align: right;
        }

        .ai-chat-section.rtl .ai-quick-actions {
            direction: rtl;
        }

        .ai-loading {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .ai-loading i {
            font-size: 2rem;
            margin-bottom: 15px;
            animation: spin 1s linear infinite;
            color: var(--accent-color);
        }

        .ai-analysis-result {
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 1rem;
        }

        .ai-analysis-result h3 {
            color: var(--text-accent);
            margin: 25px 0 15px 0;
            font-size: 1.3rem;
            font-weight: 700;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 8px;
        }

        .ai-analysis-result h4 {
            color: var(--text-primary);
            margin: 20px 0 12px 0;
            font-size: 1.1rem;
            font-weight: 600;
            background: var(--bg-secondary);
            padding: 10px 15px;
            border-radius: 8px;
            border-left: 4px solid var(--accent-color);
        }

        .ai-analysis-result ul {
            margin: 15px 0;
            padding-left: 0;
            list-style: none;
        }

        .ai-analysis-result ol {
            margin: 15px 0;
            padding-left: 20px;
        }

        .ai-analysis-result li {
            margin-bottom: 8px;
            padding: 8px 12px;
            background: var(--bg-secondary);
            border-radius: 6px;
            border-left: 3px solid var(--border-color);
            position: relative;
        }

        .ai-analysis-result ol li {
            background: transparent;
            border-left: none;
            padding: 4px 0;
        }

        .ai-analysis-result li:hover {
            border-left-color: var(--accent-color);
            background: var(--bg-tertiary);
        }

        .ai-analysis-result strong {
            color: var(--text-accent);
            font-weight: 600;
        }

        .ai-analysis-result p {
            margin: 12px 0;
            text-align: justify;
        }

        .ai-analysis-result hr {
            margin: 25px 0;
            border: none;
            height: 2px;
            background: linear-gradient(90deg, var(--accent-color), transparent);
            border-radius: 1px;
        }

        .ai-analysis-result code {
            background: var(--bg-tertiary);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            border: 1px solid var(--border-color);
        }

        /* Emoji styling */
        .ai-analysis-result span[style*="font-size: 1.1em"] {
            margin-right: 6px;
            display: inline-block;
        }

        .ai-error {
            text-align: center;
            padding: 40px 20px;
            color: var(--error-color);
        }

        .ai-error i {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .ai-placeholder {
            text-align: center;
            padding: 40px 20px;
            color: var(--text-secondary);
        }

        .ai-placeholder i {
            font-size: 2rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .content-viewer h2 {
            color: var(--text-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .loading {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .loading i {
            font-size: 3rem;
            margin-bottom: 20px;
            animation: spin 1s linear infinite;
            color: var(--accent-color);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            text-align: center;
            padding: 60px 20px;
            color: var(--error-color);
        }

        .error i {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        /* JSON Viewer Styles */
        .json-viewer {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            max-height: 600px;
            overflow-y: auto;
        }

        /* JSON Syntax Highlighting */
        .json-key {
            color: #9cdcfe;
        }

        .json-string {
            color: #ce9178;
        }

        .json-number {
            color: #b5cea8;
        }

        .json-boolean {
            color: #569cd6;
        }

        .json-null {
            color: #569cd6;
        }

        /* Animation keyframes */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* CSV Table Styles */
        .csv-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
            background: var(--bg-secondary);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 16px var(--shadow-light);
        }

        .csv-table th,
        .csv-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        .csv-table th {
            background: var(--bg-tertiary);
            font-weight: 600;
            color: var(--text-primary);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.85rem;
        }

        .csv-table tr:hover {
            background: var(--bg-tertiary);
        }

        .csv-table td {
            color: var(--text-secondary);
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .table-container {
            max-height: 600px;
            overflow-y: auto;
            border-radius: 10px;
            border: 1px solid var(--border-color);
        }

        /* Enhanced responsive design */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header {
                flex-direction: column;
                text-align: center;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .csv-table {
                font-size: 0.8rem;
            }

            .csv-table th,
            .csv-table td {
                padding: 8px;
                max-width: 150px;
            }

            .json-viewer {
                font-size: 0.8rem;
                padding: 15px;
            }

            .btn {
                padding: 8px 12px;
                font-size: 0.8rem;
            }
        }

        /* Scrollbar styling */
        .json-viewer::-webkit-scrollbar,
        .table-container::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .json-viewer::-webkit-scrollbar-track,
        .table-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .json-viewer::-webkit-scrollbar-thumb,
        .table-container::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
        }

        .json-viewer::-webkit-scrollbar-thumb:hover,
        .table-container::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        /* Enhanced loading animation */
        .loading {
            background: var(--bg-secondary);
            border-radius: 15px;
            border: 1px solid var(--border-color);
        }

        .loading i {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Pulse animation for loading */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .loading p {
            animation: pulse 2s infinite;
        }

        /* Enhanced Posts Interface Styles */
        .posts-interface {
            max-width: 100%;
        }

        /* Member Header */
        .member-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 25px;
            background: var(--bg-secondary);
            border-radius: 15px;
            border: 1px solid var(--border-color);
            box-shadow: 0 4px 16px var(--shadow-light);
        }

        .member-info h2 {
            color: var(--text-primary);
            margin: 0 0 5px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .member-role {
            color: var(--text-secondary);
            margin: 0;
            font-size: 0.9rem;
        }

        .export-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 8px;
        }

        .export-date {
            color: var(--text-secondary);
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .total-engagement-badge {
            background: var(--gradient-primary);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        /* Analytics Dashboard */
        .analytics-dashboard {
            margin-bottom: 30px;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .analytics-card {
            background: var(--bg-secondary);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px var(--shadow-light);
        }

        .analytics-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px var(--shadow-light);
        }

        .analytics-card.primary .card-icon { background: var(--gradient-primary); }
        .analytics-card.success .card-icon { background: linear-gradient(135deg, #28a745, #20c997); }
        .analytics-card.info .card-icon { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
        .analytics-card.warning .card-icon { background: linear-gradient(135deg, #ffc107, #fd7e14); }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .card-content {
            flex: 1;
        }

        .card-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
        }

        .card-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        /* Insights Section */
        .insights-section {
            margin-bottom: 30px;
        }

        .insights-section h3 {
            color: var(--text-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .insight-card {
            background: var(--bg-tertiary);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid var(--border-color);
        }

        .insight-title {
            font-size: 0.9rem;
            color: var(--text-secondary);
            margin-bottom: 10px;
            font-weight: 600;
        }

        .insight-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            line-height: 1;
        }

        .insight-label {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        .insight-date {
            font-size: 0.75rem;
            color: var(--text-accent);
            margin-top: 5px;
        }

        .content-breakdown {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }

        .content-type {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 0.85rem;
            color: var(--text-primary);
        }

        .type-icon {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: white;
        }

        .type-icon.video { background: #e74c3c; }
        .type-icon.photo { background: #3498db; }
        .type-icon.text { background: #95a5a6; }

        /* Controls Section */
        .controls-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding: 20px;
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            flex-wrap: wrap;
            gap: 15px;
        }

        .filters {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-size: 0.85rem;
            color: var(--text-secondary);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .filter-group select {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-tertiary);
            color: var(--text-primary);
            font-size: 0.9rem;
            min-width: 150px;
        }

        .view-controls {
            display: flex;
            gap: 10px;
        }

        .btn-view {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: var(--bg-tertiary);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 0.85rem;
        }

        .btn-view:hover {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        .btn-view.active {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        /* Posts Container */
        .posts-container {
            margin-bottom: 30px;
        }

        .posts-container.cards-view {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 25px;
        }

        .posts-container.list-view {
            display: flex;
            flex-direction: column;
            gap: 0;
        }

        /* Enhanced Post Cards */
        .enhanced-post-card {
            background: var(--bg-secondary);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px var(--shadow-light);
        }

        .enhanced-post-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 32px var(--shadow-light);
        }

        .post-header-enhanced {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .post-type-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .post-type-badge.type-video { background: #e74c3c; }
        .post-type-badge.type-photo { background: #3498db; }
        .post-type-badge.type-text { background: #95a5a6; }

        .post-date-enhanced {
            color: var(--text-secondary);
            font-size: 0.85rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .engagement-score {
            text-align: center;
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 8px 12px;
        }

        .score-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--text-accent);
            line-height: 1;
        }

        .score-label {
            font-size: 0.7rem;
            color: var(--text-secondary);
            margin-top: 2px;
        }

        .post-content-enhanced {
            margin-bottom: 20px;
        }

        .post-text-preview,
        .post-text-full {
            color: var(--text-primary);
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .read-more-btn,
        .read-less-btn {
            background: none;
            border: none;
            color: var(--text-accent);
            cursor: pointer;
            font-size: 0.85rem;
            padding: 5px 0;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .video-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-accent);
            font-size: 0.9rem;
            background: var(--bg-tertiary);
            padding: 8px 12px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .engagement-details {
            margin-bottom: 15px;
        }

        .engagement-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .engagement-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            flex: 1;
        }

        .engagement-item i {
            font-size: 1.1rem;
        }

        .engagement-item.likes i { color: #e74c3c; }
        .engagement-item.comments i { color: #3498db; }
        .engagement-item.shares i { color: #f39c12; }
        .engagement-item.views i { color: #27ae60; }

        .engagement-item .count {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .engagement-item .label {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .engagement-bar {
            height: 6px;
            background: var(--bg-tertiary);
            border-radius: 3px;
            overflow: hidden;
            display: flex;
        }

        .bar-segment {
            height: 100%;
            transition: width 0.5s ease;
        }

        .bar-segment.likes { background: #e74c3c; }
        .bar-segment.comments { background: #3498db; }
        .bar-segment.shares { background: #f39c12; }
        .bar-segment.views { background: #27ae60; }

        .post-footer-enhanced {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
        }

        .page-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--text-secondary);
            font-size: 0.85rem;
        }

        .page-name {
            font-weight: 500;
        }

        .post-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 6px;
            cursor: pointer;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        .enhanced-post-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
        }

        .simple-post-card:hover {
            box-shadow: 0 4px 16px var(--shadow-light);
        }

        .post-type-indicator {
            position: absolute;
            top: 15px;
            left: 15px;
            background: #2196F3;
            color: white;
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .post-date {
            position: absolute;
            top: 15px;
            left: 120px;
            color: var(--text-secondary);
            font-size: 0.85rem;
        }

        .engagement-badge {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #e91e63;
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .post-content-text {
            margin-top: 50px;
            margin-bottom: 15px;
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 1rem;
        }

        .video-duration {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--text-accent);
            font-size: 0.85rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .engagement-metrics {
            display: flex;
            gap: 25px;
            margin-bottom: 15px;
            padding: 10px 0;
            border-top: 1px solid var(--border-color);
        }

        .metric {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        .metric i {
            color: var(--text-accent);
            width: 16px;
        }

        .page-name {
            display: flex;
            align-items: center;
            gap: 6px;
            color: var(--text-secondary);
            font-size: 0.85rem;
            border-top: 1px solid var(--border-color);
            padding-top: 10px;
        }

        .no-posts {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .no-posts i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        /* List View Styles */
        .list-header {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 100px;
            gap: 15px;
            padding: 15px 20px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            margin-bottom: 10px;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.85rem;
        }

        .list-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr 100px;
            gap: 15px;
            padding: 15px 20px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            align-items: center;
        }

        .list-row:hover {
            background: var(--bg-tertiary);
            transform: translateX(5px);
        }

        .content-col {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .post-type-mini {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: white;
            flex-shrink: 0;
        }

        .post-type-mini.type-video { background: #e74c3c; }
        .post-type-mini.type-photo { background: #3498db; }
        .post-type-mini.type-text { background: #95a5a6; }

        .content-preview {
            color: var(--text-primary);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .page-col,
        .date-col {
            color: var(--text-secondary);
            font-size: 0.85rem;
        }

        .engagement-mini {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .mini-stat {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }

        .action-btn-mini {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }

        .action-btn-mini:hover {
            background: var(--accent-color);
            color: white;
        }

        /* Pagination Styles */
        .pagination-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            margin-top: 30px;
        }

        .pagination {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
        }

        .page-btn:hover {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        .page-btn.active {
            background: var(--accent-color);
            color: white;
            border-color: var(--accent-color);
        }

        .page-dots {
            color: var(--text-secondary);
            padding: 0 5px;
        }

        .page-info {
            color: var(--text-secondary);
            font-size: 0.85rem;
        }

        /* Post Detail Modal */
        .post-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .modal-content-detailed {
            background: var(--bg-secondary);
            border-radius: 20px;
            max-width: 900px;
            max-height: 90vh;
            width: 90%;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            animation: slideUp 0.3s ease;
        }

        .modal-header-detailed {
            padding: 25px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header-detailed h3 {
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-body-detailed {
            padding: 25px;
        }

        .post-detail-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-bottom: 25px;
        }

        .detail-section {
            background: var(--bg-tertiary);
            border-radius: 12px;
            padding: 20px;
        }

        .detail-section.full-width {
            grid-column: 1 / -1;
        }

        .detail-section h4 {
            color: var(--text-primary);
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1rem;
        }

        .detail-item {
            margin-bottom: 10px;
            color: var(--text-primary);
            font-size: 0.9rem;
        }

        .detail-item strong {
            color: var(--text-accent);
            margin-right: 8px;
        }

        .engagement-chart {
            display: flex;
            gap: 15px;
            align-items: end;
            height: 100px;
            margin-bottom: 15px;
        }

        .chart-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .chart-bar {
            width: 100%;
            min-height: 20px;
            border-radius: 4px 4px 0 0;
            transition: height 0.5s ease;
        }

        .chart-bar.likes { background: #e74c3c; }
        .chart-bar.comments { background: #3498db; }
        .chart-bar.shares { background: #f39c12; }
        .chart-bar.views { background: #27ae60; }

        .chart-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-align: center;
            line-height: 1.2;
        }

        .engagement-score-detail {
            color: var(--text-primary);
            font-size: 0.9rem;
            padding: 10px;
            background: var(--bg-secondary);
            border-radius: 8px;
        }

        .full-content {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 20px;
            color: var(--text-primary);
            line-height: 1.6;
            white-space: pre-wrap;
        }

        /* Modal Styles */
        .post-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }

        .modal-content {
            background: var(--bg-secondary);
            border-radius: 20px;
            max-width: 800px;
            max-height: 90vh;
            width: 90%;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            animation: slideUp 0.3s ease;
        }

        .modal-header {
            padding: 25px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            color: var(--text-primary);
            margin: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .modal-body {
            padding: 25px;
        }

        .post-full-text {
            color: var(--text-primary);
            line-height: 1.7;
            margin-bottom: 25px;
            font-size: 1.1rem;
        }

        .post-full-meta {
            background: var(--bg-tertiary);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .meta-row {
            display: flex;
            margin-bottom: 10px;
            color: var(--text-primary);
        }

        .meta-row:last-child {
            margin-bottom: 0;
        }

        .meta-row strong {
            min-width: 140px;
            color: var(--text-accent);
        }

        .engagement-breakdown h4 {
            color: var(--text-primary);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .engagement-bars {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .engagement-bar {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .bar-label {
            min-width: 120px;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .bar-container {
            flex: 1;
            height: 8px;
            background: var(--bg-tertiary);
            border-radius: 4px;
            overflow: hidden;
        }

        .bar-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .bar-fill.likes {
            background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
        }

        .bar-fill.comments {
            background: linear-gradient(90deg, #4ecdc4, #6ee7df);
        }

        .bar-fill.shares {
            background: linear-gradient(90deg, #45b7d1, #6cc5e0);
        }

        .bar-fill.views {
            background: linear-gradient(90deg, #96ceb4, #a8d8c4);
        }

        .bar-value {
            min-width: 60px;
            text-align: right;
            font-weight: 600;
            color: var(--text-primary);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                transform: translateY(50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Enhanced responsive design */
        @media (max-width: 768px) {
            .member-header {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
                text-align: center;
            }

            .export-info {
                align-items: center;
            }

            .analytics-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .insights-grid {
                grid-template-columns: 1fr;
            }

            .controls-section {
                flex-direction: column;
                align-items: stretch;
            }

            .filters {
                justify-content: center;
                flex-wrap: wrap;
            }

            .view-controls {
                justify-content: center;
            }

            .posts-container.cards-view {
                grid-template-columns: 1fr;
            }

            .list-header,
            .list-row {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .list-header {
                display: none; /* Hide header on mobile for better UX */
            }

            .list-row {
                display: flex;
                flex-direction: column;
                align-items: stretch;
                padding: 15px;
            }

            .content-col {
                order: 1;
            }

            .page-col,
            .date-col,
            .engagement-col {
                order: 2;
                display: flex;
                justify-content: space-between;
                font-size: 0.8rem;
                padding: 5px 0;
                border-top: 1px solid var(--border-color);
            }

            .page-col::before { content: "Page: "; font-weight: 600; }
            .date-col::before { content: "Date: "; font-weight: 600; }
            .engagement-col::before { content: "Engagement: "; font-weight: 600; }

            .actions-col {
                order: 3;
                text-align: center;
                padding-top: 10px;
            }

            .post-detail-grid {
                grid-template-columns: 1fr;
            }

            .modal-content-detailed {
                width: 95%;
                margin: 20px;
            }

            .modal-header-detailed,
            .modal-body-detailed {
                padding: 20px;
            }

            .engagement-chart {
                height: 80px;
                gap: 10px;
            }

            .pagination {
                flex-wrap: wrap;
                justify-content: center;
            }

            .filter-group {
                min-width: 140px;
            }

            .content-breakdown {
                justify-content: center;
                flex-wrap: wrap;
            }
        }

        @media (max-width: 480px) {
            .analytics-grid {
                grid-template-columns: 1fr;
            }

            .filters {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group select {
                min-width: auto;
            }

            .engagement-row {
                flex-wrap: wrap;
                gap: 10px;
            }

            .engagement-item {
                min-width: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-file-alt"></i>
                Export Viewer
            </h1>
            <div class="header-actions">
                <div class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
                    <i class="fas fa-moon" id="theme-icon"></i>
                    <span id="theme-text">Dark</span>
                </div>
                <button class="btn btn-secondary" onclick="window.close()">
                    <i class="fas fa-times"></i>
                    Close
                </button>
            </div>
        </div>

        <div class="file-info">
            <h2><i class="fas fa-info-circle"></i> File Information</h2>
            <div class="info-grid" id="file-info-grid">
                <!-- File info will be populated here -->
            </div>
        </div>

        <!-- AI Chat Assistant Section -->
        <div class="ai-chat-section" id="ai-chat-section" style="display: none;">
            <div class="ai-chat-header" style="direction: ltr;">
                <h2><i class="fas fa-robot"></i> <span id="ai-title">AI Assistant</span></h2>
                <div class="ai-controls" style="direction: ltr;">
                    <div class="language-toggle" style="direction: ltr;">
                        <button class="lang-btn active" id="lang-en" onclick="setLanguage('en')" title="English">
                            🇺🇸 EN
                        </button>
                        <button class="lang-btn" id="lang-ar" onclick="setLanguage('ar')" title="العربية">
                            عربي
                        </button>
                    </div>
                    <div class="ai-status">
                        <span class="ai-status-indicator" id="ai-status">Ready</span>
                        <button class="btn-clear-chat" id="clear-chat-btn" onclick="clearChatHistory()" title="Clear Chat">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="ai-chat-container">
                <div class="ai-chat-messages" id="ai-chat-messages">
                    <div class="ai-message ai-welcome" id="ai-welcome-message">
                        <div class="ai-avatar">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div class="ai-message-content">
                            <p id="welcome-text">👋 Hi! I'm your AI assistant. I can help you analyze your Facebook posts data.</p>
                            <div class="ai-quick-actions" id="quick-actions">
                                <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('analyze_performance'))">
                                    <span id="btn-analyze">📊 Analyze Performance</span>
                                </button>
                                <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('content_tips'))">
                                    <span id="btn-tips">💡 Content Tips</span>
                                </button>
                                <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('best_posts'))">
                                    <span id="btn-best">🏆 Best Posts</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ai-chat-input-container">
                    <div class="ai-chat-input-wrapper">
                        <input type="text" id="ai-chat-input" placeholder="Ask me anything about your posts..." maxlength="500">
                        <button id="ai-send-btn" onclick="sendChatMessage()" disabled>
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="ai-input-info">
                        <small id="input-hint">💡 Try asking: "How can I improve engagement?" or "What's my best performing content?"</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="content-viewer">
            <h2><i class="fas fa-eye"></i> Content Preview</h2>
            <div id="content-container">
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    <p>Loading file content...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="export-viewer.js"></script>
</body>
</html>
