<?php
header('Content-Type: application/json');

$response = ['debug' => [], 'success' => false];

try {
    // Check if files exist
    $files = [
        'team_members.json' => 'team_members.json',
        'pages.json' => 'pages.json', 
        'final_filtered_posts.json' => 'final_filtered_posts.json'
    ];
    
    foreach ($files as $name => $file) {
        if (file_exists($file)) {
            $size = filesize($file);
            $response['debug'][] = "✅ $name exists ($size bytes)";
            
            $content = file_get_contents($file);
            $data = json_decode($content, true);
            if ($data) {
                $response['debug'][] = "✅ $name JSON valid (" . count($data) . " items)";
                if ($name === 'final_filtered_posts.json' && count($data) > 0) {
                    $first = $data[0];
                    $response['debug'][] = "First post keys: " . implode(', ', array_keys($first));
                    if (isset($first['engagement'])) {
                        $response['debug'][] = "Engagement keys: " . implode(', ', array_keys($first['engagement']));
                    }
                }
            } else {
                $response['debug'][] = "❌ $name JSON invalid";
            }
        } else {
            $response['debug'][] = "❌ $name missing";
        }
    }
    
    // Test database connection
    $conn = new mysqli("localhost", "root", "", "facebook_db");
    if ($conn->connect_error) {
        $response['debug'][] = "❌ Database connection failed: " . $conn->connect_error;
    } else {
        $response['debug'][] = "✅ Database connected";
        
        // Try to insert one test post
        if (file_exists('final_filtered_posts.json')) {
            $posts = json_decode(file_get_contents('final_filtered_posts.json'), true);
            if ($posts && count($posts) > 0) {
                $post = $posts[0];
                $response['debug'][] = "Testing with first post...";
                
                $id = $post['normalizedTextHash'] ?? 'test_post';
                $caption = $post['finalFilteredText'] ?? '';
                $likes = $post['engagement']['likes'] ?? 0;
                $comments = $post['engagement']['comments'] ?? 0;
                $shares = $post['engagement']['shares'] ?? 0;
                $postUrl = $post['postUrl'] ?? '';
                $pageUrl = $post['pageUrl'] ?? '';
                $pageName = $post['pageName'] ?? '';
                $timestamp = $post['timestamp'] ?? null;
                
                $response['debug'][] = "Post data: ID=$id, Likes=$likes, Comments=$comments, Shares=$shares";
                
                $sql = "INSERT INTO posts (id, caption, likes_count, comments_count, shares_count, post_url, page_url, page_name, extraction_timestamp) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($sql);
                
                if ($stmt) {
                    $stmt->bind_param("ssiiiisss", $id, $caption, $likes, $comments, $shares, $postUrl, $pageUrl, $pageName, $timestamp);
                    if ($stmt->execute()) {
                        $response['debug'][] = "✅ Test post inserted successfully";
                        $response['success'] = true;
                    } else {
                        $response['debug'][] = "❌ Insert failed: " . $stmt->error;
                    }
                } else {
                    $response['debug'][] = "❌ Prepare failed: " . $conn->error;
                }
            }
        }
    }
    
} catch (Exception $e) {
    $response['debug'][] = "❌ Exception: " . $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
