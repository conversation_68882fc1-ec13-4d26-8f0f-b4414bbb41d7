<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Time Display Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .time-display {
            font-size: 16px;
            color: #1877f2;
            font-weight: bold;
            margin: 10px 0;
        }
        .original-time {
            color: #666;
            font-size: 14px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #1877f2;
            border-bottom: 2px solid #1877f2;
            padding-bottom: 5px;
        }
        .test-result {
            background: #e8f5e8;
            padding: 10px;
            border-left: 4px solid #4caf50;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Enhanced Post Time Display Test</h1>
    
    <div class="test-container">
        <h2>How It Works</h2>
        <p><strong>Current Time:</strong> <span id="current-time"></span></p>
        <p>The system takes relative times like "2h" or "25m" and subtracts them from the current time to calculate when the post was actually made.</p>

        <div class="test-result" style="background: #e3f2fd; border-left-color: #2196f3;">
            <strong>Example:</strong><br>
            • If it's currently <strong>8:00 PM</strong> and post shows <strong>"2h"</strong><br>
            • System calculates: 8:00 PM - 2 hours = <strong>6:00 PM</strong><br>
            • Display shows: <strong>"2h (Today at 6:00 PM)"</strong>
        </div>
    </div>

    <div class="test-container">
        <h2>Live Test Cases</h2>
        <p>Testing with current time - watch how the calculated times change as current time changes:</p>

        <div id="test-results"></div>
    </div>

    <script>
        // Enhanced function to calculate actual post time with full date information
        function calculateActualPostTime(relativeTime) {
            if (!relativeTime) return null;
            
            // Handle different relative time formats
            let match = relativeTime.match(/^(\d+)([smhd])$/i);
            if (!match) {
                // Try to match longer formats like "25 minutes ago", "2 hours ago"
                match = relativeTime.match(/(\d+)\s*(second|minute|hour|day)s?\s*ago/i);
                if (match) {
                    const unit = match[2].toLowerCase().charAt(0); // Get first letter (s, m, h, d)
                    match = [match[0], match[1], unit];
                }
            }
            
            if (!match) return null;
            
            const value = parseInt(match[1]);
            const unit = match[2].toLowerCase();
            
            // Create a date object for the current time
            const now = new Date();
            const postDate = new Date(now);
            
            // Subtract the appropriate amount of time based on the unit
            switch (unit) {
                case 's': // seconds
                    postDate.setSeconds(now.getSeconds() - value);
                    break;
                case 'm': // minutes
                    postDate.setMinutes(now.getMinutes() - value);
                    break;
                case 'h': // hours
                    postDate.setHours(now.getHours() - value);
                    break;
                case 'd': // days
                    postDate.setDate(now.getDate() - value);
                    break;
                default:
                    return null;
            }
            
            return {
                fullDate: postDate,
                timeString: postDate.toLocaleTimeString('en-US', { 
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true 
                }),
                dateString: postDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                }),
                isToday: isToday(postDate),
                isoString: postDate.toISOString()
            };
        }

        // Helper function to check if a date is today
        function isToday(date) {
            const today = new Date();
            return date.getDate() === today.getDate() &&
                   date.getMonth() === today.getMonth() &&
                   date.getFullYear() === today.getFullYear();
        }

        // Function to format post time for display - shows both relative and exact time
        function formatPostTimeDisplay(postTime) {
            if (!postTime) return 'Unknown';
            
            const actualTime = calculateActualPostTime(postTime);
            
            if (!actualTime) return postTime; // Return original if can't parse
            
            // Show both relative time and exact time
            if (actualTime.isToday) {
                return `${postTime} (Today at ${actualTime.timeString})`;
            } else {
                return `${postTime} (${actualTime.dateString} at ${actualTime.timeString})`;
            }
        }

        // Test cases
        const testCases = [
            '25m',
            '2h',
            '1h',
            '4d',
            '30s',
            '132h',
            '6559m',
            '07m',
            '02m'
        ];

        // Update current time display
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }

        // Run tests and display results
        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = ''; // Clear previous results

            const now = new Date();

            testCases.forEach(testCase => {
                const result = formatPostTimeDisplay(testCase);
                const actualTime = calculateActualPostTime(testCase);

                const testDiv = document.createElement('div');
                testDiv.className = 'test-result';

                let calculationDetails = '';
                if (actualTime) {
                    const timeDiff = now.getTime() - actualTime.fullDate.getTime();
                    const diffMinutes = Math.round(timeDiff / (1000 * 60));
                    calculationDetails = `
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">
                            <strong>Calculation:</strong> Current time (${now.toLocaleTimeString()}) - ${testCase} = ${actualTime.fullDate.toLocaleTimeString()}<br>
                            <strong>Verification:</strong> ${diffMinutes} minutes ago ≈ ${testCase}
                        </div>
                    `;
                }

                testDiv.innerHTML = `
                    <div class="original-time">Facebook shows: <strong>${testCase}</strong></div>
                    <div class="time-display">Our calculation: <strong>${result}</strong></div>
                    ${calculationDetails}
                `;

                resultsContainer.appendChild(testDiv);
            });
        }

        // Run tests when page loads and update every second
        window.onload = function() {
            updateCurrentTime();
            runTests();

            // Update current time every second
            setInterval(() => {
                updateCurrentTime();
                runTests(); // Re-run tests to show live calculations
            }, 1000);
        };
    </script>
</body>
</html>
