# Facebook Posts Desktop Application - Usage Guide

This document provides detailed instructions on how to set up, run, and use the Facebook Posts Desktop Application.

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Installation](#installation)
3. [Running the Application](#running-the-application)
4. [Building a Standalone Application](#building-a-standalone-application)
5. [Using the Application](#using-the-application)
6. [System Tray Features](#system-tray-features)
7. [Customization](#customization)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

Before you begin, ensure you have the following installed:
- [Node.js](https://nodejs.org/) (version 14.x or higher)
- npm (comes with Node.js)
- Git (optional, for cloning the repository)

## Installation

1. Clone or download the repository:
   ```
   git clone <repository-url>
   ```
   Or download and extract the ZIP file.

2. Navigate to the project directory:
   ```
   cd facebook-posts-app
   ```

3. Install dependencies:
   ```
   npm install
   ```

4. Create a `.env` file in the root directory with the following content:
   ```
   PORT=3000
   ```

5. Replace placeholder icons:
   - Replace `public/icon.ico` with your Windows icon file
   - Replace `public/icon.png` with your PNG icon (512x512 recommended)
   - Replace `public/favicon.ico` with your favicon for the web interface

## Running the Application

### Development Mode

To run the application in development mode:

```
npm start
```

This will launch the Electron application with the integrated Express server.

### Server Only Mode

If you just want to run the Express server without the Electron wrapper:

```
npm run server
```

Then open your browser and navigate to: `http://localhost:3000`

## Building a Standalone Application

To build a standalone executable for your platform:

### For Windows:
```
npm run build
```

### For macOS:
```
npm run build-mac
```

### For Linux:
```
npm run build-linux
```

The built application will be available in the `dist` folder.

## Using the Application

### Main Interface

The application has a clean, card-based interface with the following components:

1. **Header**:
   - Logo and title
   - Scraper control buttons (Start/Stop)
   - Theme toggle (Light/Dark mode)
   - Minimize to tray button

2. **Status Bar**:
   - Current scraper status (Online/Offline/Error)
   - Progress indicator during scraping
   - Next cycle time display

3. **Filter Bar**:
   - Search box for filtering posts by content
   - Category filter buttons (All, News, Sports, Entertainment)

4. **Posts Container**:
   - Grid layout of post cards
   - Each card shows:
     - Source name and avatar
     - Post content
     - Timestamp
     - Link to original Facebook post

### Features

- **Real-time Updates**: New posts appear automatically when scraped
- **Search & Filter**: Filter posts by text content or category
- **Light/Dark Mode**: Toggle between light and dark themes
- **Scraper Control**: Start and stop the scraping process
- **Progress Tracking**: See the progress of the current scraping cycle
- **Desktop Notifications**: Get notified when new posts are received

## System Tray Features

When minimized, the application continues to run in the system tray. Right-click the tray icon to access:

- **Show App**: Restore the application window
- **Start Scraper**: Start the scraping process
- **Stop Scraper**: Stop the scraping process
- **Quit**: Exit the application completely

Double-click the tray icon to show the application window.

## Customization

### Pages to Scrape

The application reads from `pages.json` to determine which Facebook pages to scrape. This file should contain an array of page URLs:

```json
[
  "https://www.facebook.com/page1",
  "https://www.facebook.com/page2",
  "https://www.facebook.com/page3"
]
```

### Automation Configuration

The `automation_config.json` file controls the timing of the scraping process:

```json
{
  "minLoopDelay": 60000,
  "maxLoopDelay": 600000,
  "minSearchDelay": 1000,
  "maxSearchDelay": 60000,
  "chunkSize": 10
}
```

- `minLoopDelay` and `maxLoopDelay`: Min and max time (in ms) between scraping cycles
- `minSearchDelay` and `maxSearchDelay`: Min and max time (in ms) between processing pages
- `chunkSize`: Number of pages processed in each batch

## Troubleshooting

### Application Won't Start

1. Make sure all dependencies are installed:
   ```
   npm install
   ```

2. Check the console for errors:
   ```
   npm start
   ```

3. Make sure port 3000 is not in use by another application.

### Scraper Not Working

1. Verify that `pages.json` exists and contains valid Facebook page URLs.

2. Check if your internet connection is working.

3. Facebook may be blocking the scraper. Try reducing the scraping frequency in `automation_config.json`.

### Other Issues

If you encounter other issues:

1. Check the application logs in the console.

2. Restart the application.

3. Clear the data files (`final_filtered_posts.json`) if they may be corrupted. 