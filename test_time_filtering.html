<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Time Filtering Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .filter-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        .filter-btn {
            padding: 10px 20px;
            border: 2px solid #1877f2;
            background: white;
            color: #1877f2;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        .filter-btn.active {
            background: #1877f2;
            color: white;
        }
        .post-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .post-time {
            color: #1877f2;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .post-content {
            color: #333;
            margin-bottom: 10px;
        }
        .post-meta {
            font-size: 12px;
            color: #666;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #1877f2;
            border-bottom: 2px solid #1877f2;
            padding-bottom: 5px;
        }
        .current-time {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .stat-item {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            flex: 1;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4caf50;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>📅 Enhanced Time Filtering Test</h1>
    
    <div class="test-container">
        <div class="current-time" id="current-time"></div>
        
        <h2>How It Works</h2>
        <p>The system now uses <strong>calculated Facebook post times</strong> instead of scraping timestamps for filtering:</p>
        <ul>
            <li><strong>Before:</strong> Filtered by when posts were scraped from Facebook</li>
            <li><strong>Now:</strong> Filters by when posts were actually published on Facebook</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>Sample Posts with Enhanced Time Filtering</h2>
        
        <div class="filter-buttons">
            <button class="filter-btn active" onclick="filterPosts('all')">All</button>
            <button class="filter-btn" onclick="filterPosts('today')">Today</button>
            <button class="filter-btn" onclick="filterPosts('week')">This Week</button>
            <button class="filter-btn" onclick="filterPosts('month')">This Month</button>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number" id="all-count">0</div>
                <div class="stat-label">All Posts</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="today-count">0</div>
                <div class="stat-label">Today</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="week-count">0</div>
                <div class="stat-label">This Week</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="month-count">0</div>
                <div class="stat-label">This Month</div>
            </div>
        </div>
        
        <div id="posts-container"></div>
    </div>

    <script>
        // Sample posts with relative times (like from your Facebook data)
        const samplePosts = [
            { relativeTime: '25m', content: 'واشنطن تفرض عقوبات على شبكة "مصارف الظل"', page: 'Al Taghier Channel' },
            { relativeTime: '1h', content: 'مراسل الحدث محمود شكر: الجيش اللبناني يكشف', page: 'Al Hadath' },
            { relativeTime: '2h', content: 'تنفيذا لأوامر ترمب.. شركة "ويستنغهاوس"', page: 'Al Hadath' },
            { relativeTime: '4d', content: 'جلال الصغير : أكو خراب وفساد وظلم', page: 'تشرين 24' },
            { relativeTime: '132h', content: 'بطل العاشر من رمضان الذي ضحى بحياته', page: 'Al Hadath' },
            { relativeTime: '6559m', content: 'الناشط الحقوقي فهمي الزبيري: مليشيا الحوثي', page: 'Al Hadath' },
            { relativeTime: '07m', content: 'نازحون فلسطينيون يحملون أوانيهم', page: 'Al Hadath' },
            { relativeTime: '02m', content: 'مباشر من قناة الحدث | تحرك عسكري إسرائيلي', page: 'Al Hadath' }
        ];

        let currentFilter = 'all';
        let processedPosts = [];

        // Calculate actual post dates from relative times
        function calculatePostDate(relativeTime) {
            const match = relativeTime.match(/^(\d+)([smhd])$/i);
            if (!match) return null;

            const value = parseInt(match[1]);
            const unit = match[2].toLowerCase();
            const now = new Date();
            const postDate = new Date(now);

            switch (unit) {
                case 's': postDate.setSeconds(now.getSeconds() - value); break;
                case 'm': postDate.setMinutes(now.getMinutes() - value); break;
                case 'h': postDate.setHours(now.getHours() - value); break;
                case 'd': postDate.setDate(now.getDate() - value); break;
            }

            return postDate;
        }

        // Process posts with calculated dates
        function processPosts() {
            processedPosts = samplePosts.map(post => {
                const calculatedDate = calculatePostDate(post.relativeTime);
                const isToday = calculatedDate && isDateToday(calculatedDate);
                const timeString = calculatedDate ? calculatedDate.toLocaleTimeString('en-US', { 
                    hour: 'numeric', minute: '2-digit', hour12: true 
                }) : 'Unknown';
                const dateString = calculatedDate ? calculatedDate.toLocaleDateString('en-US', {
                    year: 'numeric', month: 'short', day: 'numeric'
                }) : 'Unknown';

                return {
                    ...post,
                    calculatedDate,
                    displayTime: isToday ? 
                        `${post.relativeTime} (Today at ${timeString})` : 
                        `${post.relativeTime} (${dateString} at ${timeString})`
                };
            });
        }

        // Check if date is today
        function isDateToday(date) {
            const today = new Date();
            return date.getDate() === today.getDate() &&
                   date.getMonth() === today.getMonth() &&
                   date.getFullYear() === today.getFullYear();
        }

        // Filter posts based on time period
        function filterPosts(filter) {
            currentFilter = filter;
            
            // Update button states
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const weekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

            let filtered = processedPosts;

            if (filter !== 'all') {
                filtered = processedPosts.filter(post => {
                    if (!post.calculatedDate) return false;
                    
                    const postDateOnly = new Date(post.calculatedDate.getFullYear(), 
                                                 post.calculatedDate.getMonth(), 
                                                 post.calculatedDate.getDate());

                    switch (filter) {
                        case 'today': return postDateOnly.getTime() === today.getTime();
                        case 'week': return post.calculatedDate >= weekStart;
                        case 'month': return post.calculatedDate >= monthStart;
                        default: return true;
                    }
                });
            }

            renderPosts(filtered);
            updateStats();
        }

        // Render posts
        function renderPosts(posts) {
            const container = document.getElementById('posts-container');
            
            if (posts.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">No posts found for this time period.</p>';
                return;
            }

            container.innerHTML = posts.map(post => `
                <div class="post-item">
                    <div class="post-time">${post.displayTime}</div>
                    <div class="post-content">${post.content}</div>
                    <div class="post-meta">📺 ${post.page} | Calculated from: ${post.relativeTime}</div>
                </div>
            `).join('');
        }

        // Update statistics
        function updateStats() {
            const now = new Date();
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const weekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

            const todayCount = processedPosts.filter(post => {
                if (!post.calculatedDate) return false;
                const postDateOnly = new Date(post.calculatedDate.getFullYear(), 
                                             post.calculatedDate.getMonth(), 
                                             post.calculatedDate.getDate());
                return postDateOnly.getTime() === today.getTime();
            }).length;

            const weekCount = processedPosts.filter(post => 
                post.calculatedDate && post.calculatedDate >= weekStart
            ).length;

            const monthCount = processedPosts.filter(post => 
                post.calculatedDate && post.calculatedDate >= monthStart
            ).length;

            document.getElementById('all-count').textContent = processedPosts.length;
            document.getElementById('today-count').textContent = todayCount;
            document.getElementById('week-count').textContent = weekCount;
            document.getElementById('month-count').textContent = monthCount;
        }

        // Update current time display
        function updateCurrentTime() {
            document.getElementById('current-time').textContent = 
                `🕐 Current Time: ${new Date().toLocaleString()}`;
        }

        // Initialize
        function init() {
            updateCurrentTime();
            processPosts();
            filterPosts('all');
            
            // Update every minute
            setInterval(() => {
                updateCurrentTime();
                processPosts();
                filterPosts(currentFilter);
            }, 60000);
        }

        // Start when page loads
        window.onload = init;
    </script>
</body>
</html>
