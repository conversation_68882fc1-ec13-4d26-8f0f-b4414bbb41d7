<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'connection.php';

// Get the request method and endpoint
$method = $_SERVER['REQUEST_METHOD'];
$request = $_SERVER['REQUEST_URI'];
$path = parse_url($request, PHP_URL_PATH);
$path_parts = explode('/', trim($path, '/'));

// Remove 'database_api.php' from path if present
if (end($path_parts) === 'database_api.php') {
    array_pop($path_parts);
}

// Get the endpoint (last part of the path)
$endpoint = end($path_parts);
$action = isset($_GET['action']) ? $_GET['action'] : $endpoint;

try {
    switch ($method) {
        case 'GET':
            handleGet($conn, $action);
            break;
        case 'POST':
            handlePost($conn, $action);
            break;
        case 'PUT':
            handlePut($conn, $action);
            break;
        case 'DELETE':
            handleDelete($conn, $action);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleGet($conn, $action) {
    switch ($action) {
        case 'posts':
            getPosts($conn);
            break;
        case 'pages':
            getPages($conn);
            break;
        case 'members':
            getMembers($conn);
            break;
        case 'dashboard-stats':
            getDashboardStats($conn);
            break;
        case 'sync-status':
            getSyncStatus($conn);
            break;
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
            break;
    }
}

function handlePost($conn, $action) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'sync-from-json':
            syncFromJSON($conn);
            break;
        case 'sync-to-json':
            syncToJSON($conn);
            break;
        case 'posts':
            createPost($conn, $input);
            break;
        case 'pages':
            createPage($conn, $input);
            break;
        case 'members':
            createMember($conn, $input);
            break;
        case 'bulk-insert-posts':
            bulkInsertPosts($conn, $input);
            break;
        case 'bulk-insert-pages':
            bulkInsertPages($conn, $input);
            break;
        case 'bulk-insert-members':
            bulkInsertMembers($conn, $input);
            break;
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
            break;
    }
}

function handlePut($conn, $action) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'posts':
            updatePost($conn, $input);
            break;
        case 'pages':
            updatePage($conn, $input);
            break;
        case 'members':
            updateMember($conn, $input);
            break;
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
            break;
    }
}

function handleDelete($conn, $action) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($action) {
        case 'posts':
            deletePost($conn, $input);
            break;
        case 'pages':
            deletePage($conn, $input);
            break;
        case 'members':
            deleteMember($conn, $input);
            break;
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint not found']);
            break;
    }
}

// GET Functions
function getPosts($conn) {
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 1000;
    $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
    $search = isset($_GET['search']) ? $_GET['search'] : '';
    $page_id = isset($_GET['page_id']) ? $_GET['page_id'] : '';
    $member_id = isset($_GET['member_id']) ? $_GET['member_id'] : '';
    
    $sql = "SELECT * FROM posts_with_details WHERE 1=1";
    $params = [];
    $types = "";
    
    if (!empty($search)) {
        $sql .= " AND (caption LIKE ? OR page_name LIKE ? OR member_name LIKE ?)";
        $searchParam = "%$search%";
        $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
        $types .= "sss";
    }
    
    if (!empty($page_id)) {
        $sql .= " AND page_id = ?";
        $params[] = $page_id;
        $types .= "s";
    }
    
    if (!empty($member_id)) {
        $sql .= " AND member_id = ?";
        $params[] = $member_id;
        $types .= "s";
    }
    
    $sql .= " ORDER BY extraction_timestamp DESC LIMIT ? OFFSET ?";
    $params = array_merge($params, [$limit, $offset]);
    $types .= "ii";
    
    $stmt = $conn->prepare($sql);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    $posts = [];
    while ($row = $result->fetch_assoc()) {
        // Convert JSON fields back to objects
        if ($row['enhanced_post_time']) {
            $row['enhanced_post_time'] = json_decode($row['enhanced_post_time'], true);
        }
        if ($row['engagement']) {
            $row['engagement'] = json_decode($row['engagement'], true);
        }
        $posts[] = $row;
    }
    
    echo json_encode($posts);
}

function getPages($conn) {
    $sql = "SELECT * FROM pages_with_details ORDER BY page_name";
    $result = $conn->query($sql);
    
    $pages = [];
    while ($row = $result->fetch_assoc()) {
        $pages[] = $row;
    }
    
    echo json_encode($pages);
}

function getMembers($conn) {
    $sql = "SELECT * FROM members_with_stats ORDER BY member_name";
    $result = $conn->query($sql);
    
    $members = [];
    while ($row = $result->fetch_assoc()) {
        $members[] = $row;
    }
    
    echo json_encode($members);
}

function getDashboardStats($conn) {
    $sql = "CALL GetDashboardStats()";
    $result = $conn->query($sql);
    
    if ($row = $result->fetch_assoc()) {
        echo json_encode($row);
    } else {
        echo json_encode([
            'total_posts' => 0,
            'total_pages' => 0,
            'total_members' => 0,
            'total_likes' => 0,
            'total_comments' => 0,
            'total_views' => 0,
            'total_shares' => 0,
            'latest_update' => null
        ]);
    }
}

function getSyncStatus($conn) {
    // Check if database has data
    $posts_count = $conn->query("SELECT COUNT(*) as count FROM posts")->fetch_assoc()['count'];
    $pages_count = $conn->query("SELECT COUNT(*) as count FROM pages")->fetch_assoc()['count'];
    $members_count = $conn->query("SELECT COUNT(*) as count FROM members")->fetch_assoc()['count'];
    
    // Check JSON files
    $json_posts_count = 0;
    $json_pages_count = 0;
    $json_members_count = 0;
    
    if (file_exists('../final_filtered_posts.json')) {
        $json_posts = json_decode(file_get_contents('../final_filtered_posts.json'), true);
        $json_posts_count = is_array($json_posts) ? count($json_posts) : 0;
    }
    
    if (file_exists('../pages.json')) {
        $json_pages = json_decode(file_get_contents('../pages.json'), true);
        $json_pages_count = is_array($json_pages) ? count($json_pages) : 0;
    }
    
    if (file_exists('../team.json')) {
        $json_members = json_decode(file_get_contents('../team.json'), true);
        $json_members_count = is_array($json_members) ? count($json_members) : 0;
    }
    
    echo json_encode([
        'database' => [
            'posts' => $posts_count,
            'pages' => $pages_count,
            'members' => $members_count
        ],
        'json_files' => [
            'posts' => $json_posts_count,
            'pages' => $json_pages_count,
            'members' => $json_members_count
        ],
        'sync_needed' => [
            'posts' => $json_posts_count != $posts_count,
            'pages' => $json_pages_count != $pages_count,
            'members' => $json_members_count != $members_count
        ]
    ]);
}

// POST Functions
function syncFromJSON($conn) {
    $results = [
        'posts' => 0,
        'pages' => 0,
        'members' => 0,
        'errors' => []
    ];

    try {
        // Sync Members first
        if (file_exists('../team.json')) {
            $members = json_decode(file_get_contents('../team.json'), true);
            if (is_array($members)) {
                foreach ($members as $member) {
                    $member_id = $member['id'] ?? uniqid('member_');
                    $name = $member['name'] ?? 'Unknown';
                    $email = $member['email'] ?? '';
                    $role = $member['role'] ?? 'Member';

                    $sql = "INSERT INTO members (id, name, email, role) VALUES (?, ?, ?, ?)
                            ON DUPLICATE KEY UPDATE name=?, email=?, role=?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("sssssss", $member_id, $name, $email, $role, $name, $email, $role);

                    if ($stmt->execute()) {
                        $results['members']++;
                    }
                }
            }
        }

        // Sync Pages
        if (file_exists('../pages.json')) {
            $pages = json_decode(file_get_contents('../pages.json'), true);
            if (is_array($pages)) {
                foreach ($pages as $page) {
                    $page_id = generatePageId($page['link'] ?? '');
                    $name = $page['name'] ?? 'Unknown Page';
                    $url = $page['link'] ?? '';
                    $member_id = findMemberIdForPage($conn, $url);

                    $sql = "INSERT INTO pages (id, name, url, member_id) VALUES (?, ?, ?, ?)
                            ON DUPLICATE KEY UPDATE name=?, url=?, member_id=?";
                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("sssssss", $page_id, $name, $url, $member_id, $name, $url, $member_id);

                    if ($stmt->execute()) {
                        $results['pages']++;
                    }
                }
            }
        }

        // Sync Posts
        if (file_exists('../final_filtered_posts.json')) {
            $posts = json_decode(file_get_contents('../final_filtered_posts.json'), true);
            if (is_array($posts)) {
                foreach ($posts as $post) {
                    $post_id = $post['normalizedTextHash'] ?? uniqid('post_');
                    $page_id = generatePageId($post['pageUrl'] ?? '');
                    $member_id = findMemberIdForPage($conn, $post['pageUrl'] ?? '');
                    $caption = $post['finalFilteredText'] ?? '';
                    $likes = $post['engagement']['likes'] ?? 0;
                    $comments = $post['engagement']['comments'] ?? 0;
                    $views = $post['engagement']['views'] ?? 0;
                    $shares = $post['engagement']['shares'] ?? 0;
                    $post_url = $post['postUrl'] ?? '';
                    $page_url = $post['pageUrl'] ?? '';
                    $page_name = $post['pageName'] ?? '';
                    $post_date = $post['enhancedPostTime']['fullDate'] ?? $post['timestamp'] ?? null;
                    $last_update = $post['lastMetricsUpdate'] ?? $post['timestamp'] ?? null;
                    $extraction_timestamp = $post['timestamp'] ?? null;
                    $enhanced_post_time = json_encode($post['enhancedPostTime'] ?? null);
                    $engagement = json_encode($post['engagement'] ?? null);
                    $custom_url = $post['customUrl'] ?? null;

                    $sql = "INSERT INTO posts (id, page_id, member_id, caption, likes_count, comments_count,
                            views_count, shares_count, post_url, page_url, page_name, post_date,
                            last_metrics_update, extraction_timestamp, enhanced_post_time, engagement,
                            final_filtered_text, normalized_text_hash, custom_url)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            ON DUPLICATE KEY UPDATE
                            likes_count=?, comments_count=?, views_count=?, shares_count=?,
                            last_metrics_update=?, custom_url=?";

                    $stmt = $conn->prepare($sql);
                    $stmt->bind_param("ssssiiiisssssssssssiiiiss",
                        $post_id, $page_id, $member_id, $caption, $likes, $comments, $views, $shares,
                        $post_url, $page_url, $page_name, $post_date, $last_update, $extraction_timestamp,
                        $enhanced_post_time, $engagement, $caption, $post_id, $custom_url,
                        $likes, $comments, $views, $shares, $last_update, $custom_url
                    );

                    if ($stmt->execute()) {
                        $results['posts']++;
                    }
                }
            }
        }

        // Update counts
        $conn->query("CALL SyncPostsFromJSON()");

        echo json_encode(['success' => true, 'results' => $results]);

    } catch (Exception $e) {
        $results['errors'][] = $e->getMessage();
        echo json_encode(['success' => false, 'results' => $results]);
    }
}

function generatePageId($pageUrl) {
    if (empty($pageUrl)) return 'unknown_page';

    // Extract page name from URL
    if (preg_match('/facebook\.com\/([^\/\?]+)/', $pageUrl, $matches)) {
        return $matches[1];
    }

    // Fallback to hash of URL
    return 'page_' . substr(md5($pageUrl), 0, 8);
}

function findMemberIdForPage($conn, $pageUrl) {
    if (empty($pageUrl)) return null;

    // Load team data to check member assignments
    if (file_exists('../team_members.json')) {
        $team = json_decode(file_get_contents('../team_members.json'), true);
        if (is_array($team)) {
            foreach ($team as $member) {
                if (isset($member['assignedPages']) && is_array($member['assignedPages'])) {
                    foreach ($member['assignedPages'] as $assignedPage) {
                        $assignedUrl = '';
                        if (is_string($assignedPage)) {
                            $assignedUrl = $assignedPage;
                        } elseif (isset($assignedPage['link'])) {
                            $assignedUrl = $assignedPage['link'];
                        }

                        if (!empty($assignedUrl) && $pageUrl === $assignedUrl) {
                            return $member['id'];
                        }
                    }
                }
            }
        }
    }

    return null;
}

$conn->close();
?>
