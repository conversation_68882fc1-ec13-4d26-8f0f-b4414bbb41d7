<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Management - Facebook Scraper</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* Team-specific styles that work with the main theme */

        /* Left Sidebar Navigation */
        .left-sidebar {
          position: fixed;
          left: 0;
          top: 0;
          width: 250px;
          height: 100vh;
          background: var(--white);
          border-right: 1px solid rgba(0, 0, 0, 0.1);
          z-index: 1000;
          display: flex;
          flex-direction: column;
          box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .dark-mode .left-sidebar {
          background: var(--dark-card);
          border-right-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar-header {
          padding: 20px;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);
          display: flex;
          align-items: center;
          gap: 12px;
          font-weight: 600;
          font-size: 18px;
          color: var(--primary-color);
        }

        .dark-mode .sidebar-header {
          border-bottom-color: rgba(255, 255, 255, 0.1);
          color: var(--dark-text);
        }

        .sidebar-header i {
          font-size: 24px;
        }

        .sidebar-menu {
          list-style: none;
          padding: 20px 0;
          margin: 0;
          flex: 1;
        }

        .sidebar-menu li {
          margin: 0;
        }

        .sidebar-link {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 15px 20px;
          color: var(--text-color);
          text-decoration: none;
          transition: all 0.3s ease;
          border-left: 3px solid transparent;
        }

        .dark-mode .sidebar-link {
          color: var(--dark-text);
        }

        .sidebar-link:hover {
          background: rgba(59, 130, 246, 0.1);
          color: var(--primary-color);
          border-left-color: var(--primary-color);
        }

        .sidebar-link.active {
          background: rgba(59, 130, 246, 0.15);
          color: var(--primary-color);
          border-left-color: var(--primary-color);
          font-weight: 600;
        }

        .sidebar-link i {
          font-size: 18px;
          width: 20px;
          text-align: center;
        }
        .team-container {
            max-width: none;
            width: calc(100vw - 270px);
            margin: 0;
            padding: 1.5rem 1.5rem 0 1.5rem;
            box-sizing: border-box;
            min-height: 100vh;
            background: var(--secondary-color);
            color: var(--text-color);
        }

        .dark-mode .team-container {
            background: var(--dark-bg);
            color: var(--dark-text);
        }

        .team-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: var(--white);
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            width: 100%;
            box-sizing: border-box;
        }

        .dark-mode .team-header {
            background: var(--dark-card);
            border-color: rgba(255, 255, 255, 0.05);
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .team-header h1 {
            color: var(--text-color);
            margin: 0;
        }

        .dark-mode .team-header h1 {
            color: var(--dark-text);
        }

        .team-header p {
            color: var(--light-text);
            margin: 5px 0 0 0;
        }

        .dark-mode .team-header p {
            color: var(--dark-light-text);
        }

        .team-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1.5rem;
            margin-bottom: 30px;
            width: 100%;
            box-sizing: border-box;
        }

        .stat-card {
            background: var(--white);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: transform 0.2s ease;
        }

        .dark-mode .stat-card {
            background: var(--dark-card);
            border-color: rgba(255, 255, 255, 0.05);
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-card div:last-child {
            color: var(--light-text);
            font-size: 0.9rem;
        }

        .dark-mode .stat-card div:last-child {
            color: var(--dark-light-text);
        }

        /* Best Performer Section */
        .best-performer-section {
            margin-bottom: 30px;
        }

        .best-performer-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 25px;
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .best-performer-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            pointer-events: none;
        }

        .best-performer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .best-performer-header h3 {
            margin: 0;
            font-size: 1.5rem;
            color: white;
        }

        .performance-period select {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .performance-period select option {
            background: var(--dark-card);
            color: var(--dark-text);
        }

        .best-performer-content {
            position: relative;
            z-index: 1;
        }

        .performer-info {
            display: grid;
            grid-template-columns: auto 1fr auto;
            gap: 20px;
            align-items: center;
        }

        .performer-avatar {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .performer-details h4 {
            margin: 0 0 5px 0;
            font-size: 1.3rem;
            color: white;
        }

        .performer-details p {
            margin: 0;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .performer-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            text-align: center;
        }

        .performer-stat {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .performer-stat-number {
            font-size: 1.2rem;
            font-weight: bold;
            color: white;
            margin-bottom: 2px;
        }

        .performer-stat-label {
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 0.8);
        }

        .team-grid {
            display: grid;
            grid-template-columns: 1fr 2.5fr;
            gap: 1.5rem;
            width: 100%;
            box-sizing: border-box;
        }

        /* Responsive breakpoints for team container */
        @media (max-width: 1200px) {
            .team-container {
                width: calc(100vw - 260px);
                padding: 1rem 1rem 0 1rem;
            }
        }

        @media (max-width: 900px) {
            .team-container {
                width: calc(100vw - 250px);
                padding: 0.75rem 0.75rem 0 0.75rem;
            }
        }

        @media (max-width: 768px) {
            .team-container {
                width: calc(100vw - 220px);
                padding: 0.5rem 0.5rem 0 0.5rem;
            }

            .team-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 640px) {
            .team-container {
                width: 100vw;
                margin-left: 0;
                padding: 0.5rem 0.5rem 0 0.5rem;
            }
        }

        .team-members-panel,
        .member-posts-panel {
            background: var(--white);
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            padding: 20px;
        }

        .dark-mode .team-members-panel,
        .dark-mode .member-posts-panel {
            background: var(--dark-card);
            border-color: rgba(255, 255, 255, 0.05);
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }

        .team-members-panel h3,
        .member-posts-panel h3 {
            color: var(--text-color);
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .dark-mode .team-members-panel h3,
        .dark-mode .member-posts-panel h3 {
            color: var(--dark-text);
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        .member-card {
            background: var(--secondary-color);
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .dark-mode .member-card {
            background: var(--dark-secondary);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .member-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .dark-mode .member-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
        }

        .member-card.selected {
            border-color: var(--primary-color);
            background: rgba(66, 103, 178, 0.1);
        }

        .dark-mode .member-card.selected {
            background: rgba(66, 103, 178, 0.2);
        }

        .member-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .member-details h4 {
            margin: 0 0 5px 0;
            color: var(--text-color);
        }

        .dark-mode .member-details h4 {
            color: var(--dark-text);
        }

        .member-details p {
            margin: 0;
            color: var(--light-text);
            font-size: 0.9rem;
        }

        .dark-mode .member-details p {
            color: var(--dark-light-text);
        }

        .member-actions {
            display: flex;
            gap: 10px;
        }

        .btn-icon {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--light-text);
        }

        .dark-mode .btn-icon {
            color: var(--dark-light-text);
        }

        .btn-icon:hover {
            background: rgba(0, 0, 0, 0.05);
            transform: scale(1.1);
        }

        .dark-mode .btn-icon:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .btn-icon.edit:hover {
            color: #ffc107;
            background: rgba(255, 193, 7, 0.1);
        }

        .btn-icon.delete:hover {
            color: #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }

        .btn-icon.delete-posts:hover {
            color: #fd7e14;
            background: rgba(253, 126, 20, 0.1);
        }

        .btn-icon.export:hover {
            color: #28a745;
            background: rgba(40, 167, 69, 0.1);
        }

        .btn-icon.scrape:hover {
            color: #007bff;
            background: rgba(0, 123, 255, 0.1);
        }

        .btn-icon.scrape-multiple:hover {
            color: #6f42c1;
            background: rgba(111, 66, 193, 0.1);
        }

        .btn-icon.debug-urls:hover {
            color: #fd7e14;
            background: rgba(253, 126, 20, 0.1);
        }

        .btn-icon.debug-page:hover {
            color: #e83e8c;
            background: rgba(232, 62, 140, 0.1);
        }

        .btn-icon:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .export-dropdown-team {
            position: relative;
            display: inline-block;
        }

        .export-options-team {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--white);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 150px;
            margin-top: 2px;
            opacity: 0;
            transform: translateY(-10px);
            transition: opacity 0.2s ease, transform 0.2s ease;
            pointer-events: none;
        }

        .dark-mode .export-options-team {
            background: var(--dark-card);
            border-color: rgba(255, 255, 255, 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .export-dropdown-team.active .export-options-team {
            display: block;
            opacity: 1;
            transform: translateY(0);
            pointer-events: auto;
        }

        .export-option-team {
            display: block;
            width: 100%;
            padding: 8px 12px;
            border: none;
            background: transparent;
            color: var(--text-color);
            text-align: left;
            cursor: pointer;
            font-size: 0.8rem;
            transition: background-color 0.2s;
            border-radius: 0;
        }

        .dark-mode .export-option-team {
            color: var(--dark-text);
        }

        .export-option-team:hover {
            background: rgba(0, 0, 0, 0.05);
        }

        .dark-mode .export-option-team:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .export-option-team:first-child {
            border-radius: 8px 8px 0 0;
        }

        .export-option-team:last-child {
            border-radius: 0 0 8px 8px;
        }

        .export-option-team i {
            margin-right: 8px;
            width: 12px;
        }

        /* Time-based stats styling */
        .time-stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            background: var(--secondary-color);
            border-radius: 6px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            min-width: 60px;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
        }

        .time-stat:hover {
            background: rgba(66, 103, 178, 0.1);
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .dark-mode .time-stat {
            background: var(--dark-secondary);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .time-label {
            color: var(--light-text);
            font-size: 0.7rem;
            font-weight: 500;
            margin-bottom: 2px;
        }

        .dark-mode .time-label {
            color: var(--dark-light-text);
        }

        .time-count {
            color: var(--primary-color);
            font-weight: bold;
            font-size: 0.9rem;
        }

        .time-stat.highlight {
            background: rgba(66, 103, 178, 0.1);
            border-color: var(--primary-color);
        }

        /* Enhanced Pagination styles */
        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding: 1.5rem 0;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
            background: rgba(0, 0, 0, 0.02);
            border-radius: 12px;
            margin-left: -1.25rem;
            margin-right: -1.25rem;
            padding-left: 1.25rem;
            padding-right: 1.25rem;
        }

        .dark-mode .pagination-container {
            border-top-color: rgba(255, 255, 255, 0.08);
            background: rgba(255, 255, 255, 0.02);
        }

        .pagination-info {
            color: var(--light-text);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .dark-mode .pagination-info {
            color: var(--dark-light-text);
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .pagination-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem 1.25rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: 0 2px 8px rgba(66, 103, 178, 0.2);
        }

        .pagination-btn:hover:not(:disabled) {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(66, 103, 178, 0.3);
        }

        .pagination-btn:disabled {
            background: var(--light-text);
            cursor: not-allowed;
            opacity: 0.5;
            transform: none;
            box-shadow: none;
        }

        .dark-mode .pagination-btn:disabled {
            background: var(--dark-light-text);
        }

        .pagination-current {
            color: var(--text-color);
            font-weight: 600;
            font-size: 0.95rem;
            padding: 0.75rem 1rem;
            background: rgba(66, 103, 178, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(66, 103, 178, 0.2);
        }

        .dark-mode .pagination-current {
            color: var(--dark-text);
            background: rgba(66, 103, 178, 0.15);
            border-color: rgba(66, 103, 178, 0.3);
        }

        /* Responsive design for post cards */
        @media (max-width: 1400px) {
            .posts-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 1.25rem;
            }
        }

        @media (max-width: 1024px) {
            .posts-grid {
                grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
                gap: 1rem;
            }
        }

        @media (max-width: 768px) {
            .posts-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .post-card-mini {
                margin: 0 -0.5rem;
            }

            .pagination-container {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .pagination-controls {
                justify-content: center;
            }
        }

        .time-stat.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .time-stat.active .time-label,
        .time-stat.active .time-count {
            color: white;
        }

        .dark-mode .time-stat.highlight {
            background: rgba(66, 103, 178, 0.2);
        }

        .dark-mode .time-stat:hover {
            background: rgba(66, 103, 178, 0.2);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            #memberTimeStats {
                flex-direction: column;
                gap: 5px;
            }

            .time-stat {
                flex-direction: row;
                gap: 5px;
                min-width: auto;
                padding: 4px 8px;
            }
        }

        .assigned-pages {
            margin-top: 10px;
            font-size: 0.8rem;
            color: var(--light-text);
        }

        .dark-mode .assigned-pages {
            color: var(--dark-light-text);
        }

        /* Member Statistics */
        .member-stats {
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            font-size: 0.75rem;
        }

        .dark-mode .member-stats {
            border-top-color: rgba(255, 255, 255, 0.1);
        }

        .member-stat {
            text-align: center;
            padding: 4px;
            background: rgba(66, 103, 178, 0.05);
            border-radius: 4px;
            border: 1px solid rgba(66, 103, 178, 0.1);
        }

        .dark-mode .member-stat {
            background: rgba(66, 103, 178, 0.1);
            border-color: rgba(66, 103, 178, 0.2);
        }

        .member-stat-number {
            font-weight: bold;
            color: var(--primary-color);
            font-size: 0.9rem;
            margin-bottom: 1px;
        }

        .member-stat-label {
            color: var(--light-text);
            font-size: 0.7rem;
        }

        .dark-mode .member-stat-label {
            color: var(--dark-light-text);
        }

        .member-performance-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 500;
            margin-left: 8px;
        }

        .performance-excellent {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.2);
        }

        .performance-good {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.2);
        }

        .performance-average {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
            border: 1px solid rgba(108, 117, 125, 0.2);
        }

        .performance-low {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.2);
        }

        .page-tag {
            display: inline-block;
            background: rgba(66, 103, 178, 0.1);
            color: var(--primary-color);
            padding: 2px 8px;
            border-radius: 12px;
            margin: 2px;
            font-size: 0.7rem;
            border: 1px solid rgba(66, 103, 178, 0.3);
        }

        .dark-mode .page-tag {
            background: rgba(66, 103, 178, 0.2);
            border-color: rgba(66, 103, 178, 0.4);
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: var(--white);
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .dark-mode .modal-content {
            background-color: var(--dark-card);
            border-color: rgba(255, 255, 255, 0.05);
        }

        .modal-content h3 {
            color: var(--text-color);
            margin-top: 0;
        }

        .dark-mode .modal-content h3 {
            color: var(--dark-text);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: var(--text-color);
        }

        .dark-mode .form-group label {
            color: var(--dark-text);
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            background: var(--secondary-color);
            color: var(--text-color);
            font-family: inherit;
        }

        .dark-mode .form-group input,
        .dark-mode .form-group select {
            border-color: rgba(255, 255, 255, 0.1);
            background: var(--dark-secondary);
            color: var(--dark-text);
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(66, 103, 178, 0.2);
        }

        .pages-assignment {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 5px;
            padding: 10px;
            background: var(--secondary-color);
        }

        .dark-mode .pages-assignment {
            border-color: rgba(255, 255, 255, 0.1);
            background: var(--dark-secondary);
        }

        .page-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 5px;
            border-radius: 3px;
            transition: background 0.2s ease;
        }

        .page-checkbox:hover {
            background: rgba(0, 0, 0, 0.05);
        }

        .dark-mode .page-checkbox:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .page-checkbox input {
            width: auto !important;
            margin-right: 10px;
        }

        .page-checkbox label {
            color: var(--text-color);
            cursor: pointer;
            margin: 0;
            font-weight: normal;
        }

        .dark-mode .page-checkbox label {
            color: var(--dark-text);
        }

        .no-selection {
            text-align: center;
            color: var(--light-text);
            padding: 40px;
        }

        .dark-mode .no-selection {
            color: var(--dark-light-text);
        }

        .posts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .post-card-mini {
            background: var(--white);
            border-radius: 16px;
            padding: 0;
            border: 1px solid rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .dark-mode .post-card-mini {
            background: var(--dark-card);
            border-color: rgba(255, 255, 255, 0.08);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .post-card-mini:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
            border-color: var(--primary-color);
        }

        .dark-mode .post-card-mini:hover {
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
        }

        .post-card-mini::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), #667eea);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .post-card-mini:hover::before {
            opacity: 1;
        }

        .post-card-header {
            padding: 1rem 1.25rem 0.75rem 1.25rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.06);
        }

        .dark-mode .post-card-header {
            border-bottom-color: rgba(255, 255, 255, 0.06);
        }

        .post-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .post-source {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .post-source i {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .post-time {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            color: var(--light-text);
            background: rgba(0, 0, 0, 0.04);
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
        }

        .dark-mode .post-time {
            color: var(--dark-light-text);
            background: rgba(255, 255, 255, 0.06);
        }

        .post-content {
            padding: 0 1.25rem 1rem 1.25rem;
            color: var(--text-color);
            line-height: 1.5;
            font-size: 0.9rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .dark-mode .post-content {
            color: var(--dark-text);
        }

        .post-footer {
            padding: 0.75rem 1.25rem 1rem 1.25rem;
            background: rgba(0, 0, 0, 0.02);
            border-top: 1px solid rgba(0, 0, 0, 0.06);
        }

        .dark-mode .post-footer {
            background: rgba(255, 255, 255, 0.02);
            border-top-color: rgba(255, 255, 255, 0.06);
        }

        .post-engagement {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .engagement-stats {
            display: flex;
            gap: 1rem;
        }

        .engagement-item {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 0.8rem;
            color: var(--light-text);
            transition: color 0.2s ease;
        }

        .dark-mode .engagement-item {
            color: var(--dark-light-text);
        }

        .engagement-item:hover {
            color: var(--primary-color);
        }

        .engagement-item i {
            font-size: 0.9rem;
        }

        .engagement-number {
            font-weight: 600;
        }

        .post-actions {
            display: flex;
            gap: 0.5rem;
        }

        .post-action-btn {
            background: none;
            border: none;
            padding: 0.375rem;
            border-radius: 8px;
            cursor: pointer;
            color: var(--light-text);
            transition: all 0.2s ease;
            font-size: 0.85rem;
        }

        .dark-mode .post-action-btn {
            color: var(--dark-light-text);
        }

        .post-action-btn:hover {
            background: rgba(66, 103, 178, 0.1);
            color: var(--primary-color);
            transform: scale(1.1);
        }

        /* Theme toggle button styling */
        .theme-toggle {
            background: var(--secondary-color);
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: var(--text-color);
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 10px;
        }

        .dark-mode .theme-toggle {
            background: var(--dark-secondary);
            border-color: rgba(255, 255, 255, 0.1);
            color: var(--dark-text);
        }

        .theme-toggle:hover {
            background: rgba(0, 0, 0, 0.05);
            transform: scale(1.05);
        }

        .dark-mode .theme-toggle:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* Fix header buttons alignment - target only team header */
        .team-header > div:last-child {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* Override theme toggle styling to match other buttons */
        .team-header .theme-toggle {
            margin: 0;
            padding: 10px 16px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            font-size: 0.9rem;
        }

        .team-header .theme-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .dark-mode .team-header .theme-toggle:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        /* Ensure all header buttons have consistent height */
        .team-header .btn {
            height: 40px;
            font-size: 0.9rem;
        }

        /* Update Metrics Button Animation */
        #updateMemberMetricsBtn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        #updateMemberMetricsBtn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 103, 178, 0.3);
        }

        #updateMemberMetricsBtn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        #updateMemberMetricsBtn.updating {
            background: linear-gradient(45deg, #4267b2, #365899, #4267b2, #365899);
            background-size: 400% 400%;
            animation: updateGradient 2s ease-in-out infinite;
        }

        #updateMemberMetricsBtn.stopping {
            background: linear-gradient(45deg, #dc3545, #c82333, #dc3545, #c82333);
            background-size: 400% 400%;
            animation: stopGradient 1.5s ease-in-out infinite;
        }

        @keyframes updateGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes stopGradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Pulse animation for the icon */
        #updateMemberMetricsBtn.updating .fa-sync-alt {
            animation: pulse 1.5s ease-in-out infinite;
        }

        #updateMemberMetricsBtn.stopping .fa-stop {
            animation: pulse 1s ease-in-out infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Metrics Updated Indicator Styling */
        .metrics-updated-indicator {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 0.75em;
            color: #17a2b8;
            background: rgba(23, 162, 184, 0.1);
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: 8px;
            border: 1px solid rgba(23, 162, 184, 0.2);
        }

        .metrics-updated-indicator .time-ago {
            font-weight: 500;
            white-space: nowrap;
        }

        .metrics-updated-indicator i {
            animation: rotate 2s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Dark mode styling for metrics indicator */
        .dark-mode .metrics-updated-indicator {
            background: rgba(23, 162, 184, 0.15);
            border-color: rgba(23, 162, 184, 0.3);
        }

        /* Post time row styling */
        .post-time-row {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .time-ago-badge {
            font-size: 0.75em;
            color: #6c757d;
            background: rgba(108, 117, 125, 0.1);
            padding: 2px 6px;
            border-radius: 10px;
            white-space: nowrap;
        }

        .dark-mode .time-ago-badge {
            color: #adb5bd;
            background: rgba(173, 181, 189, 0.1);
        }

        /* Disabled button styling */
        .btn.disabled {
            opacity: 0.6;
            cursor: not-allowed;
            pointer-events: none;
        }

        /* Global stop button styling */
        #globalStopMetricsBtn {
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* Mobile responsive for header buttons only */
        @media (max-width: 768px) {
            .team-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
                margin: 0 -0.5rem 30px -0.5rem;
                padding: 15px;
            }

            .team-header > div:last-child {
                width: 100%;
                justify-content: flex-end;
                flex-wrap: wrap;
                gap: 8px;
            }

            .team-header .btn,
            .team-header .theme-toggle {
                font-size: 0.85rem;
                height: 36px;
            }

            .team-stats {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 1rem;
                margin: 0 -0.5rem 30px -0.5rem;
                padding: 0 0.5rem;
            }

            .team-grid {
                gap: 1rem;
                margin: 0 -0.5rem;
                padding: 0 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Left Sidebar Navigation -->
    <nav class="left-sidebar">
      <div class="sidebar-header">
        <i class="fab fa-facebook"></i>
        <span>Facebook App</span>
      </div>
      <ul class="sidebar-menu">
        <li>
          <a href="/index.html" class="sidebar-link">
            <i class="fas fa-home"></i>
            <span>Posts</span>
          </a>
        </li>
        <li>
          <a href="/team.html" class="sidebar-link active">
            <i class="fas fa-users"></i>
            <span>Team</span>
          </a>
        </li>
        <li>
          <a href="/updates.html" class="sidebar-link">
            <i class="fas fa-database"></i>
            <span>Database</span>
          </a>
        </li>
        <li>
          <a href="/proxy-manager.html" class="sidebar-link">
            <i class="fas fa-network-wired"></i>
            <span>Proxy Manager</span>
          </a>
        </li>
      </ul>
    </nav>

    <div class="team-container" style="margin-left: 250px;">
        <!-- Header -->
        <div class="team-header">
            <div>
                <h1><i class="fas fa-users"></i> Team Management</h1>
                <p>Manage team members and assign pages</p>
            </div>
            <div>
                <button class="btn primary" onclick="openAddMemberModal()">
                    <i class="fas fa-plus"></i> Add Member
                </button>
                <button id="refreshStatsBtn" class="btn secondary" onclick="manualRefreshStats()" title="Refresh Team Statistics">
                    <i class="fas fa-sync-alt"></i> Refresh Stats
                </button>


                <button id="globalStopMetricsBtn" class="btn danger" style="display: none;" onclick="stopGlobalMetricsUpdate()">
                    <i class="fas fa-stop"></i> Stop Metrics Update
                </button>
                <button class="theme-toggle" onclick="toggleTheme()">
                    <i class="fas fa-moon"></i>
                </button>

            </div>
        </div>

        <!-- Stats -->
        <div class="team-stats">
            <div class="stat-card">
                <div class="stat-number" id="totalMembers">0</div>
                <div>Total Members</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPages">0</div>
                <div>Total Pages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="assignedPages">0</div>
                <div>Assigned Pages</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPosts">0</div>
                <div>Total Posts</div>
            </div>
        </div>

        <!-- Best Performer Section -->
        <div id="bestPerformerSection" class="best-performer-section" style="display: none;">
            <div class="best-performer-card">
                <div class="best-performer-header">
                    <h3><i class="fas fa-trophy"></i> Top Performer</h3>
                    <div class="performance-period">
                        <select id="performancePeriod" onchange="updateBestPerformer()">
                            <option value="all">All Time</option>
                            <option value="month">This Month</option>
                            <option value="week">This Week</option>
                            <option value="today">Today</option>
                        </select>
                    </div>
                </div>
                <div id="bestPerformerContent">
                    <!-- Best performer content will be loaded here -->
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="team-grid">
            <!-- Team Members Panel -->
            <div class="team-members-panel">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="margin: 0;"><i class="fas fa-users"></i> Team Members</h3>
                    <button id="stopMemberScrapingBtn" onclick="stopMemberScraping()"
                            style="display: none; background: #dc3545; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer; font-size: 0.8rem;">
                        <i class="fas fa-stop"></i> Stop Scraping
                    </button>
                </div>
                <div id="teamMembersList">
                    <!-- Team members will be loaded here -->
                </div>
            </div>

            <!-- Member Posts Panel -->
            <div class="member-posts-panel">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 id="memberPostsTitle" style="margin: 0;"><i class="fas fa-file-alt"></i> Select a Member</h3>
                    <div id="memberExportControls" style="display: none;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <!-- Time-based counters -->
                            <div id="memberTimeStats" style="display: flex; gap: 10px; font-size: 0.8rem;">
                                <div class="time-stat active" data-filter="all" onclick="filterPostsByTime('all')" title="Show all posts">
                                    <span class="time-label">All:</span>
                                    <span id="allCount" class="time-count">0</span>
                                </div>
                                <div class="time-stat" data-filter="today" onclick="filterPostsByTime('today')" title="Show today's posts">
                                    <span class="time-label">Today:</span>
                                    <span id="todayCount" class="time-count">0</span>
                                </div>
                                <div class="time-stat" data-filter="week" onclick="filterPostsByTime('week')" title="Show this week's posts">
                                    <span class="time-label">This Week:</span>
                                    <span id="weekCount" class="time-count">0</span>
                                </div>
                                <div class="time-stat" data-filter="month" onclick="filterPostsByTime('month')" title="Show this month's posts">
                                    <span class="time-label">This Month:</span>
                                    <span id="monthCount" class="time-count">0</span>
                                </div>
                            </div>
                            <!-- Update Metrics Button -->
                            <button id="updateMemberMetricsBtn" class="btn primary" onclick="updateMemberMetrics()" title="Update metrics for this member's posts">
                                <i class="fas fa-sync-alt"></i> Update Metrics
                            </button>
                            <!-- Export dropdown -->
                            <div class="export-dropdown-team">
                                <button class="btn-icon export" title="Export Member Data">
                                    <i class="fas fa-download"></i>
                                </button>
                                <div class="export-options-team">
                                    <button class="export-option-team" onclick="exportMemberData('json')">
                                        <i class="fas fa-file-code"></i> Export as JSON
                                    </button>
                                    <button class="export-option-team" onclick="exportMemberData('csv')">
                                        <i class="fas fa-file-csv"></i> Export as CSV
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="memberPostsContent">
                    <div class="no-selection">
                        <i class="fas fa-user-circle" style="font-size: 3rem; color: var(--text-muted);"></i>
                        <p>Select a team member to view their assigned pages and posts</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Member Modal -->
    <div id="memberModal" class="modal">
        <div class="modal-content">
            <h3 id="modalTitle">Add Team Member</h3>
            <form id="memberForm">
                <div class="form-group">
                    <label for="memberName">Name *</label>
                    <input type="text" id="memberName" required>
                </div>
                <div class="form-group">
                    <label for="memberEmail">Email</label>
                    <input type="email" id="memberEmail">
                </div>
                <div class="form-group">
                    <label for="memberRole">Role</label>
                    <select id="memberRole">
                        <option value="Member">Member</option>
                        <option value="Manager">Manager</option>
                        <option value="Admin">Admin</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Assigned Pages</label>
                    <div id="pagesAssignment" class="pages-assignment">
                        <!-- Pages will be loaded here -->
                    </div>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn secondary" onclick="closeMemberModal()">Cancel</button>
                    <button type="submit" class="btn primary">Save</button>
                </div>
            </form>
        </div>
    </div>

    <script src="team.js"></script>
</body>
</html>
