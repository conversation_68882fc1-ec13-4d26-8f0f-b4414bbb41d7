<?php
// Database API - Returns exact database structure
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

// Database connection
$conn = new mysqli("localhost", "root", "", "facebook_db");

$response = ['success' => false, 'data' => [], 'debug' => []];

if ($conn->connect_error) {
    $response['message'] = "Connection failed: " . $conn->connect_error;
    echo json_encode($response);
    exit;
}

// Get the endpoint from URL query parameter or path
$endpoint = $_GET['endpoint'] ?? '';
if (empty($endpoint)) {
    $path = $_SERVER['REQUEST_URI'];
    $endpoint = basename(parse_url($path, PHP_URL_PATH));
}

$response['debug'][] = "Endpoint requested: " . $endpoint;

try {
    switch ($endpoint) {
        case 'posts':
            // Return exact posts table structure
            $query = "SELECT `id`, `page_id`, `member_id`, `caption`, `likes_count`, `comments_count`, `views_count`, `shares_count`, `post_url`, `post_date`, `last_metrics_update`, `extraction_timestamp`, `created_at` FROM `posts` ORDER BY `extraction_timestamp` DESC";
            $result = $conn->query($query);
            
            $posts = [];
            while ($row = $result->fetch_assoc()) {
                $posts[] = $row;
            }
            
            $response['success'] = true;
            $response['data'] = $posts;
            break;

        case 'pages':
            // Return exact pages table structure
            $query = "SELECT `id`, `name`, `url`, `member_id`, `created_at` FROM `pages` ORDER BY `name`";
            $result = $conn->query($query);
            
            $pages = [];
            while ($row = $result->fetch_assoc()) {
                $pages[] = $row;
            }
            
            $response['success'] = true;
            $response['data'] = $pages;
            break;

        case 'members':
            // Return exact members table structure
            $query = "SELECT `id`, `name`, `email`, `role`, `created_at` FROM `members` ORDER BY `name`";
            $result = $conn->query($query);
            
            $members = [];
            while ($row = $result->fetch_assoc()) {
                $members[] = $row;
            }
            
            $response['success'] = true;
            $response['data'] = $members;
            break;

        case 'sync-status':
            // Check database status
            $postsCount = $conn->query("SELECT COUNT(*) as count FROM posts")->fetch_assoc()['count'];
            $pagesCount = $conn->query("SELECT COUNT(*) as count FROM pages")->fetch_assoc()['count'];
            $membersCount = $conn->query("SELECT COUNT(*) as count FROM members")->fetch_assoc()['count'];
            
            $response['success'] = true;
            $response['data'] = [
                'database_connected' => true,
                'posts_count' => $postsCount,
                'pages_count' => $pagesCount,
                'members_count' => $membersCount,
                'last_sync' => date('Y-m-d H:i:s')
            ];
            break;

        default:
            $response['message'] = 'Unknown endpoint: ' . $endpoint;
            break;
    }

} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Database error: ' . $e->getMessage();
}

echo json_encode($response);
$conn->close();
?>
