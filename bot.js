// Import required modules
const puppeteer = require("puppeteer-extra");
// Use the stealth plugin to help avoid detection
const StealthPlugin = require("puppeteer-extra-plugin-stealth");
puppeteer.use(StealthPlugin());

const axios = require("axios");
const fs = require("fs");
require("dotenv").config();
const TelegramBot = require("node-telegram-bot-api");

// Import proxy management system for scraping only
const {
  getPuppeteerProxyOptions,
  setPuppeteerProxyAuth,
  proxyManager
} = require('./proxy-helpers');

// Initialize the Telegram bot with polling enabled
const bot = new TelegramBot(process.env.TOKEN, { polling: true });

// Define file paths for storing data
const POSTS_FILE = "posts.json";
const PAGES_FILE = "pages.json";
const FILTERED_FILE = "filtered_posts.json";
const CONFIG_FILE = "automation_config.json"; // Config file for automation timings
const FINAL_FILTERED_FILE = "final_filtered_posts.json"; // Final filtered posts file
const AUTHORIZED_USERS_FILE = "authorized_users.json"; // File for storing authorized users

// Load authorized users from file
let authorizedUsers = [];
function loadAuthorizedUsers() {
  console.log("Loading authorized users...");
  authorizedUsers = ["randomiz643"]; // Start with a default
  
  try {
    if (fs.existsSync(AUTHORIZED_USERS_FILE)) {
      const fileContent = fs.readFileSync(AUTHORIZED_USERS_FILE, "utf8");
      console.log(`Read authorized_users.json content: "${fileContent}"`);
      
      try {
        const parsed = JSON.parse(fileContent);
        
        // Ensure it's an array
        if (Array.isArray(parsed) && parsed.length > 0) {
          // Use the parsed array
          authorizedUsers = parsed.map(user => 
            typeof user === 'string' ? user.replace(/^@/, '') : String(user)
          );
          console.log("Successfully loaded authorized users:", authorizedUsers);
        } else {
          console.error("Authorized users file does not contain a valid array. Regenerating.");
          regenerateAuthorizedUsersFile();
        }
      } catch (parseErr) {
        console.error("Error parsing authorized users JSON:", parseErr);
        regenerateAuthorizedUsersFile();
      }
    } else {
      console.log("Authorized users file doesn't exist, creating default file");
      regenerateAuthorizedUsersFile();
    }
  } catch (err) {
    console.error("Unexpected error loading authorized users:", err);
    regenerateAuthorizedUsersFile();
  }
  
  // Make a manual check that randomiz643 is in the list to be sure
  if (!authorizedUsers.some(user => user.toLowerCase() === "randomiz643")) {
    console.log("Adding randomiz643 to authorized users list as a fallback");
    authorizedUsers.push("randomiz643");
    regenerateAuthorizedUsersFile();
  }
  
  console.log(`Final authorized users list: ${JSON.stringify(authorizedUsers)}`);
}

function saveAuthorizedUsers() {
  try {
    // Write with proper JSON formatting and indentation
    fs.writeFileSync(AUTHORIZED_USERS_FILE, JSON.stringify(authorizedUsers, null, 2));
    console.log("Saved authorized users:", authorizedUsers);
  } catch (err) {
    console.error("Error saving authorized users:", err);
  }
}

// Add a new authorized user
function addAuthorizedUser(username) {
  // Remove @ if present
  username = username.replace(/^@/, '');
  if (!authorizedUsers.includes(username)) {
    authorizedUsers.push(username);
    saveAuthorizedUsers();
    return true;
  }
  return false;
}

// Remove an authorized user
function removeAuthorizedUser(username) {
  // Remove @ if present
  username = username.replace(/^@/, '');
  const index = authorizedUsers.indexOf(username);
  if (index !== -1) {
    authorizedUsers.splice(index, 1);
    saveAuthorizedUsers();
    return true;
  }
  return false;
}

// Check if a user is authorized
function isUserAuthorized(username) {
  console.log(`Checking authorization for username: "${username}"`);
  console.log(`Current authorized users: ${JSON.stringify(authorizedUsers)}`);
  
  if (!username) {
    console.log("Username is empty or undefined, denying access");
    return false;
  }
  
  // Remove @ if present
  const cleanUsername = username.replace(/^@/, '');
  console.log(`Cleaned username for check: "${cleanUsername}"`);
  
  // Case-insensitive check
  const isAuthorized = authorizedUsers.some(user => 
    user.toLowerCase() === cleanUsername.toLowerCase()
  );
  
  console.log(`Authorization result for ${cleanUsername}: ${isAuthorized}`);
  return isAuthorized;
}

// Load authorized users on startup
loadAuthorizedUsers();
// Force regenerate the file to ensure it's properly formatted
regenerateAuthorizedUsersFile();

// Global object to track the last message sent by the bot per chat (for cleanup)
const lastBotMessage = {};

// --- New variable to control browser view ---
// Set headlessMode to false so the browser opens by default
let headlessMode = false;

// --- New variable for chunk size (number of pages per process) ---
let chunkSize = 10; // Default is 10 pages per chunk

// --- Global variable to track active browser instances ---
let activeBrowsers = [];

// --- Load/Save Automation Configuration ---
let minLoopDelay = 60000;
let maxLoopDelay = 600000;
let minSearchDelay = 1000;
let maxSearchDelay = 60000;

function loadAutomationConfig() {
  if (fs.existsSync(CONFIG_FILE)) {
    try {
      const config = JSON.parse(fs.readFileSync(CONFIG_FILE, "utf8"));
      minLoopDelay = config.minLoopDelay;
      maxLoopDelay = config.maxLoopDelay;
      minSearchDelay = config.minSearchDelay;
      maxSearchDelay = config.maxSearchDelay;
      
      // Load chunk size if it exists in the config
      if (config.chunkSize !== undefined) {
        chunkSize = config.chunkSize;
      }
      
      console.log("Loaded automation config:", config);
    } catch (err) {
      console.error("Error reading config, using defaults.", err);
    }
  } else {
    saveAutomationConfig();
  }
}

function saveAutomationConfig() {
  const config = {
    minLoopDelay,
    maxLoopDelay,
    minSearchDelay,
    maxSearchDelay,
    chunkSize // Add chunk size to the saved config
  };
  fs.writeFileSync(CONFIG_FILE, JSON.stringify(config, null, 2));
  console.log("Saved automation config:", config);
}

loadAutomationConfig();

// --- Randomized User-Agents and Browser Viewports ---
const randomUserAgents = [
  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36",
  "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Safari/605.1.15",
  "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36"
];
function getRandomUserAgent() {
  const userAgent = randomUserAgents[Math.floor(Math.random() * randomUserAgents.length)];
  console.log(`Random User-Agent selected: ${userAgent}`);
  return userAgent;
}

const randomViewports = [
  { width: 1920, height: 1080 },
  { width: 1366, height: 768 },
  { width: 1440, height: 900 },
  { width: 1280, height: 720 },
  { width: 1600, height: 900 }
];
function getRandomViewport() {
  const viewport = randomViewports[Math.floor(Math.random() * randomViewports.length)];
  console.log(`Random viewport selected: width=${viewport.width}, height=${viewport.height}`);
  return viewport;
}

// --- Helper Functions for Message Cleanup ---
function deletePreviousMessage(chatId) {
  if (lastBotMessage[chatId]) {
    bot.deleteMessage(chatId, lastBotMessage[chatId]).catch(err => {
      console.error(`Error deleting previous message for chat ${chatId}:`, err);
    });
    lastBotMessage[chatId] = null;
  }
}
function sendCleanMessage(chatId, text, options = {}) {
  deletePreviousMessage(chatId);
  return bot.sendMessage(chatId, text, options).then(sentMsg => {
    lastBotMessage[chatId] = sentMsg.message_id;
    return sentMsg;
  });
}

// --- File Handling: Load and Save Data ---
let posts = fs.existsSync(POSTS_FILE)
  ? JSON.parse(fs.readFileSync(POSTS_FILE, "utf8"))
  : [];
let pages = fs.existsSync(PAGES_FILE)
  ? JSON.parse(fs.readFileSync(PAGES_FILE, "utf8"))
  : [];
function savePosts() {
  fs.writeFileSync(POSTS_FILE, JSON.stringify(posts, null, 2));
}
function savePages() {
  fs.writeFileSync(PAGES_FILE, JSON.stringify(pages, null, 2));
}
function saveFilteredPost(filteredText, pageUrl, normalizedHash) {
  let filteredPosts = [];
  if (fs.existsSync(FILTERED_FILE)) {
    filteredPosts = JSON.parse(fs.readFileSync(FILTERED_FILE, "utf8"));
  }
  filteredPosts.push({
    pageUrl,
    normalizedTextHash: normalizedHash,
    filteredText,
    timestamp: new Date().toISOString()
  });
  fs.writeFileSync(FILTERED_FILE, JSON.stringify(filteredPosts, null, 2));
}
function saveFinalFilteredPost(filteredText, pageUrl, normalizedHash) {
  let finalFilteredPosts = [];
  if (fs.existsSync(FINAL_FILTERED_FILE)) {
    finalFilteredPosts = JSON.parse(fs.readFileSync(FINAL_FILTERED_FILE, "utf8"));
  }
  
  // Check if filteredText is an object (from new format) or string (from old format)
  const postData = typeof filteredText === 'object' ? 
    {
      pageUrl,
      normalizedTextHash: normalizedHash,
      pageName: filteredText.pageName,
      postTime: filteredText.postTime,
      finalFilteredText: filteredText.mainContent,
      timestamp: new Date().toISOString()
    } : 
    {
      pageUrl,
      normalizedTextHash: normalizedHash,
      finalFilteredText: filteredText,
      timestamp: new Date().toISOString()
    };
    
  finalFilteredPosts.push(postData);
  fs.writeFileSync(FINAL_FILTERED_FILE, JSON.stringify(finalFilteredPosts, null, 2));
  console.log(`Final filtered post saved for ${pageUrl}`);
}

// --- Utility Functions ---
function normalizePostText(text) {
  if (!text) return null;
  
  // First remove time references like "1m", "2h", etc.
  let result = text;
  
  // Remove the time separator character "·" which often appears after page names
  result = result.replace(/·/g, '');
  
  const containsArabic = /[\u0600-\u06FF]/.test(text);
  if (!containsArabic) {
    const readMoreIndex = result.toLowerCase().indexOf("read more");
    if (readMoreIndex !== -1) {
      result = result.substring(0, readMoreIndex);
    } else {
      const likeIndex = result.toLowerCase().indexOf("like");
      if (likeIndex !== -1) {
        result = result.substring(0, likeIndex);
      }
    }
  }
  
  // Remove time references
  result = result.replace(/\b\d+[smhd]\b/gi, '');
  
  // Remove numbers and replace periods with pipes
  result = result.replace(/\d+/g, '');
  result = result.replace(/\./g, '|');
  
  // Normalize whitespace
  result = result.replace(/\s+/g, ' ').trim();
  
  // Truncate to 50 characters if longer
  if (result.length > 50) {
    result = result.substring(0, 50);
  }
  
  return result || null;
}

function filterPostText(text) {
  const filterWords = ["comment", "like", "share", "reactions", "view more comments", "top fan"];
  let filtered = text;
  filterWords.forEach(word => {
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    filtered = filtered.replace(regex, '');
  });
  return filtered.replace(/\s+/g, ' ').trim();
}

function filterSentPostText(text) {
  if (!text) return "";
  let filtered = text;
  
  // Extract the page name, time, and main content
  let pageName = "";
  let postTime = "";
  let mainContent = "";
  let videoDuration = "";
  let isLive = false;
  
  // Try to extract the page name (usually comes before the first "·")
  const pageNameMatch = filtered.match(/^([^·]+?)(?:·|\s+\d+[smhd])/);
  if (pageNameMatch && pageNameMatch[1]) {
    pageName = pageNameMatch[1].trim();
    
    // Remove the page name from the text
    filtered = filtered.substring(pageNameMatch[1].length).trim();
    if (filtered.startsWith("·")) {
      filtered = filtered.substring(1).trim();
    }
  }
  
  // Try to extract the post time (usually appears at the beginning, like "5m", "1h", etc.)
  const timeMatch = filtered.match(/^\s*(\d+[smhd])\s*/i);
  if (timeMatch && timeMatch[1]) {
    postTime = timeMatch[1];
    filtered = filtered.substring(timeMatch[0].length).trim();
  }
  
  // Check if it's a LIVE video
  if (filtered.match(/LIVE|مباشر|بث مباشر/i)) {
    isLive = true;
  }
  
  // Try to extract video duration (format: 0:00 / 2:51)
  const durationMatch = filtered.match(/(\d+:\d+\s*\/\s*\d+:\d+)/);
  if (durationMatch && durationMatch[1]) {
    videoDuration = durationMatch[1].trim();
    // Remove the duration from the text to prevent duplicates
    filtered = filtered.replace(durationMatch[1], '');
  }
  
  // The rest is the main content
  mainContent = filtered;
  
  // Remove video duration patterns if not captured above
  mainContent = mainContent.replace(/\d+:\d+\s*\/\s*\d+:\d+/g, '');
  
  // Remove common phrases that appear at the end of Facebook posts
  const endPhrases = [
    "All reactions:",
    "All :", 
    "View more", 
    "See more", 
    "\\+ All :", 
    "\\d+ replies",
    "… See more", 
    "\\+ All : View more s", 
    "All : View more s",
    "Like",
    "Comment",
    "Share"
  ];
  
  // Create a pattern that matches any of the end phrases and everything that follows
  const endPhrasesPattern = new RegExp(`(${endPhrases.join('|')}).*$`, 's');
  mainContent = mainContent.replace(endPhrasesPattern, '');
  
  // Remove any comment sections (typically a name followed by text and timestamp)
  mainContent = mainContent.replace(/\n\s*[^(\n\r]+?(?:\n[^(\n\r]+?)?\s+\d+[smhd]\s*$/gm, '');
  
  // Remove reaction counts (numbers)
  mainContent = mainContent.replace(/\b\d+\b(?!\s*[a-zA-Z0-9])/g, '');
  
  // Remove parentheses at the end
  mainContent = mainContent.replace(/\s*\)\s*$/, '');
  
  // Remove any trailing whitespace and extra newlines
  mainContent = mainContent.trim().replace(/\n{3,}/g, '\n\n');
  
  // Return only the essential parts
  return { pageName, postTime, mainContent, videoDuration, isLive };
}

// Function to calculate actual post time from relative time
function calculateActualTime(relativeTime) {
  if (!relativeTime) return null;

  // Extract the number and unit from relative time (e.g., "4m", "2h")
  const match = relativeTime.match(/^(\d+)([smhd])$/i);
  if (!match) return null;

  const value = parseInt(match[1]);
  const unit = match[2].toLowerCase();

  // Create a date object for the current time
  const now = new Date();
  const postDate = new Date(now);

  // Subtract the appropriate amount of time based on the unit
  switch (unit) {
    case 's': // seconds
      postDate.setSeconds(now.getSeconds() - value);
      break;
    case 'm': // minutes
      postDate.setMinutes(now.getMinutes() - value);
      break;
    case 'h': // hours
      postDate.setHours(now.getHours() - value);
      break;
    case 'd': // days
      postDate.setDate(now.getDate() - value);
      break;
    default:
      return null;
  }

  // Format the time as "h:mm am/pm"
  return postDate.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
}

// Enhanced function to calculate actual post time with full date information
function calculateActualPostTime(relativeTime) {
  if (!relativeTime) return null;

  // Handle different relative time formats
  let match = relativeTime.match(/^(\d+)([smhd])$/i);
  if (!match) {
    // Try to match longer formats like "25 minutes ago", "2 hours ago"
    match = relativeTime.match(/(\d+)\s*(second|minute|hour|day)s?\s*ago/i);
    if (match) {
      const unit = match[2].toLowerCase().charAt(0); // Get first letter (s, m, h, d)
      match = [match[0], match[1], unit];
    }
  }

  if (!match) return null;

  const value = parseInt(match[1]);
  const unit = match[2].toLowerCase();

  // Create a date object for the current time
  const now = new Date();
  const postDate = new Date(now);

  // Subtract the appropriate amount of time based on the unit
  switch (unit) {
    case 's': // seconds
      postDate.setSeconds(now.getSeconds() - value);
      break;
    case 'm': // minutes
      postDate.setMinutes(now.getMinutes() - value);
      break;
    case 'h': // hours
      postDate.setHours(now.getHours() - value);
      break;
    case 'd': // days
      postDate.setDate(now.getDate() - value);
      break;
    default:
      return null;
  }

  return {
    fullDate: postDate,
    timeString: postDate.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }),
    dateString: postDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }),
    isToday: isToday(postDate),
    isoString: postDate.toISOString()
  };
}

// Helper function to check if a date is today
function isToday(date) {
  const today = new Date();
  return date.getDate() === today.getDate() &&
         date.getMonth() === today.getMonth() &&
         date.getFullYear() === today.getFullYear();
}

// Function to format post time for display - shows both relative and exact time
function formatPostTimeDisplay(postTime, relativeTime) {
  if (!postTime && !relativeTime) return 'Unknown';

  const timeToProcess = postTime || relativeTime;
  const actualTime = calculateActualPostTime(timeToProcess);

  if (!actualTime) return timeToProcess; // Return original if can't parse

  // Show both relative time and exact time
  if (actualTime.isToday) {
    return `${timeToProcess} (Today at ${actualTime.timeString})`;
  } else {
    return `${timeToProcess} (${actualTime.dateString} at ${actualTime.timeString})`;
  }
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// --- Delay Ranges ---
function randomLoopDelay() {
  const delayTime = Math.floor(Math.random() * (maxLoopDelay - minLoopDelay + 1)) + minLoopDelay;
  fs.appendFileSync("automation_times.log", JSON.stringify({ type: "loopDelay", delay: delayTime, timestamp: new Date().toISOString() }) + "\n");
  return delayTime;
}
function randomSearchDelay() {
  const delayTime = Math.floor(Math.random() * (maxSearchDelay - minSearchDelay + 1)) + minSearchDelay;
  fs.appendFileSync("automation_times.log", JSON.stringify({ type: "searchDelay", delay: delayTime, timestamp: new Date().toISOString() }) + "\n");
  return delayTime;
}

// Function to escape special Markdown characters
function escapeMarkdown(text) {
  if (!text) return '';
  
  // Characters that need to be escaped in Markdown V2: '_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!'
  return text
    .replace(/([_*[\]()~`>#+=|{}.!\\])/g, '\\$1') // Escape Markdown special characters
    .replace(/&/g, '&amp;')  // HTML entities
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;');
}

// --- Telegram Messaging Function ---
async function sendFilteredToTelegram(finalFilteredText, pageUrl, mediaUrls = []) {
  const { pageName, postTime, mainContent, videoDuration, isLive } = finalFilteredText;
  
  // Use a single emoji for all posts
  const authorEmoji = "📑"; // A single document/post emoji for all authors
  
  // Format the message with emojis
  const timeEmoji = "⏰";
  const videoEmoji = "🎬";
  const linkEmoji = "🔗";
  
  // Escape special characters in Markdown for pageName and mainContent
  const escapedPageName = escapeMarkdown(pageName);
  const escapedMainContent = mainContent; // No escaping for main content as it may contain valid formatting
  
  // Clean formatting and create the message
  let formattedMessage = `${authorEmoji} *${pageName}*\n\n`;
  
  // Only add time section if there is a post time
  if (postTime) {
    // Calculate actual time with enhanced formatting
    const actualTime = calculateActualPostTime(postTime);
    if (actualTime) {
      const displayTime = formatPostTimeDisplay(postTime);
      formattedMessage += `${timeEmoji} *${postTime}* (${displayTime})\n\n`;
    } else {
      formattedMessage += `${timeEmoji} *${postTime}*\n\n`;
    }
  }
  
  // Add video duration if it exists and it's not a live video
  if (videoDuration && !isLive) {
    formattedMessage += `${videoEmoji} *${videoDuration}*\n\n`;
  }
  
  // Add the main content
  formattedMessage += `${escapedMainContent}\n\n`;
  
  // Add the original post link
  formattedMessage += `${linkEmoji} [عرض المنشور الأصلي](${pageUrl})`;
  
  try {
    // Try sending with parse_mode: "HTML" first, which has fewer parsing issues
    if (mediaUrls && mediaUrls.length > 0) {
      if (mediaUrls.length === 1) {
        // Convert Markdown-style formatting to HTML
        const htmlMessage = formattedMessage
          .replace(/\\\*/g, '*') // First unescape asterisks
          .replace(/\*([^*]+)\*/g, '<b>$1</b>') // Then convert *text* to <b>text</b>
          .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>'); // Convert links
        
        await axios.post(`https://api.telegram.org/bot${process.env.TOKEN}/sendPhoto`, {
          chat_id: process.env.CHAT_ID,
          photo: mediaUrls[0],
          caption: htmlMessage,
          parse_mode: "HTML"
        });
      } else {
        const htmlMessage = formattedMessage
          .replace(/\\\*/g, '*')
          .replace(/\*([^*]+)\*/g, '<b>$1</b>')
          .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
        
        const mediaGroup = mediaUrls.map((url, index) => ({
          type: "photo",
          media: url,
          ...(index === 0 && { caption: htmlMessage, parse_mode: "HTML" })
        }));
        await axios.post(`https://api.telegram.org/bot${process.env.TOKEN}/sendMediaGroup`, {
          chat_id: process.env.CHAT_ID,
          media: mediaGroup
        });
      }
    } else {
      const htmlMessage = formattedMessage
        .replace(/\\\*/g, '*')
        .replace(/\*([^*]+)\*/g, '<b>$1</b>')
        .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');
      
      await axios.post(`https://api.telegram.org/bot${process.env.TOKEN}/sendMessage`, {
        chat_id: process.env.CHAT_ID,
        text: htmlMessage,
        parse_mode: "HTML"
      });
    }
    console.log(`Post sent from ${pageUrl} (with HTML formatting)`);
  } catch (err) {
    console.error("Telegram API Error (HTML):", err.response ? err.response.data : err.message);
    
    // If HTML formatting fails, try with plain text (no formatting)
    try {
      console.log("Attempting to send with plain text (no formatting)...");
      
      // Strip all formatting and just send plain text
      const plainMessage = formattedMessage
        .replace(/\\\*/g, '*')   // Unescape asterisks
        .replace(/\*([^*]+)\*/g, '$1')  // Remove bold formatting
        .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1 ($2)'); // Convert links to plain text
      
      if (mediaUrls && mediaUrls.length > 0) {
        if (mediaUrls.length === 1) {
          const proxyAxios = createProxyAxios();
          await proxyAxios.post(`https://api.telegram.org/bot${process.env.TOKEN}/sendPhoto`, {
            chat_id: process.env.CHAT_ID,
            photo: mediaUrls[0],
            caption: plainMessage
          });
        } else {
          const mediaGroup = mediaUrls.map((url, index) => ({
            type: "photo",
            media: url,
            ...(index === 0 && { caption: plainMessage })
          }));
          await axios.post(`https://api.telegram.org/bot${process.env.TOKEN}/sendMediaGroup`, {
            chat_id: process.env.CHAT_ID,
            media: mediaGroup
          });
        }
      } else {
        await axios.post(`https://api.telegram.org/bot${process.env.TOKEN}/sendMessage`, {
          chat_id: process.env.CHAT_ID,
          text: plainMessage
        });
      }
      console.log(`Post sent from ${pageUrl} (with plain text)`);
    } catch (plainErr) {
      console.error("Plain text also failed:", plainErr.response ? plainErr.response.data : plainErr.message);
    }
  }
}

// --- New Helper: Process a Page in an Existing Tab ---
async function processPageInTab(page, pageUrl) {
  console.log(`Navigating to: ${pageUrl}`);
  await page.goto(pageUrl, { waitUntil: "networkidle2" });
  try {
    await delay(2000);
    await page.evaluate(() => {
      const btn = Array.from(document.querySelectorAll("button")).find(b =>
        b.innerText && b.innerText.toLowerCase().includes("decline optional cookies")
      );
      if (btn) {
        btn.click();
        console.log("Clicked 'decline optional cookies' button");
      }
    });
  } catch (err) {
    console.log("No 'decline optional cookies' button found or error occurred:", err.message);
  }
  await page.evaluate(() => {
    const readMoreBtn = Array.from(document.querySelectorAll("div, a")).find(el =>
      el.innerText && el.innerText.toLowerCase().includes("read more")
    );
    if (readMoreBtn) {
      readMoreBtn.click();
    }
  });
  await delay(2000);
  console.log("Performing random, slow scroll event");
  await page.evaluate(async () => {
    const totalScroll = Math.floor(Math.random() * 500);
    const step = Math.floor(Math.random() * (50 - 10 + 1)) + 10;
    const delayMs = Math.floor(Math.random() * (200 - 50 + 1)) + 50;
    for (let scrolled = 0; scrolled < totalScroll; scrolled += step) {
      window.scrollBy(0, step);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  });
  const idleTime = Math.floor(Math.random() * (3000 - 1000 + 1)) + 1000;
  console.log(`Idling for ${idleTime} ms after scrolling`);
  await delay(idleTime);
  const result = await page.evaluate(() => {
    const postElement = document.querySelector("[role=article]");
    if (!postElement) return null;
    const postText = postElement.innerText.trim();
    const mediaUrls = Array.from(postElement.querySelectorAll("img"))
      .map(img => img.src)
      .filter(src => src.includes("scontent"));
    return { postText, mediaUrls };
  });
  return result;
}

// --- New Helper: Process a Chunk of Pages Using a Single Tab ---
async function processChunk(chunk) {
  console.log(`Processing a chunk of ${chunk.length} pages.`);
  // Get proxy configuration for bot browser
  const { launchOptions, proxyAuth, proxyString } = getPuppeteerProxyOptions();
  const browser = await puppeteer.launch({
    headless: headlessMode,
    ...launchOptions
  });

  // Set up proxy authentication for bot browser
  if (proxyAuth) {
    const pages = await browser.pages();
    if (pages.length > 0) {
      await setPuppeteerProxyAuth(pages[0], proxyAuth);
    }
  }
  // Add browser to active browsers list
  activeBrowsers.push(browser);
  
  let context;
  if (typeof browser.createIncognitoBrowserContext === "function") {
    context = await browser.createIncognitoBrowserContext();
    console.log("Using incognito context");
  } else {
    context = browser.defaultBrowserContext();
    console.log("Using default browser context (incognito not available)");
  }
  const page = await context.newPage();
  // Set a random user agent and viewport once for the chunk.
  await page.setUserAgent(getRandomUserAgent());
  await page.setViewport(getRandomViewport());
  for (const pageObj of chunk) {
    try {
      // Check if monitoring was stopped
      if (!botRunning) {
        console.log("Monitoring stopped during chunk processing.");
        break;
      }
      
      const currentPost = await processPageInTab(page, pageObj.link);
      if (currentPost && currentPost.postText) {
        const normalizedPostText = normalizePostText(currentPost.postText);
        if (!normalizedPostText) {
          console.log(`Post from ${pageObj.link} is invalid.`);
          continue;
        }
        const normalizedPostTextHash = normalizedPostText.substring(0, 50);
        
        // Make sure we're checking both page URL and normalized text hash for duplicates
        const duplicate = posts.find(p => 
          p.pageUrl === pageObj.link && 
          p.normalizedTextHash === normalizedPostTextHash
        );
        
        if (duplicate) {
          console.log(`Duplicate post for ${pageObj.link} with hash "${normalizedPostTextHash}" detected. Skipping.`);
          continue;
        }
        posts.push({ pageUrl: pageObj.link, normalizedTextHash: normalizedPostTextHash });
        savePosts();
        console.log(`New post detected from ${pageObj.link} with hash "${normalizedPostTextHash}".`);
        saveFilteredPost(normalizedPostTextHash, pageObj.link, normalizedPostTextHash);
        const finalFilteredText = filterSentPostText(currentPost.postText);
        saveFinalFilteredPost(finalFilteredText, pageObj.link, normalizedPostTextHash);
        await sendFilteredToTelegram(finalFilteredText, pageObj.link, currentPost.mediaUrls);
      } else {
        console.log(`No post data found for ${pageObj.link}.`);
      }
    } catch (e) {
      console.error(`Error processing ${pageObj.link}:`, e);
    }
    // No wait between pages within the chunk
  }
  
  // Remove browser from active browsers list before closing
  const browserIndex = activeBrowsers.indexOf(browser);
  if (browserIndex !== -1) {
    activeBrowsers.splice(browserIndex, 1);
  }

  // ✅ FIXED: Mark proxy as successful after chunk completion (no double counting)
  if (proxyString) {
    proxyManager.markProxyAsSuccessful(proxyString, 0);
    console.log(`✅ Marked proxy as successful for chunk of ${chunk.length} pages`);
  }

  await browser.close();
  console.log("Browser closed after chunk processing");
}

// --- Monitoring Loop ---
let botRunning = false;
let monitoringLoopId = null; // To keep track of the monitoring loop

async function startLoop() {
  botRunning = true;
  console.log("\x1b[32m%s\x1b[0m", "Bot started. Using PARALLEL processing only...");

  // ✅ DISABLED: Sequential chunk processing to prevent double processing
  console.log("🚫 Sequential chunk processing DISABLED to prevent double processing");
  console.log("✅ Using PARALLEL processing only (configured elsewhere)");

  // Don't schedule next loop if monitoring was stopped
  if (!botRunning) {
    console.log("Monitoring stopped. Not scheduling next loop.");
    return;
  }

  const loopDelay = randomLoopDelay();
  console.log("\x1b[32m%s\x1b[0m", `⏱️ Waiting for ${Math.floor(loopDelay / 60000)} minutes and ${((loopDelay % 60000) / 1000).toFixed(0)} seconds before next cycle.`);

  // Store the timeout ID so we can cancel it if needed
  monitoringLoopId = setTimeout(() => startLoop(), loopDelay);
}

// --- Monitoring Control Functions ---
async function stopLoop() {
  console.log("Stopping monitoring process...");
  botRunning = false;
  
  // Clear any pending monitoring loop timeout
  if (monitoringLoopId) {
    clearTimeout(monitoringLoopId);
    monitoringLoopId = null;
    console.log("Cleared pending monitoring loop timeout");
  }
  
  // Close all active browser instances
  if (activeBrowsers.length > 0) {
    console.log(`Closing ${activeBrowsers.length} active browser instance(s)...`);
    for (const browser of activeBrowsers) {
      try {
        await browser.close();
        console.log("Browser instance closed successfully");
      } catch (err) {
        console.error("Error closing browser:", err.message);
      }
    }
    // Clear the active browsers array
    activeBrowsers = [];
  }
  
  console.log("Monitoring completely stopped.");
}

async function resumeLoop() {
  if (botRunning) {
    console.log("Monitoring is already running.");
    return;
  }
  
  // Make sure everything is properly stopped first
  await stopLoop();
  
  console.log("Starting fresh monitoring loop...");
  // Start a completely new monitoring process
  startLoop();
}

// --- User Interaction: Menu and Commands ---
function showMenu(chatId) {
  const menuOptions = {
    reply_markup: {
      keyboard: [
        [{ text: "عرض الصفحات" }, { text: "إضافة صفحة" }],
        [{ text: "حذف صفحة" }],
        [{ text: "ضبط الوقت" }, { text: "ضبط وقت البحث" }],
        [{ text: "ضبط عدد الصفحات في الدُفعة" }],
        [{ text: "إيقاف المراقبة" }, { text: "تشغيل المراقبة" }],
        [{ text: "تشغيل العرض" }, { text: "تعطيل العرض" }],
        [{ text: "إدارة المستخدمين" }]
      ],
      resize_keyboard: true,
      one_time_keyboard: false
    }
  };
  sendCleanMessage(chatId, "اختر خياراً:", menuOptions);
}

// Show user management menu
function showUserManagementMenu(chatId) {
  const menuOptions = {
    reply_markup: {
      keyboard: [
        [{ text: "عرض المستخدمين المصرح لهم" }],
        [{ text: "إضافة مستخدم" }, { text: "حذف مستخدم" }],
        [{ text: "العودة للقائمة الرئيسية" }]
      ],
      resize_keyboard: true,
      one_time_keyboard: false
    }
  };
  sendCleanMessage(chatId, "إدارة المستخدمين:", menuOptions);
}

const userStates = {};
bot.on("message", async (msg) => {
  const chatId = msg.chat.id;
  const text = msg.text && msg.text.trim();
  const username = msg.from && msg.from.username;
  
  // Log user information for debugging
  console.log("Received message from user:", {
    chatId,
    username,
    firstName: msg.from && msg.from.first_name,
    lastName: msg.from && msg.from.last_name,
    userId: msg.from && msg.from.id,
    fullUserObject: msg.from
  });
  
  // Check if the user is authorized or this is their first interaction
  if (!isUserAuthorized(username)) {
    console.log(`User ${username} is not authorized, sending access denied message`);
    sendCleanMessage(chatId, `عذراً، أنت غير مصرح لك باستخدام هذا البوت. اسم المستخدم الخاص بك: @${username || "غير معرف"}`);
    return;
  }
  
  console.log(`User ${username} is authorized, processing command: ${text}`);
  
  if (["عرض الصفحات", "إضافة صفحة", "حذف صفحة", "ضبط الوقت", "ضبط وقت البحث", "ضبط عدد الصفحات في الدُفعة", "إيقاف المراقبة", "تشغيل المراقبة", "تشغيل العرض", "تعطيل العرض", "إدارة المستخدمين", "العودة للقائمة الرئيسية"].includes(text)) {
    userStates[chatId] = null;
    console.log(`State reset for chat ${chatId} due to command: ${text}`);
  }
  
  // Handle user management commands
  if (text === "إدارة المستخدمين") {
    showUserManagementMenu(chatId);
    return;
  } else if (text === "العودة للقائمة الرئيسية") {
    showMenu(chatId);
    return;
  } else if (text === "عرض المستخدمين المصرح لهم") {
    let userList = "المستخدمون المصرح لهم:\n";
    authorizedUsers.forEach((user, index) => {
      userList += `${index + 1}. @${user}\n`;
    });
    sendCleanMessage(chatId, userList);
    return;
  } else if (text === "إضافة مستخدم") {
    userStates[chatId] = { action: "addingUser" };
    sendCleanMessage(chatId, "يرجى إدخال اسم المستخدم الذي تريد إضافته (مثال: @username):");
    return;
  } else if (text === "حذف مستخدم") {
    if (authorizedUsers.length <= 1) {
      sendCleanMessage(chatId, "لا يمكن حذف جميع المستخدمين المصرح لهم. يجب أن يبقى مستخدم واحد على الأقل.");
      return;
    }
    let userList = "اختر المستخدم الذي تريد حذفه:\n";
    authorizedUsers.forEach((user, index) => {
      userList += `${index + 1}. @${user}\n`;
    });
    sendCleanMessage(chatId, userList);
    userStates[chatId] = { action: "removingUser" };
    return;
  } else if (userStates[chatId] && userStates[chatId].action === "addingUser") {
    const newUsername = text.trim().replace(/^@/, '');
    if (newUsername && newUsername.length > 0) {
      if (addAuthorizedUser(newUsername)) {
        sendCleanMessage(chatId, `تمت إضافة المستخدم @${newUsername} بنجاح.`);
      } else {
        sendCleanMessage(chatId, `المستخدم @${newUsername} مصرح له بالفعل.`);
      }
    } else {
      sendCleanMessage(chatId, "اسم المستخدم غير صالح. يرجى المحاولة مرة أخرى.");
    }
    userStates[chatId] = null;
    showUserManagementMenu(chatId);
    return;
  } else if (userStates[chatId] && userStates[chatId].action === "removingUser") {
    const index = parseInt(text);
    if (!isNaN(index) && index >= 1 && index <= authorizedUsers.length) {
      const username = authorizedUsers[index - 1];
      if (removeAuthorizedUser(username)) {
        sendCleanMessage(chatId, `تم حذف المستخدم @${username} بنجاح.`);
      } else {
        sendCleanMessage(chatId, "حدث خطأ أثناء حذف المستخدم.");
      }
    } else {
      sendCleanMessage(chatId, "اختيار غير صالح. يرجى إدخال رقم صحيح.");
    }
    userStates[chatId] = null;
    showUserManagementMenu(chatId);
    return;
  }
  
  // Handle main menu commands
  if (text === "إيقاف المراقبة") {
    sendCleanMessage(chatId, "جاري إيقاف المراقبة...");
    try {
      await stopLoop();
      sendCleanMessage(chatId, "تم إيقاف المراقبة بنجاح. تم إغلاق جميع نوافذ المتصفح.");
    } catch (err) {
      console.error("Error stopping loop:", err);
      sendCleanMessage(chatId, "حدث خطأ أثناء إيقاف المراقبة. يرجى المحاولة مرة أخرى.");
    }
    showMenu(chatId);
    return;
  }
  if (text === "تشغيل المراقبة") {
    sendCleanMessage(chatId, "جاري تشغيل المراقبة...");
    try {
      await resumeLoop();
      sendCleanMessage(chatId, "تم تشغيل المراقبة بنجاح.");
    } catch (err) {
      console.error("Error resuming loop:", err);
      sendCleanMessage(chatId, "حدث خطأ أثناء تشغيل المراقبة. يرجى المحاولة مرة أخرى.");
    }
    showMenu(chatId);
    return;
  }
  if (text === "تشغيل العرض") {
    headlessMode = false;
    sendCleanMessage(chatId, "تم تشغيل العرض. سيتم تشغيل المتصفح بواجهة رسومية (غير مخفي).");
    showMenu(chatId);
    return;
  }
  if (text === "تعطيل العرض") {
    headlessMode = true;
    sendCleanMessage(chatId, "تم تعطيل العرض. سيتم تشغيل المتصفح بوضع عدم العرض (مخفي).");
    showMenu(chatId);
    return;
  }
  if (text === "ضبط الوقت") {
    sendCleanMessage(chatId, "يرجى إدخال المدى المطلوب بالصيغة X-Ym (مثال: 1-8m):");
    userStates[chatId] = { action: "adjustTime" };
    return;
  }
  if (userStates[chatId] && userStates[chatId].action === "adjustTime") {
    const regex = /^(\d+)-(\d+)m$/i;
    const match = text.match(regex);
    if (match) {
      const minMinutes = parseInt(match[1], 10);
      const maxMinutes = parseInt(match[2], 10);
      if (minMinutes > 0 && maxMinutes >= minMinutes) {
        minLoopDelay = minMinutes * 60000;
        maxLoopDelay = maxMinutes * 60000;
        saveAutomationConfig();
        sendCleanMessage(chatId, `تم ضبط المدى الزمني للفحص بين ${minMinutes} و ${maxMinutes} دقيقة.`);
      } else {
        sendCleanMessage(chatId, "القيم المدخلة غير صحيحة. تأكد أن القيمة الصغرى أكبر من 0 وأن القيمة الكبرى لا تقل عنها.");
      }
    } else {
      sendCleanMessage(chatId, "صيغة الإدخال غير صحيحة. يرجى إدخال المدى مثل 1-8m.");
    }
    userStates[chatId] = null;
    showMenu(chatId);
    return;
  }
  if (text === "ضبط وقت البحث") {
    sendCleanMessage(chatId, "يرجى إدخال المدى المطلوب للبحث بالصيغة X-Ys (مثال: 1-60s):");
    userStates[chatId] = { action: "adjustSearchTime" };
    return;
  }
  if (userStates[chatId] && userStates[chatId].action === "adjustSearchTime") {
    const regex = /^(\d+)-(\d+)s$/i;
    const match = text.match(regex);
    if (match) {
      const minSeconds = parseInt(match[1], 10);
      const maxSeconds = parseInt(match[2], 10);
      if (minSeconds > 0 && maxSeconds >= minSeconds) {
        minSearchDelay = minSeconds * 1000;
        maxSearchDelay = maxSeconds * 1000;
        saveAutomationConfig();
        sendCleanMessage(chatId, `تم ضبط وقت البحث بين الصفحات بين ${minSeconds} و ${maxSeconds} ثانية.`);
      } else {
        sendCleanMessage(chatId, "القيم المدخلة غير صحيحة. تأكد أن القيمة الصغرى أكبر من 0 وأن القيمة الكبرى لا تقل عنها.");
      }
    } else {
      sendCleanMessage(chatId, "صيغة الإدخال غير صحيحة. يرجى إدخال المدى مثل 1-60s.");
    }
    userStates[chatId] = null;
    showMenu(chatId);
    return;
  }
  if (text === "ضبط عدد الصفحات في الدُفعة") {
    sendCleanMessage(chatId, "يرجى إدخال عدد الصفحات في الدُفعة (مثال: 10):");
    userStates[chatId] = { action: "adjustChunkSize" };
    return;
  }
  if (userStates[chatId] && userStates[chatId].action === "adjustChunkSize") {
    const num = parseInt(text, 10);
    if (!isNaN(num) && num > 0) {
      chunkSize = num;
      // Save the configuration to persist the chunk size
      saveAutomationConfig();
      sendCleanMessage(chatId, `تم ضبط عدد الصفحات في الدُفعة إلى ${chunkSize}.`);
    } else {
      sendCleanMessage(chatId, "يرجى إدخال رقم صالح أكبر من 0.");
    }
    userStates[chatId] = null;
    showMenu(chatId);
    return;
  }
  if (userStates[chatId] && userStates[chatId].action === "addingPage") {
    if (userStates[chatId].step === "waitingForName") {
      console.log(`Chat ${chatId}: Received page name: ${text}`);
      userStates[chatId].pageName = text;
      userStates[chatId].step = "waitingForLink";
      sendCleanMessage(chatId, "يرجى إرسال رابط الصفحة:");
      return;
    } else if (userStates[chatId].step === "waitingForLink") {
      console.log(`Chat ${chatId}: Received page link: ${text}`);
      const pageName = userStates[chatId].pageName;
      const pageLink = text;
      pages.push({ name: pageName, link: pageLink });
      savePages();
      sendCleanMessage(chatId, `تم إضافة الصفحة "${pageName}" بنجاح.`);
      userStates[chatId] = null;
      showMenu(chatId);
      return;
    }
  }
  if (text === "إضافة صفحة") {
    console.log(`Chat ${chatId}: Command "إضافة صفحة" received.`);
    userStates[chatId] = { action: "addingPage", step: "waitingForName" };
    sendCleanMessage(chatId, "يرجى إرسال اسم الصفحة التي تريد إضافتها:");
    return;
  } else if (text === "عرض الصفحات") {
    console.log(`Chat ${chatId}: Command "عرض الصفحات" received.`);
    if (pages.length === 0) {
      sendCleanMessage(chatId, "لا توجد صفحات.");
    } else {
      let list = "الصفحات:\n";
      pages.forEach((page, i) => {
        list += `${i + 1}. ${page.name}\n`;
      });
      sendCleanMessage(chatId, list);
    }
    showMenu(chatId);
    return;
  } else if (text === "حذف صفحة") {
    console.log(`Chat ${chatId}: Command "حذف صفحة" received.`);
    if (pages.length === 0) {
      sendCleanMessage(chatId, "لا توجد صفحات للحذف.");
      showMenu(chatId);
    } else {
      let list = "الصفحات:\n";
      pages.forEach((page, i) => {
        list += `${i + 1}. ${page.name}\n`;
      });
      sendCleanMessage(chatId, list + "\nيرجى إرسال رقم الصفحة التي تريد حذفها:");
      userStates[chatId] = { action: "removingPage" };
    }
    return;
  } else if (userStates[chatId] && userStates[chatId].action === "removingPage") {
    const index = parseInt(text);
    if (!isNaN(index) && index >= 1 && index <= pages.length) {
      const removedPage = pages.splice(index - 1, 1)[0];
      savePages();
      sendCleanMessage(chatId, `تم حذف الصفحة: ${removedPage.name}`);
    } else {
      sendCleanMessage(chatId, "اختيار غير صحيح. يرجى إرسال رقم صالح.");
    }
    userStates[chatId] = null;
    showMenu(chatId);
    return;
  } else {
    showMenu(chatId);
  }
});

// --- Start Monitoring Loop ---
startLoop();

// Function to forcibly regenerate authorized users file with correct format
function regenerateAuthorizedUsersFile() {
  console.log("Regenerating authorized_users.json file with correct format");
  // Ensure randomiz643 is included
  if (!authorizedUsers.includes("randomiz643")) {
    authorizedUsers.push("randomiz643");
  }
  
  // Write the file with proper JSON formatting
  const jsonContent = JSON.stringify(authorizedUsers, null, 2);
  console.log(`Writing to authorized_users.json: ${jsonContent}`);
  
  try {
    fs.writeFileSync(AUTHORIZED_USERS_FILE, jsonContent);
    console.log("Successfully regenerated authorized_users.json");
  } catch (err) {
    console.error("Error writing authorized_users.json:", err);
  }
}
