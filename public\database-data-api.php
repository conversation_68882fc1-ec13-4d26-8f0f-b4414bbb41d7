<?php
// Database Data API for Updates Page
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "facebook_db";

try {
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    $action = $_GET['action'] ?? 'posts';
    
    switch ($action) {
        case 'posts':
            $result = $conn->query("
                SELECT
                    p.id,
                    p.page_id,
                    p.member_id,
                    p.caption,
                    p.media_id,
                    p.likes_count,
                    p.comments_count,
                    p.views_count,
                    p.shares_count,
                    p.post_url,
                    p.post_date,
                    p.last_metrics_update,
                    p.extraction_timestamp,
                    p.created_at,
                    pg.name as page_name,
                    m.name as member_name
                FROM posts p
                LEFT JOIN pages pg ON p.page_id = pg.id
                LEFT JOIN members m ON p.member_id = m.id
                ORDER BY p.post_date DESC
            ");
            break;
            
        case 'pages':
            $result = $conn->query("
                SELECT 
                    p.id,
                    p.name,
                    p.url,
                    p.member_id,
                    p.created_at,
                    m.name as member_name,
                    COUNT(posts.id) as post_count
                FROM pages p
                LEFT JOIN members m ON p.member_id = m.id
                LEFT JOIN posts ON p.id = posts.page_id
                GROUP BY p.id, p.name, p.url, p.member_id, p.created_at, m.name
                ORDER BY p.name
            ");
            break;
            
        case 'members':
            $result = $conn->query("
                SELECT 
                    m.id,
                    m.name,
                    m.email,
                    m.role,
                    m.created_at,
                    COUNT(DISTINCT p.id) as page_count,
                    COUNT(DISTINCT posts.id) as post_count
                FROM members m
                LEFT JOIN pages p ON m.id = p.member_id
                LEFT JOIN posts ON m.id = posts.member_id
                GROUP BY m.id, m.name, m.email, m.role, m.created_at
                ORDER BY m.name
            ");
            break;
            
        case 'stats':
            // Get overall statistics
            $stats = [];
            
            $result = $conn->query("SELECT COUNT(*) as count FROM posts");
            $stats['posts'] = $result->fetch_assoc()['count'];
            
            $result = $conn->query("SELECT COUNT(*) as count FROM pages");
            $stats['pages'] = $result->fetch_assoc()['count'];
            
            $result = $conn->query("SELECT COUNT(*) as count FROM members");
            $stats['members'] = $result->fetch_assoc()['count'];
            
            echo json_encode([
                'success' => true,
                'data' => $stats
            ]);
            exit;
            
        default:
            throw new Exception("Invalid action: " . $action);
    }
    
    if (!$result) {
        throw new Exception("Query failed: " . $conn->error);
    }
    
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $data,
        'count' => count($data)
    ]);
    
    $conn->close();
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
