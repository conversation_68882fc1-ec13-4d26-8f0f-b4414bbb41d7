<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Database - Facebook Posts App</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="updates.css">
  <link rel="icon" href="favicon.ico">
</head>
<body>
  <div class="app-container">
    <!-- Left Sidebar Navigation -->
    <nav class="left-sidebar">
      <div class="sidebar-header">
        <i class="fab fa-facebook"></i>
        <span>Facebook App</span>
      </div>
      <ul class="sidebar-menu">
        <li>
          <a href="/index.html" class="sidebar-link">
            <i class="fas fa-home"></i>
            <span>Posts</span>
          </a>
        </li>
        <li>
          <a href="/team.html" class="sidebar-link">
            <i class="fas fa-users"></i>
            <span>Team</span>
          </a>
        </li>
        <li>
          <a href="/updates.html" class="sidebar-link active">
            <i class="fas fa-database"></i>
            <span>Database</span>
          </a>
        </li>
        <li>
          <a href="/proxy-manager.html" class="sidebar-link">
            <i class="fas fa-network-wired"></i>
            <span>Proxy Manager</span>
          </a>
        </li>
      </ul>
    </nav>

    <!-- Main Content -->
    <div class="main-content">

    <div class="updates-container">
      <div class="updates-header">
        <h2><i class="fas fa-table"></i> Data Tables Overview</h2>
        <div class="refresh-controls">
          <div class="auto-sync-indicator" id="header-auto-sync-indicator">
            <i class="fas fa-clock"></i>
            <span id="header-sync-status">Auto-sync: OFF</span>
            <span id="header-countdown" style="display: none;">Next: --</span>
          </div>
          <button id="refresh-all" class="btn primary">
            <i class="fas fa-sync-alt"></i> Refresh All
          </button>
          <button class="theme-toggle">
            <i class="fas fa-moon"></i>
          </button>
          <span class="last-updated" id="last-updated">
            Last updated: <span id="last-updated-time">Loading...</span>
          </span>
        </div>
      </div>

      <!-- Database Sync Panel - Always Visible -->
      <div class="sync-panel" id="sync-panel">
        <div class="sync-header">
          <h3><i class="fas fa-database"></i> Database Synchronization</h3>
          <button class="sync-minimize" onclick="toggleSyncPanel()">
            <i class="fas fa-chevron-up" id="sync-toggle-icon"></i>
          </button>
        </div>
        <div class="sync-content">
          <div class="sync-status" id="sync-status">
            <div class="status-loading">
              <i class="fas fa-spinner fa-spin"></i> Checking sync status...
            </div>
          </div>
          <div class="sync-actions hidden" id="sync-actions">
            <div class="sync-options">
              <div class="sync-option">
                <button id="test-extraction-btn" class="btn warning">
                  <i class="fas fa-flask"></i> Test Connection
                </button>
                <p>Test database and Electron app connections</p>
              </div>
              <div class="sync-option">
                <button id="sync-from-json" class="btn primary">
                  <i class="fas fa-upload"></i> Extract Posts → Database
                </button>
                <p>Extract core post data and relationships to MySQL database</p>
              </div>
              <div class="sync-option">
                <button id="sync-to-json" class="btn secondary">
                  <i class="fas fa-download"></i> Sync Database → JSON
                </button>
                <p>Export data from MySQL database to JSON files</p>
              </div>
            </div>

            <!-- Auto-Sync Timer Section -->
            <div class="auto-sync-section">
              <div class="auto-sync-header">
                <h4><i class="fas fa-clock"></i> Auto-Sync Timer</h4>
                <div class="auto-sync-toggle">
                  <label class="switch">
                    <input type="checkbox" id="auto-sync-enabled">
                    <span class="slider"></span>
                  </label>
                  <span id="auto-sync-status">OFF</span>
                </div>
              </div>

              <div class="auto-sync-controls">
                <div class="timer-setting">
                  <label for="sync-timer">Run "Extract Posts → Database" every:</label>
                  <select id="sync-timer">
                    <option value="60000">1 minute</option>
                    <option value="300000" selected>5 minutes</option>
                    <option value="600000">10 minutes</option>
                    <option value="900000">15 minutes</option>
                    <option value="1800000">30 minutes</option>
                    <option value="3600000">1 hour</option>
                    <option value="7200000">2 hours</option>
                  </select>
                </div>

                <div class="auto-sync-info" id="auto-sync-info" style="display: none;">
                  <div class="next-run">
                    <i class="fas fa-clock"></i>
                    Next sync in: <span id="countdown-timer">--</span>
                  </div>
                  <div class="sync-count">
                    <i class="fas fa-sync"></i>
                    Total syncs: <span id="sync-count">0</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="sync-results hidden" id="sync-results">
            <h4>Sync Results:</h4>
            <div id="sync-results-content"></div>
          </div>
        </div>
      </div>

      <!-- Table Navigation -->
      <div class="table-nav">
        <button class="table-nav-btn active" data-table="posts">
          <i class="fas fa-file-alt"></i> Posts <span class="count" id="posts-count">0</span>
        </button>
        <button class="table-nav-btn" data-table="pages">
          <i class="fas fa-globe"></i> Pages <span class="count" id="pages-count">0</span>
        </button>
        <button class="table-nav-btn" data-table="members">
          <i class="fas fa-users"></i> Members <span class="count" id="members-count">0</span>
        </button>
      </div>

      <!-- Search and Filter Controls -->
      <div class="table-controls">
        <div class="search-container">
          <input type="text" id="table-search" placeholder="Search..." class="search-input">
          <i class="fas fa-search search-icon"></i>
        </div>
        <div class="filter-container">
          <select id="table-filter" class="filter-select">
            <option value="">All</option>
          </select>
        </div>
        <div class="export-container">
          <button id="export-csv" class="btn secondary">
            <i class="fas fa-download"></i> Export CSV
          </button>
        </div>
      </div>

      <!-- Posts Table -->
      <div class="table-section active" id="posts-section">
        <div class="table-header">
          <h3><i class="fas fa-file-alt"></i> Posts Data</h3>
          <div class="table-info">
            <span class="table-description">Posts data from database</span>
          </div>
        </div>
        <div class="table-container">
          <div class="table-wrapper">
            <table class="data-table" id="posts-table">
            <thead>
              <tr>
                <th data-sort="id">ID <i class="fas fa-sort"></i></th>
                <th data-sort="page_id">Page ID <i class="fas fa-sort"></i></th>
                <th data-sort="member_id">Member ID <i class="fas fa-sort"></i></th>
                <th data-sort="caption">Caption <i class="fas fa-sort"></i></th>
                <th data-sort="media_urls">Media URLs <i class="fas fa-sort"></i></th>
                <th data-sort="likes_count">Likes <i class="fas fa-sort"></i></th>
                <th data-sort="comments_count">Comments <i class="fas fa-sort"></i></th>
                <th data-sort="views_count">Views <i class="fas fa-sort"></i></th>
                <th data-sort="shares_count">Shares <i class="fas fa-sort"></i></th>
                <th data-sort="post_url">Post URL <i class="fas fa-sort"></i></th>
                <th data-sort="post_date">Post Date <i class="fas fa-sort"></i></th>
                <th data-sort="last_metrics_update">Last Update <i class="fas fa-sort"></i></th>
                <th data-sort="extraction_timestamp">Extracted <i class="fas fa-sort"></i></th>
                <th data-sort="created_at">Created At <i class="fas fa-sort"></i></th>
              </tr>
            </thead>
            <tbody id="posts-tbody">
              <!-- Posts data will be loaded here -->
            </tbody>
          </table>
          </div>
        </div>
      </div>

      <!-- Pages Table -->
      <div class="table-section" id="pages-section">
        <div class="table-header">
          <h3><i class="fas fa-globe"></i> Pages Data</h3>
          <div class="table-info">
            <span class="table-description">Facebook pages data from database</span>
          </div>
        </div>
        <div class="table-container">
          <div class="table-wrapper">
            <table class="data-table" id="pages-table">
            <thead>
              <tr>
                <th data-sort="id">ID <i class="fas fa-sort"></i></th>
                <th data-sort="name">Name <i class="fas fa-sort"></i></th>
                <th data-sort="url">URL <i class="fas fa-sort"></i></th>
                <th data-sort="member_id">Member ID <i class="fas fa-sort"></i></th>
                <th data-sort="created_at">Created At <i class="fas fa-sort"></i></th>
              </tr>
            </thead>
            <tbody id="pages-tbody">
              <!-- Pages data will be loaded here -->
            </tbody>
          </table>
          </div>
        </div>
      </div>

      <!-- Members Table -->
      <div class="table-section" id="members-section">
        <div class="table-header">
          <h3><i class="fas fa-users"></i> Members Data</h3>
          <div class="table-info">
            <span class="table-description">Team members information</span>
          </div>
        </div>
        <div class="table-container">
          <div class="table-wrapper">
            <table class="data-table" id="members-table">
            <thead>
              <tr>
                <th data-sort="memberId">Member ID <i class="fas fa-sort"></i></th>
                <th data-sort="memberName">Member Name <i class="fas fa-sort"></i></th>
                <th data-sort="role">Role <i class="fas fa-sort"></i></th>
                <th data-sort="email">Email <i class="fas fa-sort"></i></th>
              </tr>
            </thead>
            <tbody id="members-tbody">
              <!-- Members data will be loaded here -->
            </tbody>
          </table>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div class="loading-state" id="loading-state">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p>Loading data...</p>
      </div>

      <!-- Empty State -->
      <div class="empty-state hidden" id="empty-state">
        <div class="empty-icon">
          <i class="fas fa-table"></i>
        </div>
        <h3>No Data Available</h3>
        <p>There's no data to display in the selected table.</p>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div class="pagination-container" id="pagination-container">
    <div class="pagination-info">
      <span id="pagination-info-text">Showing 0 of 0 entries</span>
    </div>
    <div class="pagination-controls">
      <button id="prev-page" class="btn secondary" disabled>
        <i class="fas fa-chevron-left"></i> Previous
      </button>
      <div class="page-numbers" id="page-numbers">
        <!-- Page numbers will be generated here -->
      </div>
      <button id="next-page" class="btn secondary" disabled>
        Next <i class="fas fa-chevron-right"></i>
      </button>
    </div>
    </div> <!-- Close main-content -->
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script src="updates.js"></script>
</body>
</html>
