<?php
/**
 * Migration script to add media_id column to posts table
 * Run this script to update existing databases with the new media_id field
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Database configuration
$host = 'localhost';
$dbname = 'facebook_db';
$username = 'root';
$password = '';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $results = [];
    
    // Check if media_id column already exists
    $checkColumn = $pdo->query("SHOW COLUMNS FROM posts LIKE 'media_id'");
    $columnExists = $checkColumn->rowCount() > 0;
    
    if ($columnExists) {
        $results[] = "✓ media_id column already exists in posts table";
    } else {
        // Add media_id column
        $pdo->exec("ALTER TABLE posts ADD COLUMN media_id VARCHAR(255) AFTER caption");
        $results[] = "✓ Added media_id column to posts table";
        
        // Add index for media_id
        $pdo->exec("CREATE INDEX idx_media_id ON posts(media_id)");
        $results[] = "✓ Added index for media_id column";
    }
    
    // Update the posts_with_details view
    $pdo->exec("DROP VIEW IF EXISTS posts_with_details");
    
    $createViewSQL = "
    CREATE VIEW posts_with_details AS
    SELECT 
        p.id as post_id,
        p.page_id,
        p.member_id,
        p.caption,
        p.media_id,
        p.likes_count,
        p.comments_count,
        p.views_count,
        p.shares_count,
        p.post_url,
        p.page_url,
        p.page_name,
        p.post_date,
        p.last_metrics_update,
        p.extraction_timestamp,
        pg.name as page_name_from_pages,
        pg.url as page_url_from_pages,
        m.name as member_name,
        m.email as member_email,
        m.role as member_role
    FROM posts p
    LEFT JOIN pages pg ON p.page_id = pg.id
    LEFT JOIN members m ON p.member_id = m.id";
    
    $pdo->exec($createViewSQL);
    $results[] = "✓ Updated posts_with_details view to include media_id";
    
    // Get table structure to verify
    $columns = $pdo->query("DESCRIBE posts")->fetchAll(PDO::FETCH_ASSOC);
    $mediaIdColumn = null;
    foreach ($columns as $column) {
        if ($column['Field'] === 'media_id') {
            $mediaIdColumn = $column;
            break;
        }
    }
    
    if ($mediaIdColumn) {
        $results[] = "✓ Verified: media_id column exists with type: " . $mediaIdColumn['Type'];
    }
    
    // Count posts to show current data
    $postCount = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();
    $results[] = "ℹ Current posts count: " . $postCount;
    
    // Count posts with media_id
    $mediaPostCount = $pdo->query("SELECT COUNT(*) FROM posts WHERE media_id IS NOT NULL AND media_id != ''")->fetchColumn();
    $results[] = "ℹ Posts with media_id: " . $mediaPostCount;
    
    echo json_encode([
        'success' => true,
        'message' => 'Migration completed successfully',
        'results' => $results,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'General error: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
