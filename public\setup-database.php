<?php
// Database setup script
header('Content-Type: text/html; charset=utf-8');

echo "<h1>Facebook Database Setup</h1>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup'])) {
    try {
        require_once 'connection.php';
        
        echo "<h2>Setting up database tables...</h2>";
        
        // Read and execute the schema file
        $schema_file = 'database_schema.sql';
        if (file_exists($schema_file)) {
            $sql_content = file_get_contents($schema_file);
            
            // Split SQL statements (basic splitting by semicolon)
            $statements = array_filter(array_map('trim', explode(';', $sql_content)));
            
            $success_count = 0;
            $error_count = 0;
            
            foreach ($statements as $statement) {
                if (empty($statement) || strpos($statement, '--') === 0) {
                    continue; // Skip empty statements and comments
                }
                
                // Skip delimiter statements
                if (stripos($statement, 'DELIMITER') !== false) {
                    continue;
                }
                
                try {
                    if ($conn->query($statement)) {
                        $success_count++;
                        echo "<p style='color: green;'>✅ Executed: " . substr($statement, 0, 50) . "...</p>";
                    } else {
                        $error_count++;
                        echo "<p style='color: red;'>❌ Failed: " . substr($statement, 0, 50) . "... Error: " . $conn->error . "</p>";
                    }
                } catch (Exception $e) {
                    $error_count++;
                    echo "<p style='color: orange;'>⚠️ Warning: " . substr($statement, 0, 50) . "... " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<h3>Setup Summary:</h3>";
            echo "<p>Successful statements: $success_count</p>";
            echo "<p>Failed statements: $error_count</p>";
            
            if ($error_count === 0) {
                echo "<p style='color: green; font-weight: bold;'>✅ Database setup completed successfully!</p>";
                echo "<p><a href='/test-database.php'>Test Database Connection</a></p>";
                echo "<p><a href='/updates.html'>Go to Updates Page</a></p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Setup completed with some warnings. Check the errors above.</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Schema file 'database_schema.sql' not found!</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Setup failed: " . $e->getMessage() . "</p>";
    }
} else {
    // Show setup form
    ?>
    <p>This script will set up the required database tables for the Facebook Posts application.</p>
    
    <h2>Prerequisites:</h2>
    <ul>
        <li>MySQL server is running</li>
        <li>Database 'facebook_db' exists</li>
        <li>Connection settings in connection.php are correct</li>
    </ul>
    
    <h2>What this will do:</h2>
    <ul>
        <li>Create tables: members, pages, posts</li>
        <li>Create indexes for better performance</li>
        <li>Create views for easier data access</li>
        <li>Create stored procedures for data synchronization</li>
        <li>Insert sample data (optional)</li>
    </ul>
    
    <form method="POST">
        <button type="submit" name="setup" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
            🚀 Setup Database
        </button>
    </form>
    
    <h2>Manual Setup:</h2>
    <p>If you prefer to set up manually, you can:</p>
    <ol>
        <li>Open your MySQL client (phpMyAdmin, MySQL Workbench, etc.)</li>
        <li>Select the 'facebook_db' database</li>
        <li>Import or run the SQL from <code>database_schema.sql</code></li>
    </ol>
    
    <h2>Connection Test:</h2>
    <?php
    try {
        require_once 'connection.php';
        echo "<p style='color: green;'>✅ Database connection is working!</p>";
        echo "<p>Connected to: " . $conn->server_info . "</p>";
        
        // Check if database exists
        $db_check = $conn->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = 'facebook_db'");
        if ($db_check && $db_check->num_rows > 0) {
            echo "<p style='color: green;'>✅ Database 'facebook_db' exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Database 'facebook_db' does not exist</p>";
            echo "<p>Please create the database first:</p>";
            echo "<code>CREATE DATABASE facebook_db;</code>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
        echo "<p>Please check your connection settings in connection.php</p>";
    }
    ?>
    
    <h2>Links:</h2>
    <ul>
        <li><a href="/test-database.php">Test Database Connection</a></li>
        <li><a href="/updates.html">Updates Page</a></li>
        <li><a href="/index.html">Main Dashboard</a></li>
    </ul>
    <?php
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

ul, ol {
    margin: 10px 0;
    padding-left: 30px;
}

li {
    margin: 5px 0;
}

button:hover {
    background: #0056b3 !important;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>
