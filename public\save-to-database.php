<?php
// Save Updates Page Data to XAMPP Database
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

// Create connection directly to avoid die() issues
$conn = new mysqli("localhost", "root", "", "facebook_db");

$response = ['success' => false, 'message' => '', 'results' => [], 'debug' => []];

try {
    $response['debug'][] = "Attempting to connect to MySQL...";

    // Check connection
    if ($conn->connect_error) {
        $response['debug'][] = "Connection failed: " . $conn->connect_error;
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    $response['debug'][] = "Connected to MySQL successfully";

    // Check if database exists, if not create it
    $conn->query("CREATE DATABASE IF NOT EXISTS facebook_db");
    $conn->select_db("facebook_db");

    // Create tables if they don't exist
    $conn->query("
        CREATE TABLE IF NOT EXISTS members (
            id VARCHAR(255) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            role VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    $conn->query("
        CREATE TABLE IF NOT EXISTS pages (
            id VARCHAR(255) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            url TEXT,
            member_id VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    $conn->query("
        CREATE TABLE IF NOT EXISTS posts (
            id VARCHAR(255) PRIMARY KEY,
            page_id VARCHAR(255),
            member_id VARCHAR(255),
            caption TEXT,
            likes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            views_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            post_url TEXT,
            page_url TEXT,
            page_name VARCHAR(255),
            post_date DATETIME,
            last_metrics_update DATETIME,
            extraction_timestamp DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");

    // Read JSON files from absolute paths
    $projectPath = 'C:\\Users\\<USER>\\Desktop\\projects\\facebook_telegram_bot_heheheheh\\facebook_telegram_bot\\';
    $postsFile = $projectPath . 'final_filtered_posts.json';
    $pagesFile = $projectPath . 'pages.json';
    $membersFile = $projectPath . 'team_members.json';

    $response['debug'][] = "Looking for files at: " . $projectPath;
    $response['debug'][] = "Posts file exists: " . (file_exists($postsFile) ? 'YES' : 'NO');
    $response['debug'][] = "Pages file exists: " . (file_exists($pagesFile) ? 'YES' : 'NO');
    $response['debug'][] = "Members file exists: " . (file_exists($membersFile) ? 'YES' : 'NO');

    $results = [
        'posts' => 0,
        'pages' => 0,
        'members' => 0,
        'errors' => []
    ];

    // Start transaction
    $conn->begin_transaction();

    // 1. Insert Members
    if (file_exists($membersFile)) {
        $membersContent = file_get_contents($membersFile);
        $response['debug'][] = "Members file size: " . strlen($membersContent) . " bytes";
        $members = json_decode($membersContent, true);
        if ($members && is_array($members)) {
            $response['debug'][] = "Found " . count($members) . " members in JSON";
            $memberStmt = $conn->prepare("INSERT INTO members (id, name, email, role) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE name=VALUES(name), email=VALUES(email), role=VALUES(role)");

            if ($memberStmt === false) {
                $results['errors'][] = "Member prepare error: " . $conn->error;
            } else {
                foreach ($members as $member) {
                    $id = $member['id'] ?? 'member_' . uniqid();
                    $name = $member['name'] ?? 'Unknown';
                    $email = $member['email'] ?? '';
                    $role = $member['role'] ?? 'Member';

                    $memberStmt->bind_param("ssss", $id, $name, $email, $role);
                    if ($memberStmt->execute()) {
                        $results['members']++;
                    } else {
                        $results['errors'][] = "Member insert error: " . $memberStmt->error;
                    }
                }
                $memberStmt->close();
            }
        }
    }

    // 2. Insert Pages
    if (file_exists($pagesFile)) {
        $pagesContent = file_get_contents($pagesFile);
        $response['debug'][] = "Pages file size: " . strlen($pagesContent) . " bytes";
        $pages = json_decode($pagesContent, true);
        if ($pages && is_array($pages)) {
            $response['debug'][] = "Found " . count($pages) . " pages in JSON";
            $pageStmt = $conn->prepare("INSERT INTO pages (id, name, url, member_id) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE name=VALUES(name), url=VALUES(url)");

            if ($pageStmt === false) {
                $results['errors'][] = "Page prepare error: " . $conn->error;
            } else {
                foreach ($pages as $index => $page) {
                    $url = $page['link'] ?? $page['url'] ?? '';
                    $id = generatePageId($url);
                    $name = $page['name'] ?? 'Unknown Page';
                    $memberId = findMemberIdForPage($url, $membersFile);

                    $pageStmt->bind_param("ssss", $id, $name, $url, $memberId);
                    if ($pageStmt->execute()) {
                        $results['pages']++;
                    } else {
                        $results['errors'][] = "Page insert error: " . $pageStmt->error;
                    }
                }
                $pageStmt->close();
            }
        }
    }

    // 3. Insert Posts
    if (file_exists($postsFile)) {
        $postsContent = file_get_contents($postsFile);
        $response['debug'][] = "Posts file size: " . strlen($postsContent) . " bytes";
        $posts = json_decode($postsContent, true);
        if ($posts && is_array($posts)) {
            $response['debug'][] = "Found " . count($posts) . " posts in JSON";
            $postStmt = $conn->prepare("
                INSERT INTO posts (
                    id, page_id, member_id, caption, likes_count, comments_count,
                    views_count, shares_count, post_url, page_url, page_name,
                    post_date, last_metrics_update, extraction_timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    likes_count=VALUES(likes_count), comments_count=VALUES(comments_count),
                    views_count=VALUES(views_count), shares_count=VALUES(shares_count),
                    last_metrics_update=VALUES(last_metrics_update)
            ");

            if ($postStmt === false) {
                $results['errors'][] = "Post prepare error: " . $conn->error;
            } else {
            
            foreach ($posts as $post) {
                $id = $post['normalizedTextHash'] ?? $post['id'] ?? 'post_' . uniqid();
                $pageUrl = $post['pageUrl'] ?? '';

                // Generate page ID from URL
                $pageId = generatePageId($pageUrl);

                // Find member ID based on page URL
                $memberId = findMemberIdForPage($pageUrl, $membersFile);

                $caption = $post['finalFilteredText'] ?? $post['caption'] ?? '';
                $likes = $post['engagement']['likes'] ?? 0;
                $comments = $post['engagement']['comments'] ?? 0;
                $views = $post['engagement']['views'] ?? 0;
                $shares = $post['engagement']['shares'] ?? 0;
                $postUrl = $post['postUrl'] ?? '';
                $pageName = $post['pageName'] ?? '';
                $postDate = $post['enhancedPostTime']['fullDate'] ?? $post['timestamp'] ?? null;
                $lastUpdate = $post['lastMetricsUpdate'] ?? $post['timestamp'] ?? null;
                $extractionTime = $post['timestamp'] ?? null;

                // Convert timestamps to MySQL format
                if ($postDate) $postDate = date('Y-m-d H:i:s', strtotime($postDate));
                if ($lastUpdate) $lastUpdate = date('Y-m-d H:i:s', strtotime($lastUpdate));
                if ($extractionTime) $extractionTime = date('Y-m-d H:i:s', strtotime($extractionTime));
                
                    $postStmt->bind_param(
                        "ssssiiiissssss",
                        $id, $pageId, $memberId, $caption, $likes, $comments, $views, $shares,
                        $postUrl, $pageUrl, $pageName, $postDate, $lastUpdate, $extractionTime
                    );

                    if ($postStmt->execute()) {
                        $results['posts']++;
                    } else {
                        $results['errors'][] = "Post insert error: " . $postStmt->error;
                    }
                }
                $postStmt->close();
            }
        }
    }

    // Commit transaction
    $conn->commit();
    
    $response['success'] = true;
    $response['message'] = 'Data successfully saved to database!';
    $response['results'] = $results;
    
} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
    $response['results'] = $results;
}

echo json_encode($response, JSON_PRETTY_PRINT);

// Helper functions
function generatePageId($pageUrl) {
    if (empty($pageUrl)) return 'unknown_page';

    // Extract page name from URL
    if (preg_match('/facebook\.com\/([^\/\?]+)/', $pageUrl, $matches)) {
        return $matches[1];
    }

    // Fallback to hash of URL
    return 'page_' . substr(md5($pageUrl), 0, 8);
}

function findMemberIdForPage($pageUrl, $membersFile) {
    if (empty($pageUrl) || !file_exists($membersFile)) return null;

    $members = json_decode(file_get_contents($membersFile), true);
    if (!is_array($members)) return null;

    foreach ($members as $member) {
        if (isset($member['assignedPages']) && is_array($member['assignedPages'])) {
            foreach ($member['assignedPages'] as $assignedPage) {
                $assignedUrl = '';
                if (is_string($assignedPage)) {
                    $assignedUrl = $assignedPage;
                } elseif (isset($assignedPage['link'])) {
                    $assignedUrl = $assignedPage['link'];
                }

                if (!empty($assignedUrl) && $pageUrl === $assignedUrl) {
                    return $member['id'];
                }
            }
        }
    }

    return null;
}
?>
