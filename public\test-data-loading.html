<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 Data Loading Test for Export Analytics</h1>
    
    <div class="test-section">
        <h2>API Endpoint Tests</h2>
        <button onclick="testTeamAPI()">Test Team API</button>
        <button onclick="testPostsAPI()">Test Posts API</button>
        <button onclick="testBothAPIs()">Test Both APIs</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="api-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Data Analysis</h2>
        <button onclick="analyzeData()">Analyze Loaded Data</button>
        <button onclick="testMemberPostMatching()">Test Member-Post Matching</button>
        
        <div id="analysis-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Export Analytics Simulation</h2>
        <button onclick="simulateExportAnalytics()">Simulate Export Analytics</button>
        
        <div id="simulation-results"></div>
    </div>

    <script>
        let teamMembers = [];
        let allPosts = [];
        
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('api-results').innerHTML = '';
            document.getElementById('analysis-results').innerHTML = '';
            document.getElementById('simulation-results').innerHTML = '';
        }
        
        async function testTeamAPI() {
            try {
                addResult('api-results', '🔄 Testing /api/team endpoint...', 'info');
                
                const response = await fetch('/api/team');
                const status = response.status;
                
                if (response.ok) {
                    const data = await response.json();
                    teamMembers = data;
                    
                    addResult('api-results', `✅ Team API Success: ${data.length} members loaded`, 'success');
                    addResult('api-results', `<pre>${JSON.stringify(data.slice(0, 2), null, 2)}</pre>`, 'info');
                } else {
                    addResult('api-results', `❌ Team API Failed: Status ${status}`, 'error');
                }
            } catch (error) {
                addResult('api-results', `❌ Team API Error: ${error.message}`, 'error');
            }
        }
        
        async function testPostsAPI() {
            try {
                addResult('api-results', '🔄 Testing /api/posts endpoint...', 'info');
                
                const response = await fetch('/api/posts');
                const status = response.status;
                
                if (response.ok) {
                    const data = await response.json();
                    
                    // Handle both array and object responses
                    if (Array.isArray(data)) {
                        allPosts = data;
                        addResult('api-results', `✅ Posts API Success: ${data.length} posts loaded (array format)`, 'success');
                    } else if (data.posts) {
                        allPosts = data.posts;
                        addResult('api-results', `✅ Posts API Success: ${data.posts.length} posts loaded (object format)`, 'success');
                    } else {
                        addResult('api-results', `⚠️ Posts API: Unexpected format`, 'error');
                        addResult('api-results', `<pre>${JSON.stringify(data, null, 2).substring(0, 500)}...</pre>`, 'info');
                    }
                    
                    if (allPosts.length > 0) {
                        addResult('api-results', `<pre>Sample post: ${JSON.stringify(allPosts[0], null, 2).substring(0, 500)}...</pre>`, 'info');
                    }
                } else {
                    addResult('api-results', `❌ Posts API Failed: Status ${status}`, 'error');
                }
            } catch (error) {
                addResult('api-results', `❌ Posts API Error: ${error.message}`, 'error');
            }
        }
        
        async function testBothAPIs() {
            await testTeamAPI();
            await testPostsAPI();
        }
        
        function analyzeData() {
            addResult('analysis-results', '🔍 Analyzing loaded data...', 'info');
            
            // Team members analysis
            addResult('analysis-results', `📊 Team Members: ${teamMembers.length}`, 'info');
            if (teamMembers.length > 0) {
                const memberWithPages = teamMembers.filter(m => m.assignedPages && m.assignedPages.length > 0);
                addResult('analysis-results', `👥 Members with assigned pages: ${memberWithPages.length}`, 'info');
                
                teamMembers.forEach((member, index) => {
                    const pages = member.assignedPages || [];
                    addResult('analysis-results', `Member ${index + 1}: ${member.name} - ${pages.length} pages`, 'info');
                });
            }
            
            // Posts analysis
            addResult('analysis-results', `📝 Total Posts: ${allPosts.length}`, 'info');
            if (allPosts.length > 0) {
                const postsWithEngagement = allPosts.filter(p => p.engagement);
                const postsWithEnhancedTime = allPosts.filter(p => p.enhancedPostTime);
                const postsWithPageUrl = allPosts.filter(p => p.pageUrl);
                
                addResult('analysis-results', `💬 Posts with engagement: ${postsWithEngagement.length}`, 'info');
                addResult('analysis-results', `⏰ Posts with enhanced time: ${postsWithEnhancedTime.length}`, 'info');
                addResult('analysis-results', `🔗 Posts with page URL: ${postsWithPageUrl.length}`, 'info');
                
                // Show unique page URLs
                const uniquePageUrls = [...new Set(allPosts.map(p => p.pageUrl).filter(Boolean))];
                addResult('analysis-results', `🌐 Unique page URLs: ${uniquePageUrls.length}`, 'info');
                addResult('analysis-results', `<pre>${uniquePageUrls.slice(0, 5).join('\n')}</pre>`, 'info');
            }
        }
        
        function testMemberPostMatching() {
            addResult('analysis-results', '🔗 Testing member-post matching...', 'info');
            
            if (teamMembers.length === 0 || allPosts.length === 0) {
                addResult('analysis-results', '❌ Need both team members and posts to test matching', 'error');
                return;
            }
            
            teamMembers.forEach(member => {
                const memberPosts = getMemberPosts(member);
                addResult('analysis-results', `${member.name}: ${memberPosts.length} posts matched`, memberPosts.length > 0 ? 'success' : 'error');
                
                if (memberPosts.length > 0) {
                    const samplePost = memberPosts[0];
                    addResult('analysis-results', `Sample post: ${(samplePost.finalFilteredText || samplePost.text || '').substring(0, 100)}...`, 'info');
                }
            });
        }
        
        function getMemberPosts(member) {
            if (!member || !member.assignedPages || !Array.isArray(member.assignedPages)) {
                return [];
            }
            
            return allPosts.filter(post => {
                if (!post || !post.pageUrl) {
                    return false;
                }
                
                return member.assignedPages.some(assignedPage => {
                    if (typeof assignedPage === 'string') {
                        return post.pageUrl.includes(assignedPage) || 
                               post.pageName === assignedPage ||
                               (post.pageUrl === assignedPage);
                    } else if (assignedPage && assignedPage.link) {
                        return post.pageUrl === assignedPage.link ||
                               post.pageUrl.includes(assignedPage.link) ||
                               (assignedPage.name && post.pageName === assignedPage.name);
                    }
                    return false;
                });
            });
        }
        
        function simulateExportAnalytics() {
            addResult('simulation-results', '🎯 Simulating Export Analytics...', 'info');
            
            if (teamMembers.length === 0) {
                addResult('simulation-results', '❌ No team members found', 'error');
                return;
            }
            
            if (allPosts.length === 0) {
                addResult('simulation-results', '❌ No posts found', 'error');
                return;
            }
            
            // Calculate overall stats
            const totalMembers = teamMembers.length;
            const totalPosts = allPosts.length;
            const totalEngagement = allPosts.reduce((total, post) => {
                const engagement = post.engagement || {};
                return total + (engagement.likes || 0) + (engagement.comments || 0) + (engagement.shares || 0) + (engagement.views || 0);
            }, 0);
            
            addResult('simulation-results', `📊 Overall Stats:`, 'success');
            addResult('simulation-results', `👥 ${totalMembers} team members`, 'info');
            addResult('simulation-results', `📝 ${totalPosts} total posts`, 'info');
            addResult('simulation-results', `❤️ ${totalEngagement.toLocaleString()} total engagement`, 'info');
            
            // Member-specific stats
            addResult('simulation-results', `👤 Member-specific stats:`, 'success');
            teamMembers.forEach(member => {
                const memberPosts = getMemberPosts(member);
                const memberEngagement = memberPosts.reduce((total, post) => {
                    const engagement = post.engagement || {};
                    return total + (engagement.likes || 0) + (engagement.comments || 0) + (engagement.shares || 0) + (engagement.views || 0);
                }, 0);
                
                addResult('simulation-results', `${member.name}: ${memberPosts.length} posts, ${memberEngagement.toLocaleString()} engagement`, memberPosts.length > 0 ? 'success' : 'error');
            });
            
            if (teamMembers.some(member => getMemberPosts(member).length > 0)) {
                addResult('simulation-results', '✅ Export Analytics should work with this data!', 'success');
            } else {
                addResult('simulation-results', '❌ No posts matched to any team members. Check page assignments.', 'error');
            }
        }
    </script>
</body>
</html>
