<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .test-item.success {
            border-color: #28a745;
            background: #d4edda;
        }
        .test-item.error {
            border-color: #dc3545;
            background: #f8d7da;
        }
        .test-item.warning {
            border-color: #ffc107;
            background: #fff3cd;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
        .status-icon {
            font-size: 1.2rem;
            margin-right: 10px;
        }
        .success .status-icon {
            color: #28a745;
        }
        .error .status-icon {
            color: #dc3545;
        }
        .warning .status-icon {
            color: #ffc107;
        }
        .loading .status-icon {
            color: #007bff;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background: #f2f2f2;
        }
        .json-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1><i class="fas fa-database"></i> Database Connection Test Suite</h1>
            <div>
                <button class="test-button" onclick="runAllTests()">
                    <i class="fas fa-play"></i> Run All Tests
                </button>
                <button class="test-button" onclick="clearResults()">
                    <i class="fas fa-trash"></i> Clear Results
                </button>
            </div>
        </div>

        <!-- Basic Connection Tests -->
        <div class="test-section">
            <h2><i class="fas fa-plug"></i> Basic Connection Tests</h2>
            
            <div class="test-item" id="test-server-running">
                <div>
                    <i class="fas fa-server status-icon"></i>
                    <strong>Server Running Test</strong>
                    <p>Check if Node.js server is responding</p>
                </div>
                <button class="test-button" onclick="testServerRunning()">Test</button>
            </div>

            <div class="test-item" id="test-mysql-connection">
                <div>
                    <i class="fas fa-database status-icon"></i>
                    <strong>MySQL Connection Test</strong>
                    <p>Test database connection and basic query</p>
                </div>
                <button class="test-button" onclick="testMySQLConnection()">Test</button>
            </div>

            <div class="test-item" id="test-database-exists">
                <div>
                    <i class="fas fa-folder status-icon"></i>
                    <strong>Database Exists Test</strong>
                    <p>Check if 'facebook_db' database exists</p>
                </div>
                <button class="test-button" onclick="testDatabaseExists()">Test</button>
            </div>
        </div>

        <!-- Table Structure Tests -->
        <div class="test-section">
            <h2><i class="fas fa-table"></i> Table Structure Tests</h2>
            
            <div class="test-item" id="test-tables-exist">
                <div>
                    <i class="fas fa-list status-icon"></i>
                    <strong>Tables Exist Test</strong>
                    <p>Check if required tables (posts, pages, members) exist</p>
                </div>
                <button class="test-button" onclick="testTablesExist()">Test</button>
            </div>

            <div class="test-item" id="test-table-structure">
                <div>
                    <i class="fas fa-columns status-icon"></i>
                    <strong>Table Structure Test</strong>
                    <p>Verify table columns and data types</p>
                </div>
                <button class="test-button" onclick="testTableStructure()">Test</button>
            </div>
        </div>

        <!-- API Endpoint Tests -->
        <div class="test-section">
            <h2><i class="fas fa-code"></i> API Endpoint Tests</h2>
            
            <div class="test-item" id="test-api-posts">
                <div>
                    <i class="fas fa-file-alt status-icon"></i>
                    <strong>Posts API Test</strong>
                    <p>Test /api/database/posts endpoint</p>
                </div>
                <button class="test-button" onclick="testPostsAPI()">Test</button>
            </div>

            <div class="test-item" id="test-api-pages">
                <div>
                    <i class="fas fa-globe status-icon"></i>
                    <strong>Pages API Test</strong>
                    <p>Test /api/database/pages endpoint</p>
                </div>
                <button class="test-button" onclick="testPagesAPI()">Test</button>
            </div>

            <div class="test-item" id="test-api-members">
                <div>
                    <i class="fas fa-users status-icon"></i>
                    <strong>Members API Test</strong>
                    <p>Test /api/database/members endpoint</p>
                </div>
                <button class="test-button" onclick="testMembersAPI()">Test</button>
            </div>

            <div class="test-item" id="test-api-sync-status">
                <div>
                    <i class="fas fa-sync status-icon"></i>
                    <strong>Sync Status API Test</strong>
                    <p>Test /api/database/sync-status endpoint</p>
                </div>
                <button class="test-button" onclick="testSyncStatusAPI()">Test</button>
            </div>
        </div>

        <!-- Data Operations Tests -->
        <div class="test-section">
            <h2><i class="fas fa-exchange-alt"></i> Data Operations Tests</h2>
            
            <div class="test-item" id="test-create-tables">
                <div>
                    <i class="fas fa-plus status-icon"></i>
                    <strong>Quick Database Setup</strong>
                    <p>Create database and tables automatically with sample data</p>
                </div>
                <button class="test-button" onclick="testCreateTables()">Setup Database</button>
            </div>

            <div class="test-item" id="test-insert-sample">
                <div>
                    <i class="fas fa-plus-circle status-icon"></i>
                    <strong>Insert Sample Data Test</strong>
                    <p>Insert test data to verify write operations</p>
                </div>
                <button class="test-button" onclick="testInsertSample()">Test</button>
            </div>

            <div class="test-item" id="test-sync-operation">
                <div>
                    <i class="fas fa-sync-alt status-icon"></i>
                    <strong>Sync Operation Test</strong>
                    <p>Test the actual sync from JSON to database</p>
                </div>
                <button class="test-button" onclick="testSyncOperation()">Test</button>
            </div>
        </div>

        <!-- JSON File Tests -->
        <div class="test-section">
            <h2><i class="fas fa-file-code"></i> JSON File Tests</h2>
            
            <div class="test-item" id="test-json-files">
                <div>
                    <i class="fas fa-file status-icon"></i>
                    <strong>JSON Files Test</strong>
                    <p>Check if JSON data files exist and are readable</p>
                </div>
                <button class="test-button" onclick="testJSONFiles()">Test</button>
            </div>
        </div>
    </div>

    <script src="test-database-connection.js"></script>
</body>
</html>
