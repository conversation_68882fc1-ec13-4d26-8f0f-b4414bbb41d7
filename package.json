{"name": "facebook-posts-app", "version": "1.0.0", "description": "Desktop application to display Facebook posts", "main": "main.js", "scripts": {"start": "electron .", "dev": "nodemon --watch main.js --watch server.js --exec \"electron .\"", "server": "node server.js", "build": "electron-builder build --win", "build-mac": "electron-builder build --mac", "build-linux": "electron-builder build --linux"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.8.2", "dotenv": "^16.4.7", "electron-is-dev": "^2.0.0", "express": "^4.18.2", "fs": "^0.0.1-security", "https-proxy-agent": "^7.0.6", "mysql2": "^3.6.0", "node-cron": "^4.1.0", "puppeteer": "^24.4.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "socket.io": "^4.7.2", "socks-proxy-agent": "^8.0.5"}, "devDependencies": {"electron": "^29.0.1", "electron-builder": "^24.13.3", "nodemon": "^3.0.2"}, "build": {"appId": "com.facebook.posts.app", "productName": "Facebook Posts App", "directories": {"output": "dist"}, "win": {"target": "nsis"}, "mac": {"target": "dmg"}, "linux": {"target": ["AppImage", "deb"], "category": "Utility"}, "files": ["**/*", "!pages.json", "!final_filtered_posts.json", "!error_screenshots/**", "!node_modules/puppeteer/.local-chromium/**"], "extraResources": [{"from": "pages.json", "to": "pages.json"}, {"from": "final_filtered_posts.json", "to": "final_filtered_posts.json"}], "asar": true}}