<!DOCTYPE html>
<html>
<head>
    <title>Database Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #2d5a2d; }
        .error { background: #5a2d2d; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <h1>🧪 Database Connection Test</h1>
    
    <button onclick="testConnection()">Test Connection</button>
    <button onclick="extractData()">Extract Posts to Database</button>
    
    <div id="results"></div>

    <script>
        async function testConnection() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>Testing connection...</p>';
            
            try {
                const response = await fetch('http://localhost/simple-test.php');
                const data = await response.json();
                
                let html = '<h3>Test Results:</h3>';
                
                if (data.success) {
                    html += '<div class="test-result success">✅ Connection Test PASSED!</div>';
                } else {
                    html += '<div class="test-result error">❌ Connection Test FAILED!</div>';
                }
                
                html += '<h4>Debug Info:</h4>';
                data.debug.forEach(debug => {
                    html += '<div class="test-result">' + debug + '</div>';
                });
                
                results.innerHTML = html;
                
            } catch (error) {
                results.innerHTML = '<div class="test-result error">❌ Error: ' + error.message + '</div>';
            }
        }
        
        async function extractData() {
            const results = document.getElementById('results');
            results.innerHTML = '<p>Extracting data...</p>';
            
            try {
                const response = await fetch('http://localhost/extract-posts-to-database.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();
                
                let html = '<h3>Extraction Results:</h3>';
                
                if (data.success) {
                    html += '<div class="test-result success">✅ Extraction SUCCESSFUL!</div>';
                } else {
                    html += '<div class="test-result error">❌ Extraction FAILED!</div>';
                }
                
                html += '<p><strong>Message:</strong> ' + data.message + '</p>';
                
                if (data.stats) {
                    html += '<h4>Statistics:</h4>';
                    html += '<div class="test-result">Posts processed: ' + data.stats.posts_processed + '</div>';
                    html += '<div class="test-result">Posts inserted: ' + data.stats.posts_inserted + '</div>';
                    html += '<div class="test-result">Posts updated: ' + data.stats.posts_updated + '</div>';
                }
                
                if (data.debug) {
                    html += '<h4>Debug Info:</h4>';
                    data.debug.forEach(debug => {
                        html += '<div class="test-result">' + debug + '</div>';
                    });
                }
                
                results.innerHTML = html;
                
            } catch (error) {
                results.innerHTML = '<div class="test-result error">❌ Error: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
