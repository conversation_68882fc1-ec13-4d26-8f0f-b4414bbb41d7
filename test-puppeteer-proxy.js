// Test Puppeteer proxy authentication
const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
puppeteer.use(StealthPlugin());

const { getPuppeteerProxyOptions, setPuppeteerProxyAuth } = require('./proxy-helpers');

async function testPuppeteerProxy() {
  console.log('🧪 Testing Puppeteer proxy authentication...\n');
  
  try {
    // Get proxy configuration
    const { launchOptions, proxyAuth } = getPuppeteerProxyOptions();
    
    console.log('🔧 Launch options:', JSON.stringify(launchOptions, null, 2));
    console.log('🔐 Proxy auth:', proxyAuth ? `${proxyAuth.username}:****` : 'None');
    
    if (!proxyAuth) {
      console.log('❌ No proxy authentication configured');
      return;
    }
    
    // Launch browser with proxy
    console.log('🚀 Launching browser with proxy...');
    const browser = await puppeteer.launch({
      headless: false, // Use visible browser for debugging
      ...launchOptions
    });
    
    // Get the first page
    const pages = await browser.pages();
    const page = pages[0] || await browser.newPage();
    
    // Set up proxy authentication
    console.log('🔐 Setting up proxy authentication...');
    await setPuppeteerProxyAuth(page, proxyAuth);
    
    // Test with a simple IP check
    console.log('🌐 Testing IP check through proxy...');
    try {
      await page.goto('https://httpbin.org/ip', { waitUntil: 'networkidle2', timeout: 15000 });
      const content = await page.content();
      console.log('✅ IP check successful!');
      
      // Extract IP from response
      const ipMatch = content.match(/"origin":\s*"([^"]+)"/);
      if (ipMatch) {
        console.log('📍 Proxy IP:', ipMatch[1]);
      }
      
    } catch (ipError) {
      console.log('❌ IP check failed:', ipError.message);
    }
    
    // Test with Facebook
    console.log('\n🔍 Testing Facebook access...');
    try {
      await page.goto('https://www.facebook.com', { 
        waitUntil: 'networkidle2', 
        timeout: 20000 
      });
      
      const title = await page.title();
      console.log('✅ Facebook access successful!');
      console.log('📄 Page title:', title);
      
      // Check for any error messages
      const url = page.url();
      console.log('🔗 Final URL:', url);
      
    } catch (fbError) {
      console.log('❌ Facebook access failed:', fbError.message);
      
      // Try to get more details
      try {
        const url = page.url();
        const title = await page.title();
        console.log('🔗 Current URL:', url);
        console.log('📄 Current title:', title);
      } catch (e) {
        console.log('❌ Could not get page details');
      }
    }
    
    // Keep browser open for inspection
    console.log('\n⏸️ Browser will stay open for 10 seconds for inspection...');
    await new Promise(resolve => setTimeout(resolve, 10000));
    
    await browser.close();
    console.log('✅ Test completed');
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

testPuppeteerProxy().catch(console.error);
