<?php
// Test database connection and setup
header('Content-Type: text/html; charset=utf-8');

echo "<h1>Facebook Database Connection Test</h1>";

try {
    // Test connection
    require_once 'connection.php';
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Test if tables exist
    $tables = ['members', 'pages', 'posts'];
    echo "<h2>Table Status:</h2>";
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            // Get row count
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_result->fetch_assoc()['count'];
            echo "<p style='color: green;'>✅ Table '$table' exists with $count records</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' does not exist</p>";
        }
    }
    
    // Test API endpoint
    echo "<h2>API Test:</h2>";
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . '/database_api.php?action=sync-status';
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'ignore_errors' => true
        ]
    ]);
    
    $api_response = @file_get_contents($api_url, false, $context);
    
    if ($api_response !== false) {
        $api_data = json_decode($api_response, true);
        if ($api_data) {
            echo "<p style='color: green;'>✅ Database API is working</p>";
            echo "<pre>" . json_encode($api_data, JSON_PRETTY_PRINT) . "</pre>";
        } else {
            echo "<p style='color: orange;'>⚠️ API responded but returned invalid JSON</p>";
            echo "<pre>Response: " . htmlspecialchars($api_response) . "</pre>";
        }
    } else {
        echo "<p style='color: red;'>❌ Database API is not responding</p>";
        echo "<p>URL tested: $api_url</p>";
    }
    
    // Show database info
    echo "<h2>Database Information:</h2>";
    $db_info = $conn->query("SELECT DATABASE() as db_name, VERSION() as version");
    if ($db_info && $row = $db_info->fetch_assoc()) {
        echo "<p>Database: " . $row['db_name'] . "</p>";
        echo "<p>MySQL Version: " . $row['version'] . "</p>";
    }
    
    // Show sample data if available
    echo "<h2>Sample Data:</h2>";
    
    // Sample posts
    $posts_sample = $conn->query("SELECT id, caption, likes_count, page_name FROM posts LIMIT 3");
    if ($posts_sample && $posts_sample->num_rows > 0) {
        echo "<h3>Sample Posts:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Caption</th><th>Likes</th><th>Page</th></tr>";
        while ($row = $posts_sample->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars(substr($row['id'], 0, 20)) . "...</td>";
            echo "<td>" . htmlspecialchars(substr($row['caption'] ?? '', 0, 50)) . "...</td>";
            echo "<td>" . $row['likes_count'] . "</td>";
            echo "<td>" . htmlspecialchars($row['page_name'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No posts data found</p>";
    }
    
    // Sample pages
    $pages_sample = $conn->query("SELECT id, name, posts_count FROM pages LIMIT 3");
    if ($pages_sample && $pages_sample->num_rows > 0) {
        echo "<h3>Sample Pages:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Posts Count</th></tr>";
        while ($row = $pages_sample->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . $row['posts_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No pages data found</p>";
    }
    
    // Sample members
    $members_sample = $conn->query("SELECT id, name, role, total_posts_count FROM members LIMIT 3");
    if ($members_sample && $members_sample->num_rows > 0) {
        echo "<h3>Sample Members:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Role</th><th>Posts Count</th></tr>";
        while ($row = $members_sample->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['role']) . "</td>";
            echo "<td>" . $row['total_posts_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No members data found</p>";
    }
    
    echo "<h2>Setup Instructions:</h2>";
    echo "<ol>";
    echo "<li>If tables don't exist, run the SQL from <code>database_schema.sql</code></li>";
    echo "<li>Use the 'Database Sync' feature in the Updates page to import data</li>";
    echo "<li>Check that your MySQL server is running</li>";
    echo "<li>Verify the connection settings in <code>connection.php</code></li>";
    echo "</ol>";
    
    echo "<p><a href='/updates.html'>Go to Updates Page</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed!</p>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    
    echo "<h2>Troubleshooting:</h2>";
    echo "<ul>";
    echo "<li>Check if MySQL server is running</li>";
    echo "<li>Verify database name 'facebook_db' exists</li>";
    echo "<li>Check username/password in connection.php</li>";
    echo "<li>Make sure PHP MySQL extension is installed</li>";
    echo "</ul>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

table {
    width: 100%;
    max-width: 600px;
}

th, td {
    padding: 8px 12px;
    text-align: left;
}

th {
    background-color: #f5f5f5;
}

pre {
    background-color: #f5f5f5;
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto;
}

code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
}
</style>
