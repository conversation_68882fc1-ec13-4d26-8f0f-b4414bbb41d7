<?php
// Simple Database Test
header('Content-Type: text/html');

echo "<h2>Database Connection Test</h2>";

try {
    $conn = new mysqli("localhost", "root", "", "facebook_db");
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ Database connection successful!</p>";
    
    // Test tables
    $tables = ['members', 'pages', 'posts'];
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            // Count records
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_result->fetch_assoc()['count'];
            echo "<p style='color: blue;'>✅ Table '$table' exists with $count records</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' missing</p>";
        }
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>Electron App Test</h2>";

// Test Electron app
$ports = [3000, 3001, 8080, 5000];
$found = false;

foreach ($ports as $port) {
    echo "<p>Testing port $port...</p>";
    
    $url = "http://localhost:$port/api/posts";
    $context = stream_context_create([
        'http' => [
            'timeout' => 3,
            'method' => 'GET'
        ]
    ]);
    
    $data = @file_get_contents($url, false, $context);
    if ($data !== false) {
        $posts = json_decode($data, true);
        if (is_array($posts)) {
            echo "<p style='color: green;'>✅ Found Electron app on port $port with " . count($posts) . " posts!</p>";
            $found = true;
            break;
        }
    }
}

if (!$found) {
    echo "<p style='color: red;'>❌ Could not find Electron app on any port</p>";
    echo "<p>Make sure your Electron app is running!</p>";
}
?>
