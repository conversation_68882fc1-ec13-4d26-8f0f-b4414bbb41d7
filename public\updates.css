/* Database Page Specific Styles */

/* Left Sidebar Navigation - Enhanced for dark mode */
.left-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: 250px;
  height: 100vh;
  background: var(--white);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.dark-mode .left-sidebar {
  background: var(--dark-card);
  border-right-color: rgba(255, 255, 255, 0.1);
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.3);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  font-size: 18px;
  color: var(--primary-color);
}

.dark-mode .sidebar-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text);
}

.sidebar-header i {
  font-size: 24px;
}

.sidebar-menu {
  list-style: none;
  padding: 20px 0;
  margin: 0;
  flex: 1;
}

.sidebar-menu li {
  margin: 0;
}

.sidebar-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  color: var(--text-color);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.dark-mode .sidebar-link {
  color: var(--dark-text);
}

.sidebar-link:hover {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
}

.sidebar-link.active {
  background: rgba(59, 130, 246, 0.15);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
  font-weight: 600;
}

.sidebar-link i {
  font-size: 18px;
  width: 20px;
  text-align: center;
}

/* Ensure main-content never goes behind sidebar */
.main-content {
  margin-left: 0 !important;
  min-height: 100vh;
  padding: 0;
  overflow: visible;
  position: relative;
}

/* Ensure body can scroll normally */
body {
  overflow-y: auto !important;
  overflow-x: hidden !important;
}

/* FULL WIDTH - ensure content never goes behind sidebar */
.updates-container {
  width: calc(100vw - 281px) !important; /* Full width minus sidebar + margins */
  margin-left: 251px !important; /* Start with tiny 1px gap from sidebar */
  margin-top: 20px !important; /* Add top margin for proper spacing */
  margin-right: 30px !important; /* Increased right margin to prevent cutoff */
  margin-bottom: 0 !important;
  padding: 20px 20px 100px 20px !important; /* Proper padding for content */
  background: var(--secondary-color);
  box-sizing: border-box;
  min-height: calc(100vh - 40px); /* Account for top margin */
}

.dark-mode .updates-container {
  background: var(--dark-bg);
}

/* Header box - optimized spacing */
.updates-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 20px 0;
  padding: 20px 20px;
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.dark-mode .updates-header {
  background: var(--dark-card);
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* Table container - no side margins, full width */
.table-container {
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin: 0 0 20px 0; /* No side margins */
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.dark-mode .table-container {
  background: var(--dark-card);
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* Simple table controls */
.table-controls {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: var(--white);
  gap: 20px;
}

.dark-mode .table-controls {
  background: var(--dark-card);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

/* Header removed - no longer needed */

.dark-mode .main-content header {
  background: var(--dark-card);
  border-bottom-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* Navigation tabs - no side margins, full width */
.table-nav {
  display: flex;
  gap: 0;
  margin: 0 0 20px 0; /* No side margins */
  background: var(--white);
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;
}

.dark-mode .table-nav {
  background: var(--dark-card);
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.table-nav-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: var(--text-color);
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.dark-mode .table-nav-btn {
  color: var(--dark-text);
}

.table-nav-btn.active {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(66, 103, 178, 0.3);
}

.table-nav-btn:hover:not(.active) {
  background: rgba(66, 103, 178, 0.1);
  color: var(--primary-color);
}

/* Simple search input */
#table-search {
  width: 300px;
  padding: 10px 15px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  font-size: 14px;
  background: var(--white);
  color: var(--text-color);
}

#table-search:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Simple filter dropdown */
#table-filter {
  width: 150px;
  padding: 10px 15px;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 8px;
  background: var(--white);
  color: var(--text-color);
  cursor: pointer;
}

/* Simple export button */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: var(--primary-color);
  color: white;
  cursor: pointer;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.btn:hover {
  background: #365899;
}

/* Media links styling */
.cell-media-urls {
  max-width: 200px;
}

.media-link {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 12px;
  display: inline-block;
  margin: 2px 0;
  padding: 2px 6px;
  background: rgba(66, 103, 178, 0.1);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.media-link:hover {
  background: var(--primary-color);
  color: white;
  text-decoration: none;
}

.dark-mode .media-link {
  color: #74a9ff;
  background: rgba(116, 169, 255, 0.1);
}

.dark-mode .media-link:hover {
  background: #74a9ff;
  color: var(--dark-card);
}

/* Dark mode */
.dark-mode #table-search,
.dark-mode #table-filter {
  background: var(--dark-secondary);
  border-color: rgba(255, 255, 255, 0.2);
  color: var(--dark-text);
}

/* Simple button override */
.btn {
  padding: 8px 16px !important;
  border-radius: 6px !important;
  border: none !important;
  background: var(--primary-color) !important;
  color: white !important;
  cursor: pointer !important;
  font-size: 14px !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 6px !important;
}

.btn.primary {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 2px 8px rgba(66, 103, 178, 0.3);
}

.btn:hover {
  background: #365899 !important;
}

.btn.success {
  background: var(--success);
  color: white;
  box-shadow: 0 2px 8px rgba(46, 204, 113, 0.3);
}

.btn.success:hover {
  background: #27ae60;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(46, 204, 113, 0.4);
}

/* Table wrapper - clean and simple */
.table-wrapper {
  overflow-x: auto;
  overflow-y: visible;
  width: 100%;
  background: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark-mode .table-wrapper {
  background: var(--dark-card);
}

/* Enhanced table styling with better dark mode support */
.data-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--white);
  min-width: 1400px; /* Increased for new media_id column */
  table-layout: fixed;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark-mode .data-table {
  background: var(--dark-card);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.data-table th {
  background: linear-gradient(135deg, var(--secondary-color), rgba(59, 130, 246, 0.1));
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  color: var(--text-color);
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 10;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.dark-mode .data-table th {
  background: linear-gradient(135deg, var(--dark-secondary), rgba(74, 158, 255, 0.1));
  color: var(--dark-text);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.data-table th:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.2));
  transform: translateY(-1px);
}

.dark-mode .data-table th:hover {
  background: linear-gradient(135deg, rgba(74, 158, 255, 0.1), rgba(74, 158, 255, 0.2));
}

.data-table td {
  padding: 10px 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  color: var(--text-color);
  vertical-align: middle;
  font-size: 12px;
  line-height: 1.4;
  transition: all 0.3s ease;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dark-mode .data-table td {
  border-bottom-color: rgba(255, 255, 255, 0.08);
  color: var(--dark-text);
}

.data-table tr:hover td {
  background: rgba(59, 130, 246, 0.05);
  transform: scale(1.005);
}

.dark-mode .data-table tr:hover td {
  background: rgba(74, 158, 255, 0.1);
}

/* Compact column layout to fit screen without scrolling */
.data-table th:nth-child(1), .data-table td:nth-child(1) {
  width: 150px;
  min-width: 150px;
  white-space: nowrap;
  overflow: visible !important;
  text-overflow: none !important;
  text-align: right;
  font-size: 12px;
  padding: 6px 8px;
} /* ID - Arabic page names */

.data-table th:nth-child(2), .data-table td:nth-child(2) {
  width: 200px;
  min-width: 200px;
  white-space: nowrap;
  overflow: visible !important;
  text-overflow: none !important;
  text-align: left;
  font-size: 10px;
  padding: 6px 4px;
} /* Page ID */

.data-table th:nth-child(3), .data-table td:nth-child(3) {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
  white-space: nowrap;
  overflow: visible !important;
  text-overflow: none !important;
  text-align: left;
  font-size: 9px;
  padding: 6px 4px; /* Reduce horizontal padding to fit more text */
} /* Member ID */

.data-table th:nth-child(4), .data-table td:nth-child(4) {
  width: 100px;
  min-width: 100px;
  max-width: 100px;
  white-space: normal;
  word-wrap: break-word;
  overflow: hidden;
  line-height: 1.2;
  font-size: 8px;
} /* Caption */

.data-table th:nth-child(5), .data-table td:nth-child(5) {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-align: center;
  font-size: 10px;
  padding: 6px 4px;
} /* Media ID */

.data-table th:nth-child(6), .data-table td:nth-child(6) {
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  text-align: center;
  white-space: nowrap;
  font-size: 9px;
  padding: 4px 2px;
} /* Likes */

.data-table th:nth-child(7), .data-table td:nth-child(7) {
  width: 55px;
  min-width: 55px;
  max-width: 55px;
  text-align: center;
  white-space: nowrap;
  font-size: 9px;
  padding: 4px 2px;
} /* Comments */

.data-table th:nth-child(8), .data-table td:nth-child(8) {
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  text-align: center;
  white-space: nowrap;
  font-size: 9px;
  padding: 4px 2px;
} /* Views */

.data-table th:nth-child(9), .data-table td:nth-child(9) {
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  text-align: center;
  white-space: nowrap;
  font-size: 9px;
  padding: 4px 2px;
} /* Shares */

.data-table th:nth-child(10), .data-table td:nth-child(10) {
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  text-align: center;
  white-space: nowrap;
  font-size: 9px;
  padding: 4px 2px;
} /* Post URL */

.data-table th:nth-child(11), .data-table td:nth-child(11) {
  width: 90px;
  min-width: 90px;
  max-width: 90px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 8px;
} /* Post Date */

.data-table th:nth-child(12), .data-table td:nth-child(12) {
  width: 90px;
  min-width: 90px;
  max-width: 90px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 8px;
} /* Last Update */

.data-table th:nth-child(13), .data-table td:nth-child(13) {
  width: 90px;
  min-width: 90px;
  max-width: 90px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 8px;
} /* Extraction Timestamp */

.data-table th:nth-child(14), .data-table td:nth-child(14) {
  width: 90px;
  min-width: 90px;
  max-width: 90px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 8px;
} /* Created At */

.data-table th:nth-child(13), .data-table td:nth-child(13) {
  width: 90px;
  min-width: 90px;
  max-width: 90px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 8px;
} /* Created At */
/* Enhanced data styling for better readability */
.data-table td:nth-child(1) { /* ID column */
  font-weight: 600;
  color: var(--primary-color);
  text-align: center;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 4px;
}

.dark-mode .data-table td:nth-child(1) {
  background: rgba(74, 158, 255, 0.1);
  color: #4A9EFF;
}

.data-table td:nth-child(2),
.data-table td:nth-child(3) { /* Page ID, Member ID */
  font-family: 'Courier New', monospace;
  font-size: 10px;
  text-align: center;
  /* No background highlight */
}

.data-table td:nth-child(4) { /* Caption */
  line-height: 1.6;
  word-wrap: break-word;
  white-space: normal;
}

.data-table td:nth-child(5) { /* Media ID */
  font-family: 'Courier New', monospace;
  font-size: 10px;
  text-align: center;
  color: var(--primary-color);
  font-weight: 500;
}

.cell-media-id {
  background: rgba(59, 130, 246, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 9px;
  font-weight: 600;
  color: var(--primary-color);
}

.dark-mode .cell-media-id {
  background: rgba(74, 158, 255, 0.2);
  color: #4A9EFF;
}

.data-table td:nth-child(6),
.data-table td:nth-child(7),
.data-table td:nth-child(8),
.data-table td:nth-child(9) { /* Metrics columns */
  font-weight: 600;
  font-size: 11px; /* Smaller to prevent overlap */
  text-align: center;
}

.data-table td:nth-child(10) { /* Post URL */
  text-align: center;
  padding: 4px;
}

/* URL Button Style */
.url-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 9px;
  cursor: pointer;
  transition: background 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.url-btn:hover {
  background: #365899;
  transform: translateY(-1px);
}

.dark-mode .url-btn {
  background: #4A9EFF;
  color: white;
}

.dark-mode .url-btn:hover {
  background: #6BB6FF;
}

.dark-mode .data-table td {
  border-bottom-color: rgba(255, 255, 255, 0.08);
  color: var(--dark-text);
}

/* Dark mode for clear data styling */
.dark-mode .data-table td:nth-child(1) {
  color: #4A9EFF; /* Brighter blue for dark mode */
}

.dark-mode .data-table td:nth-child(5) { /* Media ID */
  color: #4A9EFF; /* Brighter blue for media IDs in dark mode */
}

.dark-mode .data-table td:nth-child(10) {
  color: #4A9EFF; /* Brighter blue for URLs in dark mode */
}

.data-table tr:hover {
  background: rgba(66, 103, 178, 0.05);
}

.dark-mode .data-table tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Pagination improvements */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 15px;
  background: var(--white);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-mode .pagination {
  background: var(--dark-card);
  border-top-color: rgba(255, 255, 255, 0.1);
}

.pagination button {
  padding: 8px 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: var(--white);
  color: var(--text-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dark-mode .pagination button {
  background: var(--dark-secondary);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--dark-text);
}

.pagination button:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination .page-number.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Custom scrollbar for table wrapper */
.table-wrapper::-webkit-scrollbar {
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #365899;
}

.dark-mode .table-wrapper::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

/* Ensure table headers are properly aligned */
.data-table th {
  position: sticky;
  top: 0;
  z-index: 10;
  background: var(--secondary-color);
}

.dark-mode .data-table th {
  background: var(--dark-secondary);
}

/* Removed conflicting updates-container rule - using full-width layout above */

.updates-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--light-border);
}

.updates-header h2 {
  color: var(--text-color);
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.refresh-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.refresh-controls .theme-toggle {
  width: 40px;
  height: 40px;
  padding: 8px;
}

/* Header Auto-Sync Indicator */
.auto-sync-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-color);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.auto-sync-indicator.active {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.auto-sync-indicator.active i {
  animation: pulse 2s infinite;
}

.auto-sync-indicator i {
  font-size: 12px;
}

#header-countdown {
  font-weight: 600;
  color: #FF9800;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Database Sync Panel - Always Visible */
.sync-panel {
  background: var(--white);
  border: 2px solid var(--primary-color);
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(24, 119, 242, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.sync-panel.minimized .sync-content {
  display: none;
}

.sync-panel.minimized {
  margin-bottom: 10px;
}

.sync-header {
  background: var(--primary-color);
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sync-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.sync-minimize {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 5px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.sync-minimize:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.sync-minimize i {
  transition: transform 0.3s ease;
}

.sync-panel.minimized .sync-minimize i {
  transform: rotate(180deg);
}

.sync-content {
  padding: 20px;
}

.sync-status {
  margin-bottom: 20px;
}

.status-loading {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--light-text);
  font-size: 0.9rem;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.status-item {
  background: var(--light-bg);
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid var(--primary-color);
}

.status-item h4 {
  margin: 0 0 10px 0;
  color: var(--text-color);
  font-size: 1rem;
}

.status-counts {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-label {
  font-size: 0.85rem;
  color: var(--light-text);
}

.status-number {
  font-weight: 600;
  color: var(--text-color);
}

.status-sync-needed {
  font-size: 0.8rem;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 600;
}

.status-sync-needed.needed {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-sync-needed.synced {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.sync-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.sync-option {
  text-align: center;
  padding: 20px;
  background: var(--light-bg);
  border-radius: 8px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.sync-option:hover {
  border-color: var(--primary-color);
  background: rgba(24, 119, 242, 0.02);
}

.sync-option button {
  margin-bottom: 10px;
  width: 100%;
  max-width: 250px;
}

.sync-option p {
  margin: 0;
  color: var(--light-text);
  font-size: 0.9rem;
  line-height: 1.4;
}

.sync-results {
  background: var(--light-bg);
  padding: 15px;
  border-radius: 8px;
  margin-top: 20px;
}

.sync-results h4 {
  margin: 0 0 15px 0;
  color: var(--text-color);
}

.sync-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--light-border);
}

.sync-result-item:last-child {
  border-bottom: none;
}

.sync-result-label {
  font-weight: 500;
  color: var(--text-color);
}

.sync-result-count {
  background: var(--primary-color);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.sync-result-count.success {
  background: #28a745;
}

.sync-result-count.error {
  background: #dc3545;
}

/* Dark mode styles for sync panel */
.dark-mode .sync-panel {
  background: var(--dark-card);
  border-color: var(--primary-color);
}

.dark-mode .status-item {
  background: var(--dark-bg-secondary);
}

.dark-mode .sync-option {
  background: var(--dark-bg-secondary);
}

.dark-mode .sync-option:hover {
  background: rgba(24, 119, 242, 0.1);
}

.dark-mode .sync-results {
  background: var(--dark-bg-secondary);
}

.dark-mode .sync-result-item {
  border-bottom-color: var(--dark-border);
}

.dark-mode .status-item h3 {
  color: var(--dark-text);
}

.dark-mode .status-item p {
  color: var(--dark-light-text);
}

.dark-mode .status-label {
  color: var(--dark-light-text);
}

.dark-mode .status-value {
  color: var(--dark-text);
}

.dark-mode .sync-results p {
  color: var(--dark-text);
}

.dark-mode .status-grid {
  gap: 20px;
}

.dark-mode .status-item {
  background: var(--dark-bg-secondary);
  border: 1px solid var(--dark-border);
}

.dark-mode .status-item .status-label {
  color: var(--dark-light-text);
}

.dark-mode .status-item .status-value {
  color: var(--dark-text);
}

.dark-mode .status-badge {
  background: var(--dark-bg-secondary);
  color: var(--dark-text);
  border: 1px solid var(--dark-border);
}

.dark-mode .status-counts .status-label {
  color: var(--dark-light-text) !important;
}

.dark-mode .status-counts .status-number {
  color: var(--dark-text) !important;
}

.dark-mode .status-item h4 {
  color: var(--dark-text) !important;
}

.dark-mode .status-sync-needed.needed {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border-color: rgba(255, 193, 7, 0.3);
}

.dark-mode .status-sync-needed.synced {
  background: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border-color: rgba(40, 167, 69, 0.3);
}

.dark-mode .sync-option p {
  color: var(--dark-light-text) !important;
}

.dark-mode .sync-success h4 {
  color: var(--dark-text) !important;
}

.dark-mode .sync-result-label {
  color: var(--dark-text) !important;
}

.dark-mode .sync-error h4 {
  color: var(--dark-text) !important;
}

.dark-mode .sync-error p {
  color: var(--dark-light-text) !important;
}

.last-updated {
  color: var(--light-text);
  font-size: 0.9rem;
}

/* Duplicate table navigation styles removed - using the ones above */

/* Duplicate table controls removed - using the one above */

.search-container {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: 2px solid var(--light-border);
  border-radius: 8px;
  font-size: 0.9rem;
  background: var(--light-bg);
  color: var(--text-color);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(24, 119, 242, 0.1);
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--light-text);
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-select {
  padding: 10px 16px;
  border: 2px solid var(--light-border);
  border-radius: 8px;
  background: var(--light-bg);
  color: var(--text-color);
  font-size: 0.9rem;
  cursor: pointer;
}

.export-container {
  display: flex;
  gap: 10px;
}

/* Table Sections */
.table-section {
  display: none;
}

.table-section.active {
  display: block;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-header h3 {
  color: var(--text-color);
  margin: 0;
  font-size: 1.4rem;
  font-weight: 600;
}

.table-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.table-description {
  color: var(--light-text);
  font-size: 0.9rem;
}

/* Data Tables */
.table-container {
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid var(--light-border);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.data-table th {
  background: var(--light-bg);
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: var(--text-color);
  border-bottom: 2px solid var(--light-border);
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.data-table th:hover {
  background: rgba(24, 119, 242, 0.05);
}

.data-table th i {
  margin-left: 5px;
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.data-table th:hover i {
  opacity: 1;
}

.data-table th.sorted-asc i::before {
  content: "\f0de"; /* fa-sort-up */
  color: var(--primary-color);
}

.data-table th.sorted-desc i::before {
  content: "\f0dd"; /* fa-sort-down */
  color: var(--primary-color);
}

.data-table td {
  padding: 14px 12px;
  border-bottom: 1px solid var(--light-border);
  color: var(--text-color);
  vertical-align: top;
}

.data-table tbody tr:hover {
  background: rgba(24, 119, 242, 0.02);
}

/* Removed alternating row background */

/* Cell Content Styles */
.cell-id {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  color: var(--light-text);
  /* Removed gray background */
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.cell-caption {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}

.cell-caption:hover {
  white-space: normal;
  overflow: visible;
  max-width: none;
}

.cell-number {
  font-weight: 600;
  text-align: right;
}

.cell-date {
  font-size: 0.85rem;
  color: var(--light-text);
}

.cell-url {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cell-url a {
  color: #007bff;
  text-decoration: none;
}

.cell-url a:hover {
  text-decoration: underline;
}

.cell-url {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cell-url a {
  color: var(--primary-color);
  text-decoration: none;
}

.cell-url a:hover {
  text-decoration: underline;
}

.cell-badge {
  background: var(--primary-color);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  display: inline-block;
}

.cell-badge.success {
  background: #28a745;
}

.cell-badge.warning {
  background: #ffc107;
  color: #000;
}

.cell-badge.danger {
  background: #dc3545;
}

/* Loading and Empty States */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--light-text);
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--light-text);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  margin: 0 0 10px 0;
  color: var(--text-color);
}

.hidden {
  display: none !important;
}

/* Enhanced Pagination - centered with margins */
.pagination-container {
  position: fixed;
  left: 300px;
  right: 50px;
  bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: var(--white);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  flex-wrap: nowrap;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.pagination-info {
  color: var(--text-color);
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
}

.dark-mode .pagination-info {
  color: var(--dark-text);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

.page-number {
  padding: 8px 12px;
  border: 1px solid var(--light-border);
  background: var(--light-bg);
  color: var(--text-color);
  text-decoration: none;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.page-number:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.page-number.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Enhanced Dark Mode Support */
.dark-mode .updates-header {
  border-bottom-color: var(--dark-border);
}

.dark-mode .updates-header h2 {
  color: var(--dark-text);
}

/* Duplicate dark mode table nav styles removed - using the ones above */

/* Dark mode text improvements */
.dark-mode .table-header h3 {
  color: var(--dark-text);
}

.dark-mode .table-description {
  color: var(--dark-light-text);
}

.dark-mode .last-updated {
  color: var(--dark-light-text);
}

.dark-mode .page-ellipsis {
  color: var(--dark-light-text) !important;
}

.dark-mode .search-input,
.dark-mode .filter-select {
  background: var(--dark-bg-secondary);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

.dark-mode .table-container {
  background: var(--dark-card);
  border-color: var(--dark-border);
}

.dark-mode .data-table th {
  background: var(--dark-bg-secondary);
  color: var(--dark-text);
  border-bottom-color: var(--dark-border);
}

.dark-mode .data-table td {
  color: var(--dark-text);
  border-bottom-color: var(--dark-border);
}

.dark-mode .data-table tbody tr:hover {
  background: rgba(24, 119, 242, 0.05);
}

.dark-mode .cell-id {
  /* Removed gray background in dark mode too */
  color: var(--dark-light-text);
}

.dark-mode .pagination-container {
  background: var(--dark-card);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Enhanced pagination buttons */
.page-number, #prev-page, #next-page {
  padding: 10px 16px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  background: var(--white);
  color: var(--primary-color);
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  min-width: 44px;
  justify-content: center;
}

.page-number:hover, #prev-page:hover, #next-page:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.page-number.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.page-number:disabled, #prev-page:disabled, #next-page:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.dark-mode .page-number,
.dark-mode #prev-page,
.dark-mode #next-page {
  background: var(--dark-secondary);
  border-color: rgba(74, 158, 255, 0.3);
  color: var(--dark-text);
}

.dark-mode .page-number:hover,
.dark-mode #prev-page:hover,
.dark-mode #next-page:hover {
  background: #4A9EFF;
  color: white;
}

.dark-mode .page-number.active {
  background: #4A9EFF;
  color: white;
  border-color: #4A9EFF;
}

.dark-mode .page-number {
  background: var(--dark-bg-secondary);
  border-color: var(--dark-border);
  color: var(--dark-text);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .updates-container {
    width: calc(100vw - 275px) !important;
    margin-left: 250px !important;
    margin-right: 15px !important;
    padding: 15px 10px 100px 10px !important;
  }

  .main-content header {
    margin-left: 250px;
    margin-right: 15px;
    width: calc(100vw - 275px);
    padding: 15px 10px;
  }
}

@media (max-width: 768px) {
  .updates-container {
    width: calc(100vw - 265px) !important;
    margin-left: 250px !important;
    margin-right: 10px !important;
    padding: 10px 8px 100px 8px !important;
  }

  .main-content header {
    margin-left: 250px;
    margin-right: 10px;
    width: calc(100vw - 265px);
    padding: 15px 8px;
  }
}

  .updates-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .table-controls {
    flex-wrap: wrap;
    gap: 10px;
  }

  #table-search {
    width: 200px;
  }

  #table-filter {
    width: 100px;
  }

  .search-container {
    max-width: none;
  }

  .table-container {
    overflow-x: auto;
  }

  .data-table {
    min-width: 800px;
  }

  .pagination-container {
    left: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 12px;
    gap: 10px;
  }

  .table-nav {
    flex-wrap: wrap;
  }

  .table-nav-btn {
    flex: 1;
    min-width: 120px;
  }

  .header-controls {
    flex-wrap: wrap;
    gap: 8px;
  }

  .header-controls .btn {
    font-size: 0.85rem;
    padding: 8px 12px;
  }
}

@media (max-width: 480px) {
  .updates-container {
    width: calc(100vw - 265px) !important;
    margin-left: 255px !important;
    margin-right: 8px !important;
    padding: 8px 5px 100px 5px !important;
  }

  .main-content header {
    margin-left: 255px;
    margin-right: 8px;
    width: calc(100vw - 265px);
    padding: 12px 5px;
  }

  .updates-header h2 {
    font-size: 1.4rem;
  }

  .table-nav-btn {
    padding: 10px 12px;
    font-size: 0.85rem;
  }

  .data-table th,
  .data-table td {
    padding: 10px 8px;
    font-size: 0.8rem;
  }

  .cell-caption {
    max-width: 150px;
  }

  .cell-url {
    max-width: 120px;
  }
}

/* Auto-Sync Timer Section */
.auto-sync-section {
  margin-top: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.auto-sync-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.auto-sync-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.auto-sync-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.3);
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4CAF50;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

#auto-sync-status {
  font-weight: 600;
  font-size: 14px;
  min-width: 40px;
}

.auto-sync-controls {
  display: grid;
  gap: 20px;
}

.timer-setting {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.timer-setting label {
  font-weight: 500;
  font-size: 14px;
}

.timer-setting select {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 14px;
  font-weight: 500;
  min-width: 120px;
}

.auto-sync-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.next-run, .sync-count {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.next-run i, .sync-count i {
  font-size: 16px;
  opacity: 0.8;
}

#countdown-timer {
  font-weight: 600;
  color: #FFE066;
}

#sync-count {
  font-weight: 600;
  color: #66FFB2;
}

/* Dark mode support */
.dark-mode .auto-sync-section {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
}

.dark-mode .timer-setting select {
  background: rgba(255, 255, 255, 0.95);
  color: #2d3748;
}

/* Responsive design */
@media (max-width: 768px) {
  .auto-sync-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .timer-setting {
    flex-direction: column;
    text-align: center;
  }

  .auto-sync-info {
    grid-template-columns: 1fr;
    text-align: center;
  }
}


