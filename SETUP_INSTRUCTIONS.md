# Facebook Posts App - Setup Instructions

Follow these steps to get your Facebook Posts desktop application up and running:

## Quick Start Guide

1. **Install Node.js**:
   - Download and install Node.js from [nodejs.org](https://nodejs.org/) (version 14.x or higher)

2. **Install Dependencies**:
   - Open a command prompt/terminal in the application folder
   - Run: `npm install`

3. **Create a .env file**:
   - Create a file named `.env` in the root folder
   - Add this line: `PORT=3000`

4. **Start the Application**:
   - Windows: Double-click `start.bat`
   - Or run: `npm start`

## Troubleshooting Common Issues

### Application Fails to Start

If you see an error about missing modules:

```
npm install electron-is-dev
```

If you still encounter issues, run:

```
npm install --save-dev electron@latest
```

### Icon Issues

The application uses placeholder icons. You can replace them with your own icons:

- `public/icon.png` - Main application icon (512x512 px)
- `public/icon.ico` - Windows icon
- `public/favicon.ico` - Browser favicon

### Facebook Pages Configuration

Edit `pages.json` to add your own Facebook pages to scrape. The file should contain an array of Facebook page URLs:

```json
[
  "https://www.facebook.com/page1",
  "https://www.facebook.com/page2",
  "https://www.facebook.com/page3"
]
```

### Scraping Frequency

To adjust how often the application scrapes Facebook pages, edit `automation_config.json`:

```json
{
  "minLoopDelay": 300000,
  "maxLoopDelay": 600000,
  "minSearchDelay": 5000,
  "maxSearchDelay": 15000,
  "chunkSize": 3
}
```

- `minLoopDelay` and `maxLoopDelay`: Min and max time (in milliseconds) between scraping cycles
- `minSearchDelay` and `maxSearchDelay`: Min and max time between processing pages
- `chunkSize`: Number of pages processed in each batch

## Building a Standalone Application

To create a standalone executable:

```
npm run build
```

The built application will be in the `dist` folder.

## More Information

For more detailed documentation, see:
- `README.md` - Overview and main features
- `USAGE_GUIDE.md` - Comprehensive usage instructions 