// Migration script to download existing Facebook images locally
const fs = require('fs');
const path = require('path');
const axios = require('axios');
const crypto = require('crypto');

const FINAL_FILTERED_FILE = path.join(__dirname, 'final_filtered_posts.json');
const IMAGES_DIR = path.join(__dirname, 'public', 'images');

// Create images directory if it doesn't exist
if (!fs.existsSync(IMAGES_DIR)) {
  fs.mkdirSync(IMAGES_DIR, { recursive: true });
  console.log('✅ Created images directory');
}

// Function to download and save image locally
async function downloadImage(imageUrl, postId, imageIndex) {
  try {
    if (!imageUrl || !imageUrl.includes('scontent')) {
      return imageUrl; // Return original URL if not a Facebook image
    }

    // Generate a unique filename
    const urlHash = crypto.createHash('md5').update(imageUrl).digest('hex');
    const extension = '.jpg'; // Facebook images are usually JPG
    const filename = `${postId}_${imageIndex}_${urlHash}${extension}`;
    const localPath = path.join(IMAGES_DIR, filename);
    const publicPath = `/images/${filename}`;

    // Check if image already exists
    if (fs.existsSync(localPath)) {
      console.log(`✅ Image already exists: ${filename}`);
      return publicPath;
    }

    // Download the image
    console.log(`📥 Downloading: ${imageUrl.substring(0, 80)}...`);
    
    const response = await axios({
      method: 'GET',
      url: imageUrl,
      responseType: 'stream',
      timeout: 30000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    // Save the image
    const writer = fs.createWriteStream(localPath);
    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log(`✅ Downloaded: ${filename}`);
        resolve(publicPath);
      });
      writer.on('error', (error) => {
        console.error(`❌ Error saving ${filename}:`, error.message);
        resolve(imageUrl); // Return original URL if download fails
      });
    });

  } catch (error) {
    console.error(`❌ Error downloading image:`, error.message);
    return imageUrl; // Return original URL if download fails
  }
}

// Function to migrate all posts
async function migrateAllPosts() {
  try {
    if (!fs.existsSync(FINAL_FILTERED_FILE)) {
      console.log('❌ No posts file found');
      return;
    }

    console.log('📖 Reading posts file...');
    const posts = JSON.parse(fs.readFileSync(FINAL_FILTERED_FILE, 'utf8'));
    
    console.log(`🔍 Found ${posts.length} posts to check`);
    
    let postsWithImages = 0;
    let totalImages = 0;
    let downloadedImages = 0;
    let updatedPosts = 0;

    for (let i = 0; i < posts.length; i++) {
      const post = posts[i];
      const postId = post.normalizedTextHash || `post_${i}`;
      
      if (post.mediaUrls && post.mediaUrls.length > 0) {
        postsWithImages++;
        totalImages += post.mediaUrls.length;
        
        console.log(`\n📝 Processing post ${i + 1}/${posts.length}: ${postId.substring(0, 50)}...`);
        console.log(`   Images: ${post.mediaUrls.length}`);
        
        let hasUpdates = false;
        const newMediaUrls = [];
        
        for (let j = 0; j < post.mediaUrls.length; j++) {
          const originalUrl = post.mediaUrls[j];
          
          // Skip if already a local URL
          if (originalUrl.startsWith('/images/')) {
            newMediaUrls.push(originalUrl);
            continue;
          }
          
          const localUrl = await downloadImage(originalUrl, postId, j);
          newMediaUrls.push(localUrl);
          
          if (localUrl !== originalUrl) {
            downloadedImages++;
            hasUpdates = true;
          }
          
          // Small delay between downloads
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        if (hasUpdates) {
          post.mediaUrls = newMediaUrls;
          updatedPosts++;
        }
      }
      
      // Progress update every 10 posts
      if ((i + 1) % 10 === 0) {
        console.log(`\n📊 Progress: ${i + 1}/${posts.length} posts processed`);
        console.log(`   Downloaded: ${downloadedImages} images`);
        console.log(`   Updated: ${updatedPosts} posts`);
      }
    }

    // Save updated posts
    if (updatedPosts > 0) {
      console.log('\n💾 Saving updated posts...');
      fs.writeFileSync(FINAL_FILTERED_FILE, JSON.stringify(posts, null, 2));
      console.log('✅ Posts file updated');
    }

    // Final summary
    console.log('\n🎉 Migration Complete!');
    console.log(`📊 Summary:`);
    console.log(`   Total posts: ${posts.length}`);
    console.log(`   Posts with images: ${postsWithImages}`);
    console.log(`   Total images: ${totalImages}`);
    console.log(`   Downloaded images: ${downloadedImages}`);
    console.log(`   Updated posts: ${updatedPosts}`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

// Run migration
console.log('🚀 Starting image migration...');
migrateAllPosts();
