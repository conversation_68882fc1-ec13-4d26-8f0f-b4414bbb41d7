<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Setup - Facebook Scraper</title>
    <link rel="stylesheet" href="updates.css">
    <style>
        .setup-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: var(--white);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 30px;
            color: var(--primary-color);
        }
        
        .config-form {
            display: grid;
            gap: 20px;
        }
        
        .form-group {
            display: grid;
            gap: 8px;
        }
        
        .form-group label {
            font-weight: 600;
            color: var(--text-color);
        }
        
        .form-group input, .form-group select {
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: #1e40af;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .status-box {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: 500;
        }
        
        .status-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .status-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        
        .hidden {
            display: none;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .dark-mode .setup-container {
            background: var(--dark-card);
        }
        
        .dark-mode .form-group input,
        .dark-mode .form-group select {
            background: var(--dark-secondary);
            border-color: var(--dark-border);
            color: var(--dark-text);
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-header">
            <h1>🗄️ Database Setup</h1>
            <p>Configure your MySQL database connection</p>
        </div>
        
        <div id="status-container"></div>
        
        <form class="config-form" id="database-config-form">
            <div class="form-row">
                <div class="form-group">
                    <label for="db_host">Database Host</label>
                    <input type="text" id="db_host" name="db_host" value="localhost" required>
                </div>
                <div class="form-group">
                    <label for="db_port">Port</label>
                    <input type="number" id="db_port" name="db_port" value="3306" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="db_name">Database Name</label>
                <input type="text" id="db_name" name="db_name" value="facebook_db" required>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="db_user">Username</label>
                    <input type="text" id="db_user" name="db_user" value="root" required>
                </div>
                <div class="form-group">
                    <label for="db_password">Password</label>
                    <input type="password" id="db_password" name="db_password" value="">
                </div>
            </div>
            
            <div class="form-group">
                <label for="db_ssl">SSL Connection</label>
                <select id="db_ssl" name="db_ssl">
                    <option value="false">Disabled</option>
                    <option value="true">Enabled</option>
                </select>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="auto_sync">Auto Sync</label>
                    <select id="auto_sync" name="auto_sync">
                        <option value="true">Enabled</option>
                        <option value="false">Disabled</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="sync_interval">Sync Interval (minutes)</label>
                    <input type="number" id="sync_interval" name="sync_interval" value="5" min="1">
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="testConnection()">
                    <span id="test-btn-text">Test Connection</span>
                    <span id="test-loading" class="loading hidden"></span>
                </button>
                <button type="submit" class="btn btn-primary">
                    <span id="save-btn-text">Save Configuration</span>
                    <span id="save-loading" class="loading hidden"></span>
                </button>
                <button type="button" class="btn btn-primary" onclick="setupDatabase()">
                    <span id="setup-btn-text">Setup Database</span>
                    <span id="setup-loading" class="loading hidden"></span>
                </button>
            </div>
        </form>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="updates.html" class="btn btn-secondary">Go to Database Dashboard</a>
        </div>
    </div>

    <script>
        // Load current configuration
        window.addEventListener('DOMContentLoaded', loadCurrentConfig);
        
        async function loadCurrentConfig() {
            try {
                const response = await fetch('database-config.php?action=get');
                const config = await response.json();
                
                if (config.success) {
                    document.getElementById('db_host').value = config.data.DB_HOST || 'localhost';
                    document.getElementById('db_port').value = config.data.DB_PORT || '3306';
                    document.getElementById('db_name').value = config.data.DB_NAME || 'facebook_db';
                    document.getElementById('db_user').value = config.data.DB_USER || 'root';
                    document.getElementById('db_password').value = config.data.DB_PASSWORD || '';
                    document.getElementById('db_ssl').value = config.data.DB_SSL || 'false';
                    document.getElementById('auto_sync').value = config.data.AUTO_SYNC_ENABLED || 'true';
                    document.getElementById('sync_interval').value = parseInt(config.data.AUTO_SYNC_INTERVAL || '300000') / 60000;
                }
            } catch (error) {
                console.log('Could not load current config:', error);
            }
        }
        
        // Form submission
        document.getElementById('database-config-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const saveBtn = document.getElementById('save-btn-text');
            const saveLoading = document.getElementById('save-loading');
            
            saveBtn.classList.add('hidden');
            saveLoading.classList.remove('hidden');
            
            const formData = new FormData(e.target);
            formData.append('action', 'save');
            
            try {
                const response = await fetch('database-config.php', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                showStatus(result.success ? 'success' : 'error', result.message);
                
            } catch (error) {
                showStatus('error', 'Failed to save configuration: ' + error.message);
            } finally {
                saveBtn.classList.remove('hidden');
                saveLoading.classList.add('hidden');
            }
        });
        
        async function testConnection() {
            const testBtn = document.getElementById('test-btn-text');
            const testLoading = document.getElementById('test-loading');
            
            testBtn.classList.add('hidden');
            testLoading.classList.remove('hidden');
            
            try {
                const response = await fetch('database-config.php?action=test');
                const result = await response.json();
                
                showStatus(result.success ? 'success' : 'error', result.message);
                
                if (result.server_info) {
                    showStatus('info', `Server: ${result.server_info} (Version: ${result.server_version})`);
                }
                
            } catch (error) {
                showStatus('error', 'Connection test failed: ' + error.message);
            } finally {
                testBtn.classList.remove('hidden');
                testLoading.classList.add('hidden');
            }
        }
        
        async function setupDatabase() {
            const setupBtn = document.getElementById('setup-btn-text');
            const setupLoading = document.getElementById('setup-loading');
            
            setupBtn.classList.add('hidden');
            setupLoading.classList.remove('hidden');
            
            try {
                const response = await fetch('database-config.php?action=setup');
                const result = await response.json();
                
                showStatus(result.success ? 'success' : 'error', result.message);
                
                if (result.details) {
                    result.details.forEach(detail => {
                        showStatus('info', detail);
                    });
                }
                
            } catch (error) {
                showStatus('error', 'Database setup failed: ' + error.message);
            } finally {
                setupBtn.classList.remove('hidden');
                setupLoading.classList.add('hidden');
            }
        }
        
        function showStatus(type, message) {
            const container = document.getElementById('status-container');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-box status-${type}`;
            statusDiv.textContent = message;
            
            container.appendChild(statusDiv);
            
            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 10000);
        }
    </script>
</body>
</html>
