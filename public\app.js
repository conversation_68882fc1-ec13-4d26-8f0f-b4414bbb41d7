// Initialize Socket.io connection
const socket = io();

// DOM Elements
const postsContainer = document.getElementById('posts-container');
const searchInput = document.getElementById('search-input');
const filterButtons = document.querySelectorAll('.filter-buttons button');
const themeToggle = document.querySelector('.theme-toggle');
const postTemplate = document.getElementById('post-template');

// <PERSON><PERSON><PERSON> controls
const startScraperBtn = document.getElementById('start-scraper');
const stopScraperBtn = document.getElementById('stop-scraper');
const pauseScraperBtn = document.getElementById('pause-scraper');
const resumeScraperBtn = document.getElementById('resume-scraper');
const statusDot = document.querySelector('.status-dot');
const statusText = document.querySelector('.status-text');
const progressContainer = document.getElementById('progress-container');
const progressFill = document.querySelector('.progress-fill');
const progressText = document.querySelector('.progress-text');
const nextCycleContainer = document.getElementById('next-cycle');
const nextCycleTime = document.getElementById('next-cycle-time');

// Admin sidebar elements
const adminSidebar = document.getElementById('admin-sidebar');
const adminToggleBtn = document.getElementById('admin-toggle');
const sidebarCloseBtn = document.getElementById('sidebar-close');
const saveConfigBtn = document.getElementById('save-config');
const headlessModeToggle = document.getElementById('headless-mode');
const headlessModeLabel = document.getElementById('headless-mode-label');
const metricsHeadlessModeToggle = document.getElementById('metrics-headless-mode');
const metricsHeadlessModeLabel = document.getElementById('metrics-headless-mode-label');
const chunkSizeInput = document.getElementById('chunk-size');
const parallelPagesInput = document.getElementById('parallel-pages');
const minLoopDelayInput = document.getElementById('min-loop-delay');
const maxLoopDelayInput = document.getElementById('max-loop-delay');
const minSearchDelayInput = document.getElementById('min-search-delay');
const maxSearchDelayInput = document.getElementById('max-search-delay');
const pagesList = document.getElementById('pages-list');
const pageNameInput = document.getElementById('page-name');
const pageUrlInput = document.getElementById('page-url');
const addPageBtn = document.getElementById('add-page');
const pageItemTemplate = document.getElementById('page-item-template');
const authStatus = document.getElementById('auth-status');
const loginButton = document.getElementById('login-button');
const saveSessionButton = document.getElementById('save-session-button');
const clearSessionButton = document.getElementById('clear-session-button');

// Auto-export elements
const autoExportEnabledToggle = document.getElementById('auto-export-enabled');
const autoExportEnabledLabel = document.getElementById('auto-export-enabled-label');
const autoExportTimeInput = document.getElementById('auto-export-time');
const autoExportFrequencySelect = document.getElementById('auto-export-frequency');
const autoExportJsonCheckbox = document.getElementById('auto-export-json');
const triggerManualExportBtn = document.getElementById('trigger-manual-export');
const autoExportStatus = document.getElementById('auto-export-status');
const nextExportTime = document.getElementById('next-export-time');

// New navigation controls
const navigationControl = document.getElementById('navigation-control');
const navigateUrlInput = document.getElementById('navigate-url');
const navigateBtn = document.getElementById('navigate-btn');

// Add a direct listener for the headless mode toggle
headlessModeToggle.addEventListener('change', function(event) {
  // Prevent any other event handlers from running
  event.stopPropagation();
  
  const isChecked = this.checked;
  console.log('Headless mode toggle changed to:', isChecked, '(type:', typeof isChecked, ')');
  updateHeadlessModeLabel(isChecked);
  
  // Show indicator
  headlessModeLabel.textContent = 'Saving...';
  
  // Use dedicated headless endpoint
  fetch('/api/headless', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ headlessMode: isChecked })
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(result => {
      if (result.success) {
        showNotification('Headless mode ' + (isChecked ? 'enabled' : 'disabled'), 'success');
      } else {
        showNotification('Failed to update headless mode: ' + (result.error || 'Unknown error'), 'error');
        // Revert the toggle if server failed
        headlessModeToggle.checked = !isChecked;
        updateHeadlessModeLabel(!isChecked);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      showNotification('Error updating headless mode: ' + error.message, 'error');
      // Revert the toggle on error
      headlessModeToggle.checked = !isChecked;
      updateHeadlessModeLabel(!isChecked);
    })
    .finally(() => {
      // Restore label
      updateHeadlessModeLabel(headlessModeToggle.checked);
    });
});

// Add event handler for metrics headless mode toggle
metricsHeadlessModeToggle.addEventListener('change', function(event) {
  event.stopPropagation();

  const isChecked = this.checked;
  console.log('Metrics headless mode toggle changed to:', isChecked);
  updateMetricsHeadlessModeLabel(isChecked);

  // Show indicator
  metricsHeadlessModeLabel.textContent = 'Saving...';

  // Use dedicated metrics headless endpoint
  fetch('/api/metrics-headless', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ metricsHeadlessMode: isChecked })
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(result => {
      if (result.success) {
        showNotification('Metrics headless mode ' + (isChecked ? 'enabled' : 'disabled'), 'success');
      } else {
        showNotification('Failed to update metrics headless mode: ' + (result.error || 'Unknown error'), 'error');
        // Revert the toggle if server failed
        metricsHeadlessModeToggle.checked = !isChecked;
        updateMetricsHeadlessModeLabel(!isChecked);
      }
    })
    .catch(error => {
      console.error('Error:', error);
      showNotification('Error updating metrics headless mode: ' + error.message, 'error');
      // Revert the toggle on error
      metricsHeadlessModeToggle.checked = !isChecked;
      updateMetricsHeadlessModeLabel(!isChecked);
    })
    .finally(() => {
      // Restore label
      updateMetricsHeadlessModeLabel(metricsHeadlessModeToggle.checked);
    });
});

// Auto-export toggle event listener
autoExportEnabledToggle.addEventListener('change', function(event) {
  event.stopPropagation();

  const isChecked = this.checked;
  console.log('Auto-export toggle changed to:', isChecked);
  updateAutoExportEnabledLabel(isChecked);

  // Show indicator
  autoExportEnabledLabel.textContent = 'Saving...';

  // Save auto-export configuration
  saveAutoExportConfig();
});

// Manual export trigger
triggerManualExportBtn.addEventListener('click', function() {
  triggerManualExport();
});

// Auto-export time and frequency change listeners
autoExportTimeInput.addEventListener('change', saveAutoExportConfig);
autoExportFrequencySelect.addEventListener('change', saveAutoExportConfig);
autoExportJsonCheckbox.addEventListener('change', saveAutoExportConfig);

// Update auto-export enabled label
function updateAutoExportEnabledLabel(isEnabled) {
  if (autoExportEnabledLabel) {
    autoExportEnabledLabel.textContent = isEnabled ? 'Enabled' : 'Disabled';
    autoExportEnabledLabel.className = isEnabled ? 'toggle-label enabled' : 'toggle-label disabled';
  }
}

// Update headless mode label
function updateHeadlessModeLabel(isEnabled) {
  if (headlessModeLabel) {
    headlessModeLabel.textContent = isEnabled ? 'Enabled' : 'Disabled';
    headlessModeLabel.className = isEnabled ? 'toggle-label enabled' : 'toggle-label disabled';
  }
}

// Update metrics headless mode label
function updateMetricsHeadlessModeLabel(isEnabled) {
  if (metricsHeadlessModeLabel) {
    metricsHeadlessModeLabel.textContent = isEnabled ? 'Enabled' : 'Disabled';
    metricsHeadlessModeLabel.className = isEnabled ? 'toggle-label enabled' : 'toggle-label disabled';
  }
}

// State variables
let posts = [];
let filteredPosts = [];
let currentFilter = 'all';
let searchQuery = '';
let isScraperRunning = false;
let pages = []; // Store pages data
let teamMembers = []; // Store team members for filtering

// Pagination variables
let currentPage = 1;
let postsPerPage = 12;
let totalPages = 1;

// Notification System
let notifications = [];
let unreadCount = 0;
const MAX_NOTIFICATIONS = 100;

// Notification DOM elements
const notificationBell = document.getElementById('notification-bell');
const notificationBadge = document.getElementById('notification-badge');
const notificationDropdown = document.getElementById('notification-dropdown');
const notificationList = document.getElementById('notification-list');
const noNotifications = document.getElementById('no-notifications');
const clearAllNotificationsBtn = document.getElementById('clear-all-notifications');
const closeNotificationDropdownBtn = document.getElementById('close-notification-dropdown');
const viewAllNotificationsBtn = document.getElementById('view-all-notifications');
const allNotificationsModal = document.getElementById('allNotificationsModal');
const allNotificationsList = document.getElementById('all-notifications-list');
const noNotificationsModal = document.getElementById('no-notifications-modal');
const clearAllNotificationsModalBtn = document.getElementById('clear-all-notifications-modal');
const notificationCount = document.getElementById('notification-count');
const notificationFilterButtons = document.querySelectorAll('.filter-btn');

// Check for saved theme preference
if (localStorage.getItem('darkMode') === 'true') {
  document.body.classList.add('dark-mode');
  themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
}

// Fetch posts on page load
window.addEventListener('DOMContentLoaded', () => {
  // Clear any existing progress bars first
  clearAllProgressBars();

  checkScraperStatus();
  setupImageModal();
  loadConfiguration();
  loadPages();

  // Load team members first, then fetch posts
  loadTeamMembers().then(() => {
    fetchPosts(); // Load posts after team members are loaded
  }).catch(() => {
    fetchPosts(); // Load posts even if team members fail to load
  });

  checkAuthStatus(); // Check Facebook authentication status
  startTimeUpdates(); // Start real-time time updates
  startDynamicTimeUpdates(); // Start dynamic "last updated" time updates
  loadAutoExportConfig(); // Load auto-export configuration

  // Add minimize button if in desktop app context
  if (window.ipcRenderer) {
    addMinimizeButton();
  }

  // Create overlay for sidebar
  createSidebarOverlay();

  // Setup event delegation for change link buttons
  setupChangeLinkEventDelegation();

  // Add direct click handler to the toggle switch label
  const toggleSwitch = document.querySelector('.toggle-switch');
  if (toggleSwitch) {
    toggleSwitch.addEventListener('click', function(e) {
      // Only handle clicks on the label or slider, not the input
      if (e.target !== headlessModeToggle) {
        // Toggle the checkbox
        headlessModeToggle.checked = !headlessModeToggle.checked;

        // Manually trigger the change event
        const changeEvent = new Event('change', { bubbles: true });
        headlessModeToggle.dispatchEvent(changeEvent);
      }
    });
  }

  // Initialize notification system
  initializeNotificationSystem();
});

// ===== NOTIFICATION SYSTEM =====

function initializeNotificationSystem() {
  // Load notifications from localStorage
  loadNotificationsFromStorage();

  // Set up event listeners
  if (notificationBell) {
    notificationBell.addEventListener('click', toggleNotificationDropdown);
  }

  if (clearAllNotificationsBtn) {
    clearAllNotificationsBtn.addEventListener('click', clearAllNotifications);
  }

  if (closeNotificationDropdownBtn) {
    closeNotificationDropdownBtn.addEventListener('click', closeNotificationDropdown);
  }

  if (viewAllNotificationsBtn) {
    viewAllNotificationsBtn.addEventListener('click', openAllNotificationsModal);
  }

  if (clearAllNotificationsModalBtn) {
    clearAllNotificationsModalBtn.addEventListener('click', clearAllNotifications);
  }

  // Set up filter buttons
  if (notificationFilterButtons) {
    notificationFilterButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const filter = e.target.getAttribute('data-filter');
        filterNotifications(filter);

        // Update active filter button
        notificationFilterButtons.forEach(b => b.classList.remove('active'));
        e.target.classList.add('active');
      });
    });
  }

  // Close dropdown when clicking outside
  document.addEventListener('click', (e) => {
    if (!e.target.closest('.notification-container')) {
      closeNotificationDropdown();
    }
  });

  // Close modal when clicking outside
  if (allNotificationsModal) {
    allNotificationsModal.addEventListener('click', (e) => {
      if (e.target === allNotificationsModal) {
        closeAllNotificationsModal();
      }
    });
  }

  // Update notification display
  updateNotificationDisplay();

  // Add a welcome notification if this is the first time loading
  if (notifications.length === 0) {
    addNotification('Welcome to the Facebook Monitoring Dashboard! 🎉', 'info', { isWelcome: true });
  }
}

function addNotification(message, type = 'info', data = {}) {
  const notification = {
    id: Date.now() + Math.random(),
    message,
    type, // 'success', 'info', 'warning', 'error'
    timestamp: new Date().toISOString(),
    read: false,
    data
  };

  // Add to beginning of array
  notifications.unshift(notification);

  // Limit notifications
  if (notifications.length > MAX_NOTIFICATIONS) {
    notifications = notifications.slice(0, MAX_NOTIFICATIONS);
  }

  // Update unread count
  unreadCount = notifications.filter(n => !n.read).length;

  // Save to localStorage
  saveNotificationsToStorage();

  // Update UI
  updateNotificationDisplay();
  updateNotificationBadge();

  // Show bell animation
  if (notificationBell) {
    notificationBell.classList.add('has-notifications');
    setTimeout(() => {
      notificationBell.classList.remove('has-notifications');
    }, 500);
  }

  console.log('Notification added:', notification);
}

function updateNotificationDisplay() {
  // Update dropdown
  if (notificationList && noNotifications) {
    if (notifications.length === 0) {
      notificationList.innerHTML = '';
      noNotifications.style.display = 'block';
    } else {
      noNotifications.style.display = 'none';
      const recentNotifications = notifications.slice(0, 5); // Show only 5 most recent
      notificationList.innerHTML = recentNotifications.map(createNotificationHTML).join('');
    }
  }

  // Update modal
  updateAllNotificationsModal();
}

function createNotificationHTML(notification) {
  const timeAgo = getTimeAgo(new Date(notification.timestamp));
  const iconClass = getNotificationIcon(notification.type);

  return `
    <div class="notification-item ${notification.read ? '' : 'unread'}" data-id="${notification.id}">
      <div class="notification-content">
        <div class="notification-icon ${notification.type}">
          <i class="${iconClass}"></i>
        </div>
        <div class="notification-text">
          <div class="notification-message">${notification.message}</div>
          <div class="notification-time">${timeAgo}</div>
        </div>
      </div>
    </div>
  `;
}

function getNotificationIcon(type) {
  switch (type) {
    case 'success': return 'fas fa-check-circle';
    case 'warning': return 'fas fa-exclamation-triangle';
    case 'error': return 'fas fa-times-circle';
    case 'info':
    default: return 'fas fa-info-circle';
  }
}

function getTimeAgo(date) {
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  return date.toLocaleDateString();
}

function updateNotificationBadge() {
  if (notificationBadge) {
    if (unreadCount > 0) {
      notificationBadge.textContent = unreadCount > 99 ? '99+' : unreadCount;
      notificationBadge.classList.add('show', 'pulse');
      setTimeout(() => notificationBadge.classList.remove('pulse'), 1000);
    } else {
      notificationBadge.classList.remove('show', 'pulse');
    }
  }
}

function toggleNotificationDropdown() {
  if (notificationDropdown) {
    const isVisible = notificationDropdown.classList.contains('show');
    if (isVisible) {
      closeNotificationDropdown();
    } else {
      openNotificationDropdown();
    }
  }
}

function openNotificationDropdown() {
  if (notificationDropdown) {
    notificationDropdown.classList.add('show');
    // Mark notifications as read when dropdown is opened
    markNotificationsAsRead();
  }
}

function closeNotificationDropdown() {
  if (notificationDropdown) {
    notificationDropdown.classList.remove('show');
  }
}

function markNotificationsAsRead() {
  let hasUnread = false;
  notifications.forEach(notification => {
    if (!notification.read) {
      notification.read = true;
      hasUnread = true;
    }
  });

  if (hasUnread) {
    unreadCount = 0;
    updateNotificationBadge();
    saveNotificationsToStorage();
  }
}

function clearAllNotifications() {
  notifications = [];
  unreadCount = 0;
  saveNotificationsToStorage();
  updateNotificationDisplay();
  updateNotificationBadge();
  closeNotificationDropdown();
  closeAllNotificationsModal();
}

function openAllNotificationsModal() {
  if (allNotificationsModal) {
    allNotificationsModal.classList.add('show');
    updateAllNotificationsModal();
    // Reset filter to 'all'
    if (notificationFilterButtons) {
      notificationFilterButtons.forEach(btn => {
        btn.classList.toggle('active', btn.getAttribute('data-filter') === 'all');
      });
    }
  }
}

function closeAllNotificationsModal() {
  if (allNotificationsModal) {
    allNotificationsModal.classList.remove('show');
  }
}

function updateAllNotificationsModal() {
  if (allNotificationsList && noNotificationsModal && notificationCount) {
    if (notifications.length === 0) {
      allNotificationsList.innerHTML = '';
      noNotificationsModal.style.display = 'block';
      notificationCount.textContent = '0 notifications';
    } else {
      noNotificationsModal.style.display = 'none';
      allNotificationsList.innerHTML = notifications.map(createNotificationHTML).join('');
      notificationCount.textContent = `${notifications.length} notification${notifications.length !== 1 ? 's' : ''}`;
    }
  }
}

function filterNotifications(filter) {
  let filteredNotifications = notifications;

  if (filter !== 'all') {
    filteredNotifications = notifications.filter(n => n.type === filter);
  }

  if (allNotificationsList && noNotificationsModal && notificationCount) {
    if (filteredNotifications.length === 0) {
      allNotificationsList.innerHTML = '';
      noNotificationsModal.style.display = 'block';
      notificationCount.textContent = `0 ${filter} notifications`;
    } else {
      noNotificationsModal.style.display = 'none';
      allNotificationsList.innerHTML = filteredNotifications.map(createNotificationHTML).join('');
      notificationCount.textContent = `${filteredNotifications.length} ${filter === 'all' ? '' : filter} notification${filteredNotifications.length !== 1 ? 's' : ''}`;
    }
  }
}

function saveNotificationsToStorage() {
  try {
    localStorage.setItem('notifications', JSON.stringify(notifications));
    localStorage.setItem('unreadCount', unreadCount.toString());
  } catch (error) {
    console.error('Failed to save notifications to localStorage:', error);
  }
}

function loadNotificationsFromStorage() {
  try {
    const stored = localStorage.getItem('notifications');
    const storedUnread = localStorage.getItem('unreadCount');

    if (stored) {
      notifications = JSON.parse(stored);
    }

    if (storedUnread) {
      unreadCount = parseInt(storedUnread) || 0;
    }

    // Clean up old notifications (older than 7 days)
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);

    notifications = notifications.filter(n => new Date(n.timestamp) > weekAgo);
    unreadCount = notifications.filter(n => !n.read).length;

    // Save cleaned data back
    saveNotificationsToStorage();
  } catch (error) {
    console.error('Failed to load notifications from localStorage:', error);
    notifications = [];
    unreadCount = 0;
  }
}

// ===== END NOTIFICATION SYSTEM =====

// Global functions for HTML onclick handlers
window.closeAllNotificationsModal = closeAllNotificationsModal;

function createSidebarOverlay() {
  const overlay = document.createElement('div');
  overlay.className = 'sidebar-overlay';
  document.body.appendChild(overlay);

  overlay.addEventListener('click', () => {
    closeSidebar();
  });
}

// Setup event delegation for change link buttons
function setupChangeLinkEventDelegation() {
  // Use event delegation on the posts container
  const postsContainer = document.getElementById('posts-container');
  if (postsContainer) {
    postsContainer.addEventListener('click', function(e) {
      // Check if the clicked element is a change link button
      if (e.target.closest('.change-link-btn')) {
        const button = e.target.closest('.change-link-btn');
        const postId = button.getAttribute('data-post-id');
        const currentUrl = button.getAttribute('data-current-url');

        if (postId && currentUrl) {
          console.log('Opening change link modal for post:', postId);
          openChangeLinkModal(postId, currentUrl);
        } else {
          console.error('Missing data attributes on change link button:', {
            button,
            postId,
            currentUrl,
            allAttributes: Array.from(button.attributes).map(attr => ({ name: attr.name, value: attr.value }))
          });
          showNotification('Error: Missing post data', 'error');
        }
      }

      // Check if the clicked element is an update metrics button
      if (e.target.closest('.update-metrics-btn')) {
        const button = e.target.closest('.update-metrics-btn');
        const postId = button.getAttribute('data-post-id');
        const targetUrl = button.getAttribute('data-target-url');

        if (postId && targetUrl) {
          console.log('Updating metrics for post:', postId, 'URL:', targetUrl);
          updatePostMetrics(postId, targetUrl, button);
        } else {
          console.error('Missing data attributes on update metrics button:', {
            button,
            postId,
            targetUrl,
            allAttributes: Array.from(button.attributes).map(attr => ({ name: attr.name, value: attr.value }))
          });
          showNotification('Error: Missing post data', 'error');
        }
      }

      // Check if the clicked element is a delete post button
      if (e.target.closest('.delete-post-btn')) {
        const button = e.target.closest('.delete-post-btn');
        const postId = button.getAttribute('data-post-id');

        if (postId) {
          console.log('Opening delete confirmation for post:', postId);
          openDeletePostModal(postId);
        } else {
          console.error('Missing data attributes on delete post button:', {
            button,
            postId,
            allAttributes: Array.from(button.attributes).map(attr => ({ name: attr.name, value: attr.value }))
          });
          showNotification('Error: Missing post data', 'error');
        }
      }
    });
  }
}

// Admin sidebar functions
function openSidebar() {
  adminSidebar.classList.add('open');
  document.querySelector('.sidebar-overlay').classList.add('show');
  
  // Reload configuration and pages when sidebar opens
  loadConfiguration();
  loadPages();
  checkApiConnectivity();
  checkAuthStatus(); // Check authentication status when opening sidebar
}

function closeSidebar() {
  adminSidebar.classList.remove('open');
  document.querySelector('.sidebar-overlay').classList.remove('show');
}

// Check API connectivity
function checkApiConnectivity() {
  fetch('/api/test')
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      console.log('API connectivity test:', data);
    })
    .catch(error => {
      console.error('API connectivity error:', error);
      showNotification('API connectivity issue: ' + error.message, 'warning');
    });
}

// Load configuration from server
function loadConfiguration() {
  fetch('/api/config')
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(config => {
      // Update UI with config values
      const isHeadless = config.headlessMode;
      const isMetricsHeadless = config.metricsHeadlessMode !== undefined ? config.metricsHeadlessMode : false;
      console.log('Loaded headlessMode from server:', isHeadless);
      console.log('Loaded metricsHeadlessMode from server:', isMetricsHeadless);
      headlessModeToggle.checked = isHeadless;
      updateHeadlessModeLabel(isHeadless);
      metricsHeadlessModeToggle.checked = isMetricsHeadless;
      updateMetricsHeadlessModeLabel(isMetricsHeadless);
      
      chunkSizeInput.value = config.chunkSize;
      parallelPagesInput.value = config.parallelPages || 1;
      minLoopDelayInput.value = Math.floor(config.minLoopDelay / 60000); // Convert ms to minutes
      maxLoopDelayInput.value = Math.floor(config.maxLoopDelay / 60000);
      minSearchDelayInput.value = Math.floor(config.minSearchDelay / 1000); // Convert ms to seconds
      maxSearchDelayInput.value = Math.floor(config.maxSearchDelay / 1000);

      // Update metrics update delay field
      if (config.metricsUpdateDelay !== undefined) {
        const metricsDelayInput = document.getElementById('metrics-update-delay');
        if (metricsDelayInput) {
          metricsDelayInput.value = Math.floor(config.metricsUpdateDelay / 1000); // Convert ms to seconds
        }
      }

      // Update posts per page field
      if (config.postsPerPage !== undefined) {
        const postsPerPageInput = document.getElementById('posts-per-page');
        if (postsPerPageInput) {
          postsPerPageInput.value = config.postsPerPage;
        }
      }
    })
    .catch(error => {
      console.error('Error loading configuration:', error);
      // Add fallback values if API fails
      headlessModeToggle.checked = true;
      updateHeadlessModeLabel(true);
      metricsHeadlessModeToggle.checked = false;
      updateMetricsHeadlessModeLabel(false);
      
      chunkSizeInput.value = 10;
      parallelPagesInput.value = 1;
      minLoopDelayInput.value = 1;
      maxLoopDelayInput.value = 10;
      minSearchDelayInput.value = 1;
      maxSearchDelayInput.value = 60;

      // Set fallback for metrics update delay
      const metricsDelayInput = document.getElementById('metrics-update-delay');
      if (metricsDelayInput) {
        metricsDelayInput.value = 30; // Default 30 seconds
      }

      showNotification('Failed to load configuration: ' + error.message, 'error');
    });
}

// Save configuration to server
function saveConfiguration() {
  // Get values from inputs
  const minLoopDelayMinutes = parseInt(minLoopDelayInput.value) || 1;
  const maxLoopDelayMinutes = parseInt(maxLoopDelayInput.value) || 10;
  const minSearchDelaySeconds = parseInt(minSearchDelayInput.value) || 1;
  const maxSearchDelaySeconds = parseInt(maxSearchDelayInput.value) || 60;
  const chunkSizeValue = parseInt(chunkSizeInput.value) || 10;
  const parallelPagesValue = parseInt(parallelPagesInput.value) || 1;
  const isHeadless = headlessModeToggle.checked;
  const isMetricsHeadless = metricsHeadlessModeToggle.checked;

  // Get metrics update delay
  const metricsDelayInput = document.getElementById('metrics-update-delay');
  const metricsUpdateDelaySeconds = metricsDelayInput ? (parseInt(metricsDelayInput.value) || 30) : 30;

  // Get posts per page
  const postsPerPageInput = document.getElementById('posts-per-page');
  const postsPerPageValue = postsPerPageInput ? (parseInt(postsPerPageInput.value) || 5) : 5;

  console.log('Current form values:');
  console.log('- Headless mode:', isHeadless);
  console.log('- Metrics headless mode:', isMetricsHeadless);
  console.log('- Chunk size:', chunkSizeValue);
  console.log('- Parallel pages:', parallelPagesValue);
  console.log('- Min loop delay:', minLoopDelayMinutes, 'minutes');
  console.log('- Max loop delay:', maxLoopDelayMinutes, 'minutes');
  console.log('- Min search delay:', minSearchDelaySeconds, 'seconds');
  console.log('- Max search delay:', maxSearchDelaySeconds, 'seconds');
  console.log('- Metrics update delay:', metricsUpdateDelaySeconds, 'seconds');
  console.log('- Posts per page:', postsPerPageValue);
  
  // Client-side validation
  const clientValidationErrors = [];
  
  // Validate min/max loop delay
  if (minLoopDelayMinutes < 1) {
    clientValidationErrors.push('Minimum cycle delay cannot be less than 1 minute');
  }
  if (maxLoopDelayMinutes > 720) { // 12 hours
    clientValidationErrors.push('Maximum cycle delay cannot be more than 12 hours');
  }
  if (minLoopDelayMinutes > maxLoopDelayMinutes) {
    clientValidationErrors.push('Minimum cycle delay cannot be greater than maximum cycle delay');
  }
  
  // Validate min/max search delay
  if (minSearchDelaySeconds < 1) {
    clientValidationErrors.push('Minimum search delay cannot be less than 1 second');
  }
  if (maxSearchDelaySeconds > 300) { // 5 minutes
    clientValidationErrors.push('Maximum search delay cannot be more than 5 minutes');
  }
  if (minSearchDelaySeconds > maxSearchDelaySeconds) {
    clientValidationErrors.push('Minimum search delay cannot be greater than maximum search delay');
  }
  
  // Validate chunk size
  if (chunkSizeValue < 1) {
    clientValidationErrors.push('Chunk size cannot be less than 1');
  }
  if (chunkSizeValue > 50) {
    clientValidationErrors.push('Chunk size cannot be more than 50');
  }

  // Validate parallel pages
  if (parallelPagesValue < 1) {
    clientValidationErrors.push('Parallel pages cannot be less than 1');
  }
  if (parallelPagesValue > 10) {
    clientValidationErrors.push('Parallel pages cannot be more than 10');
  }

  // Validate metrics update delay
  if (metricsUpdateDelaySeconds < 1) {
    clientValidationErrors.push('Metrics update delay cannot be less than 1 second');
  }
  if (metricsUpdateDelaySeconds > 300) {
    clientValidationErrors.push('Metrics update delay cannot be more than 300 seconds (5 minutes)');
  }

  // Validate posts per page
  if (postsPerPageValue < 1) {
    clientValidationErrors.push('Posts per page cannot be less than 1');
  }
  if (postsPerPageValue > 50) {
    clientValidationErrors.push('Posts per page cannot be more than 50');
  }
  
  // Show validation errors if any
  if (clientValidationErrors.length > 0) {
    clientValidationErrors.forEach(error => {
      showNotification(error, 'warning');
    });
    return;
  }
  
  // Create config object with converted values
  const config = {
    headlessMode: Boolean(isHeadless),
    metricsHeadlessMode: Boolean(isMetricsHeadless),
    chunkSize: chunkSizeValue,
    parallelPages: parallelPagesValue,
    minLoopDelay: minLoopDelayMinutes * 60000, // Convert minutes to ms
    maxLoopDelay: maxLoopDelayMinutes * 60000,
    minSearchDelay: minSearchDelaySeconds * 1000, // Convert seconds to ms
    maxSearchDelay: maxSearchDelaySeconds * 1000,
    metricsUpdateDelay: metricsUpdateDelaySeconds * 1000, // Convert seconds to ms
    postsPerPage: postsPerPageValue
  };
  
  // Show saving indicator
  saveConfigBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
  saveConfigBtn.disabled = true;
  
  fetch('/api/config', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(config)
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(result => {
      if (result.success) {
        showNotification('Configuration saved successfully', 'success');
      } else {
        // Handle validation errors from server
        if (result.validationErrors && result.validationErrors.length > 0) {
          result.validationErrors.forEach(error => {
            showNotification(error, 'warning');
          });
        } else {
          showNotification('Failed to save configuration: ' + (result.error || 'Unknown error'), 'error');
        }
      }
    })
    .catch(error => {
      console.error('Error saving configuration:', error);
      showNotification('Failed to save configuration: ' + error.message, 'error');
    })
    .finally(() => {
      // Reset button state
      saveConfigBtn.innerHTML = '<i class="fas fa-save"></i> Save Configuration';
      saveConfigBtn.disabled = false;
    });
}

// Load auto-export configuration from server
function loadAutoExportConfig() {
  fetch('/api/auto-export/config')
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(result => {
      if (result.success && result.config) {
        const config = result.config;

        // Update UI with config values
        autoExportEnabledToggle.checked = config.autoExportEnabled;
        updateAutoExportEnabledLabel(config.autoExportEnabled);

        autoExportTimeInput.value = config.autoExportTime;
        autoExportFrequencySelect.value = config.autoExportFrequency;
        autoExportJsonCheckbox.checked = config.autoExportJsonEnabled;

        // Update next export time display
        if (config.nextExportTime) {
          const nextTime = new Date(config.nextExportTime);
          nextExportTime.textContent = nextTime.toLocaleString();
          autoExportStatus.style.display = 'block';
        } else {
          autoExportStatus.style.display = 'none';
        }

        console.log('Auto-export configuration loaded:', config);
      }
    })
    .catch(error => {
      console.error('Error loading auto-export configuration:', error);
      // Set fallback values
      autoExportEnabledToggle.checked = false;
      updateAutoExportEnabledLabel(false);
      autoExportTimeInput.value = '02:00';
      autoExportFrequencySelect.value = 'daily';
      autoExportJsonCheckbox.checked = true;
      autoExportStatus.style.display = 'none';
    });
}

// Save auto-export configuration to server
function saveAutoExportConfig() {
  const config = {
    autoExportEnabled: autoExportEnabledToggle.checked,
    autoExportTime: autoExportTimeInput.value,
    autoExportFrequency: autoExportFrequencySelect.value,
    autoExportJsonEnabled: autoExportJsonCheckbox.checked
  };

  console.log('Saving auto-export configuration:', config);

  fetch('/api/auto-export/config', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(config)
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(result => {
      if (result.success) {
        showNotification('Auto-export configuration saved successfully', 'success');

        // Update next export time display
        if (result.config && result.config.nextExportTime) {
          const nextTime = new Date(result.config.nextExportTime);
          nextExportTime.textContent = nextTime.toLocaleString();
          autoExportStatus.style.display = 'block';
        } else {
          autoExportStatus.style.display = 'none';
        }
      } else {
        showNotification('Failed to save auto-export configuration: ' + (result.error || 'Unknown error'), 'error');
        // Revert the toggle if server failed
        autoExportEnabledToggle.checked = !autoExportEnabledToggle.checked;
        updateAutoExportEnabledLabel(autoExportEnabledToggle.checked);
      }
    })
    .catch(error => {
      console.error('Error saving auto-export configuration:', error);
      showNotification('Error saving auto-export configuration: ' + error.message, 'error');
      // Revert the toggle on error
      autoExportEnabledToggle.checked = !autoExportEnabledToggle.checked;
      updateAutoExportEnabledLabel(autoExportEnabledToggle.checked);
    })
    .finally(() => {
      // Restore label
      updateAutoExportEnabledLabel(autoExportEnabledToggle.checked);
    });
}

// Trigger manual export - uses the same individual member export functionality
async function triggerManualExport() {
  const originalText = triggerManualExportBtn.innerHTML;

  try {
    // Show loading state
    triggerManualExportBtn.disabled = true;
    triggerManualExportBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting All Members...';

    // First, get all team members
    const teamResponse = await fetch('/api/team');
    if (!teamResponse.ok) {
      throw new Error('Failed to fetch team members');
    }

    const teamMembers = await teamResponse.json();

    if (!teamMembers || teamMembers.length === 0) {
      showNotification('No team members found to export', 'warning');
      return;
    }

    // Show progress notification and log team members
    console.log('Manual export - Team members to export:', teamMembers.map(m => ({ id: m.id, name: m.name })));
    showNotification(`Starting manual export for ${teamMembers.length} team members...`, 'info');

    // Export each member individually
    const exportResults = [];
    const errors = [];

    for (let i = 0; i < teamMembers.length; i++) {
      const member = teamMembers[i];

      // Update button text with progress
      triggerManualExportBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> Exporting ${member.name} (${i + 1}/${teamMembers.length})`;

      try {
        // Call the individual member export endpoint
        const exportResponse = await fetch(`/api/team/export-member/${member.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ format: 'json' })
        });

        const exportResult = await exportResponse.json();

        console.log(`Manual export result for ${member.name}:`, exportResult);

        if (exportResult.success) {
          exportResults.push({
            memberName: member.name,
            fileName: exportResult.fileName,
            postsCount: exportResult.postsCount,
            fileSize: exportResult.fileSize,
            success: true
          });

          // Show progress notification
          showNotification(`✅ Exported ${member.name}: ${exportResult.postsCount} posts (${i + 1}/${teamMembers.length})`, 'success');
        } else {
          const errorMsg = exportResult.error || exportResult.message || 'Unknown error';
          console.warn(`Manual export failed for ${member.name}:`, errorMsg);
          errors.push(`${member.name}: ${errorMsg}`);
          exportResults.push({
            memberName: member.name,
            success: false,
            error: errorMsg,
            postsCount: exportResult.postsCount || 0
          });

          // Show warning notification for members with no posts
          if (exportResult.postsCount === 0) {
            showNotification(`⚠️ ${member.name}: No posts found (${i + 1}/${teamMembers.length})`, 'warning');
          } else {
            showNotification(`❌ Failed to export ${member.name} (${i + 1}/${teamMembers.length})`, 'error');
          }
        }
      } catch (memberError) {
        console.error(`Error exporting ${member.name}:`, memberError);
        errors.push(`${member.name}: ${memberError.message}`);
        exportResults.push({
          memberName: member.name,
          success: false,
          error: memberError.message
        });
      }

      // Small delay between exports to avoid overwhelming the server
      if (i < teamMembers.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Show final results
    const successCount = exportResults.filter(r => r.success).length;
    const errorCount = errors.length;

    // Show success notification
    if (errorCount === 0) {
      showNotification(`🎉 Manual export completed! All ${successCount} team members exported successfully!`, 'success');
    } else {
      showNotification(`⚠️ Manual export completed: ${successCount} successful, ${errorCount} failed`, 'warning');
    }

    // Reload auto-export config to update next export time
    loadAutoExportConfig();

  } catch (error) {
    console.error(`Error during manual export:`, error);
    showNotification(`Error during manual export: ${error.message}`, 'error');
  } finally {
    // Reset button state
    triggerManualExportBtn.disabled = false;
    triggerManualExportBtn.innerHTML = originalText;
  }
}

// Load pages from server
function loadPages() {
  fetch('/api/pages')
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      pages = data;
      renderPages();
    })
    .catch(error => {
      console.error('Error loading pages:', error);
      pagesList.innerHTML = `<div class="error-message">Failed to load pages: ${error.message}</div>`;
    });
}

// Load team members from server
function loadTeamMembers() {
  return fetch('/api/team')
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      teamMembers = data;
      renderFilterButtons();
      console.log('Team members loaded:', teamMembers);
      return data;
    })
    .catch(error => {
      console.error('Error loading team members:', error);
      // Fallback to default filters if team loading fails
      renderFilterButtons();
      throw error;
    });
}

// Find which team member is assigned to a specific page URL
function findTeamMemberForPage(pageUrl) {
  if (!pageUrl || !teamMembers || teamMembers.length === 0) {
    console.log('findTeamMemberForPage: Missing data', { pageUrl, teamMembersCount: teamMembers?.length });
    return null;
  }

  console.log('findTeamMemberForPage: Searching for', pageUrl);
  console.log('Available team members:', teamMembers);

  for (const member of teamMembers) {
    console.log(`Checking member: ${member.name}`, member.assignedPages);
    if (member.assignedPages && member.assignedPages.length > 0) {
      const isAssigned = member.assignedPages.some(assignedPage => {
        console.log(`  Comparing with assigned page:`, assignedPage);
        if (typeof assignedPage === 'string') {
          // If assigned page is a string, check if page URL contains it
          const matches = pageUrl.includes(assignedPage);
          console.log(`  String comparison: "${pageUrl}".includes("${assignedPage}") = ${matches}`);
          return matches;
        } else if (assignedPage.link) {
          // If assigned page is an object with link property
          const matches = pageUrl === assignedPage.link;
          console.log(`  Object comparison: "${pageUrl}" === "${assignedPage.link}" = ${matches}`);
          return matches;
        }
        return false;
      });

      if (isAssigned) {
        console.log(`✅ Found match: ${member.name}`);
        return member;
      }
    }
  }

  console.log('❌ No team member found for', pageUrl);
  return null;
}

// Render filter buttons based on team members
function renderFilterButtons() {
  const teamDropdownMenu = document.getElementById('team-dropdown-menu');
  if (!teamDropdownMenu) return;

  // Clear existing dropdown options
  teamDropdownMenu.innerHTML = '';

  // Add team member options to dropdown
  teamMembers.forEach(member => {
    const option = document.createElement('button');
    option.className = 'team-dropdown-option';
    option.setAttribute('data-filter', member.id);
    option.textContent = member.name;
    option.addEventListener('click', handleTeamMemberSelect);
    teamDropdownMenu.appendChild(option);
  });

  // If no team members, add a placeholder
  if (teamMembers.length === 0) {
    const noMembersOption = document.createElement('button');
    noMembersOption.className = 'team-dropdown-option';
    noMembersOption.setAttribute('data-filter', 'no-members');
    noMembersOption.textContent = 'No Team Members';
    noMembersOption.disabled = true;
    noMembersOption.style.opacity = '0.5';
    teamDropdownMenu.appendChild(noMembersOption);
  }

  // Setup dropdown functionality
  setupTeamDropdown();
}

// Setup team dropdown functionality
function setupTeamDropdown() {
  const dropdownTrigger = document.getElementById('team-dropdown-btn');
  const dropdownMenu = document.getElementById('team-dropdown-menu');
  const allFilterBtn = document.getElementById('all-filter-btn');

  if (!dropdownTrigger || !dropdownMenu || !allFilterBtn) return;

  // Handle All button click
  allFilterBtn.addEventListener('click', handleAllFilterClick);

  // Handle dropdown trigger click
  dropdownTrigger.addEventListener('click', (e) => {
    e.stopPropagation();
    toggleDropdown();
  });

  // Close dropdown when clicking outside
  document.addEventListener('click', (e) => {
    if (!dropdownTrigger.contains(e.target) && !dropdownMenu.contains(e.target)) {
      closeDropdown();
    }
  });
}

// Toggle dropdown visibility
function toggleDropdown() {
  const dropdownTrigger = document.getElementById('team-dropdown-btn');
  const dropdownMenu = document.getElementById('team-dropdown-menu');

  if (dropdownMenu.classList.contains('show')) {
    closeDropdown();
  } else {
    openDropdown();
  }
}

// Open dropdown
function openDropdown() {
  const dropdownTrigger = document.getElementById('team-dropdown-btn');
  const dropdownMenu = document.getElementById('team-dropdown-menu');

  dropdownTrigger.classList.add('open');
  dropdownMenu.classList.add('show');
}

// Close dropdown
function closeDropdown() {
  const dropdownTrigger = document.getElementById('team-dropdown-btn');
  const dropdownMenu = document.getElementById('team-dropdown-menu');

  dropdownTrigger.classList.remove('open');
  dropdownMenu.classList.remove('show');
}

// Handle All filter button click
function handleAllFilterClick(e) {
  // Reset dropdown selection
  const selectedTeamText = document.getElementById('selected-team-text');
  const dropdownTrigger = document.getElementById('team-dropdown-btn');
  const allOptions = document.querySelectorAll('.team-dropdown-option');

  selectedTeamText.textContent = 'Team Members';
  dropdownTrigger.classList.remove('active');

  // Remove active class from all dropdown options
  allOptions.forEach(option => option.classList.remove('active'));

  // Set All button as active
  e.target.classList.add('active');

  // Update filter
  currentFilter = 'all';
  applyFilters();
  closeDropdown();
}

// Handle team member selection from dropdown
function handleTeamMemberSelect(e) {
  const selectedMemberId = e.target.getAttribute('data-filter');
  const selectedMemberName = e.target.textContent;
  const selectedTeamText = document.getElementById('selected-team-text');
  const dropdownTrigger = document.getElementById('team-dropdown-btn');
  const allFilterBtn = document.getElementById('all-filter-btn');
  const allOptions = document.querySelectorAll('.team-dropdown-option');

  // Update dropdown display
  selectedTeamText.textContent = selectedMemberName;
  dropdownTrigger.classList.add('active');

  // Remove active class from All button
  allFilterBtn.classList.remove('active');

  // Remove active class from all dropdown options
  allOptions.forEach(option => option.classList.remove('active'));

  // Add active class to selected option
  e.target.classList.add('active');

  // Update filter
  currentFilter = selectedMemberId;
  applyFilters();
  closeDropdown();
}

// Render pages in the sidebar
function renderPages() {
  // Clear the list
  pagesList.innerHTML = '';
  
  if (pages.length === 0) {
    pagesList.innerHTML = '<div class="no-pages">No pages added yet</div>';
    return;
  }
  
  // Create and append page items
  pages.forEach((page, index) => {
    const pageItemElement = document.importNode(pageItemTemplate.content, true).querySelector('.page-item');
    
    // Set page data
    pageItemElement.querySelector('.page-name').textContent = page.name || 'Unnamed Page';
    pageItemElement.querySelector('.page-url').textContent = page.link;
    
    // Set delete button action
    const deleteBtn = pageItemElement.querySelector('.delete-page');
    deleteBtn.dataset.index = index;
    deleteBtn.addEventListener('click', handleDeletePage);
    
    pagesList.appendChild(pageItemElement);
  });
}

// Add a new page
function addPage() {
  const name = pageNameInput.value.trim();
  const url = pageUrlInput.value.trim();
  
  if (!url) {
    showNotification('Please enter a valid URL', 'warning');
    return;
  }
  
  // Validate URL format
  let formattedUrl;
  try {
    // Add http:// if missing
    formattedUrl = url.startsWith('http') ? url : `https://${url}`;
    new URL(formattedUrl); // This will throw if URL is invalid
  } catch (error) {
    showNotification('Please enter a valid URL', 'warning');
    return;
  }
  
  // Check if URL is a Facebook URL
  if (!formattedUrl.includes('facebook.com') && !formattedUrl.includes('fb.com')) {
    showNotification('URL should be a Facebook page', 'warning');
    // Continue anyway, just warn the user
  }
  
  const newPage = {
    name: name || extractDomainFromUrl(formattedUrl),
    link: formattedUrl
  };
  
  // Show loading indicator
  addPageBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
  addPageBtn.disabled = true;
  
  fetch('/api/pages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(newPage)
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(result => {
      if (result.success) {
        pages.push(newPage);
        renderPages();
        pageNameInput.value = '';
        pageUrlInput.value = '';
        showNotification('Page added successfully', 'success');
      } else {
        showNotification('Failed to add page: ' + (result.error || 'Unknown error'), 'error');
      }
    })
    .catch(error => {
      console.error('Error adding page:', error);
      showNotification('Failed to add page: ' + error.message, 'error');
    })
    .finally(() => {
      // Reset button state
      addPageBtn.innerHTML = '<i class="fas fa-plus"></i> Add Page';
      addPageBtn.disabled = false;
    });
}

// Helper function to extract domain from URL for page name
function extractDomainFromUrl(url) {
  try {
    const domain = new URL(url).hostname
      .replace('www.', '')
      .replace('.com', '')
      .replace('.net', '')
      .replace('.org', '');
    return domain.charAt(0).toUpperCase() + domain.slice(1);
  } catch (e) {
    return 'Facebook Page';
  }
}

// Delete a page
function handleDeletePage(e) {
  const index = parseInt(e.currentTarget.dataset.index);
  
  if (isNaN(index) || index < 0 || index >= pages.length) {
    showNotification('Invalid page index', 'error');
    return;
  }
  
  const page = pages[index];
  
  if (confirm(`Are you sure you want to delete "${page.name || 'this page'}"?`)) {
    // Get the button and add loading state
    const deleteBtn = e.currentTarget;
    const originalContent = deleteBtn.innerHTML;
    deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    deleteBtn.disabled = true;
    
    fetch(`/api/pages/${index}`, {
      method: 'DELETE'
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}`);
        }
        return response.json();
      })
      .then(result => {
        if (result.success) {
          pages.splice(index, 1);
          renderPages();
          showNotification('Page deleted successfully', 'success');
        } else {
          showNotification('Failed to delete page: ' + (result.error || 'Unknown error'), 'error');
          // Reset button state
          deleteBtn.innerHTML = originalContent;
          deleteBtn.disabled = false;
        }
      })
      .catch(error => {
        console.error('Error deleting page:', error);
        showNotification('Failed to delete page: ' + error.message, 'error');
        // Reset button state
        deleteBtn.innerHTML = originalContent;
        deleteBtn.disabled = false;
      });
  }
}

function addMinimizeButton() {
  const headerControls = document.querySelector('.header-controls');
  
  // Create minimize button
  const minimizeBtn = document.createElement('button');
  minimizeBtn.className = 'btn minimize';
  minimizeBtn.innerHTML = '<i class="fas fa-minus"></i>';
  minimizeBtn.title = 'Minimize to tray';
  
  // Add style
  const style = document.createElement('style');
  style.textContent = `
    .btn.minimize {
      background-color: transparent;
      color: var(--light-text);
      padding: 0.5rem;
      margin-left: 0.5rem;
    }
    .dark-mode .btn.minimize {
      color: var(--dark-light-text);
    }
    .btn.minimize:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
    .dark-mode .btn.minimize:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  `;
  document.head.appendChild(style);
  
  // Add click event
  minimizeBtn.addEventListener('click', () => {
    window.ipcRenderer.send('minimize-to-tray');
  });
  
  // Add to header
  headerControls.appendChild(minimizeBtn);
}

// Event Listeners
searchInput.addEventListener('input', handleSearch);
// Note: Filter button event listeners are now added dynamically in renderFilterButtons()
themeToggle.addEventListener('click', toggleTheme);
startScraperBtn.addEventListener('click', startScraper);
stopScraperBtn.addEventListener('click', stopScraper);
pauseScraperBtn.addEventListener('click', pauseScraper);
resumeScraperBtn.addEventListener('click', resumeScraper);

// Admin sidebar event listeners
adminToggleBtn.addEventListener('click', openSidebar);
sidebarCloseBtn.addEventListener('click', closeSidebar);
saveConfigBtn.addEventListener('click', saveConfiguration);
addPageBtn.addEventListener('click', addPage);

// Facebook authentication event listeners
loginButton.addEventListener('click', startFacebookLogin);
saveSessionButton.addEventListener('click', saveFacebookSession);
clearSessionButton.addEventListener('click', clearFacebookSession);

// New event listener for navigation
navigateBtn.addEventListener('click', navigateToBrowserUrl);

// Bulk metrics update event listeners
const updateAllMetricsBtn = document.getElementById('update-all-metrics');
const stopMetricsUpdateBtn = document.getElementById('stop-metrics-update');
const openBrowserBtn = document.getElementById('open-browser');
const exportTeamBtn = document.getElementById('export-team-posts');

if (updateAllMetricsBtn) {
  updateAllMetricsBtn.addEventListener('click', startBulkMetricsUpdate);
}

if (stopMetricsUpdateBtn) {
  stopMetricsUpdateBtn.addEventListener('click', stopBulkMetricsUpdate);
}

if (exportTeamBtn) {
  exportTeamBtn.addEventListener('click', () => exportTeamPosts('json'));
}

if (openBrowserBtn) {
  openBrowserBtn.addEventListener('click', openBrowser);
}

// Export team posts button is now a direct action button

// Bulk metrics update functions
async function startBulkMetricsUpdate() {
  // Show the date filter modal instead of directly starting the update
  showDateFilterModal();
}

async function stopBulkMetricsUpdate() {
  try {
    const response = await fetch('/api/posts/stop-all-metrics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();

    if (result.success) {
      showNotification('Bulk metrics update stopped', 'info');
      resetBulkMetricsButtons();
    } else {
      showNotification('Failed to stop bulk metrics update: ' + result.message, 'error');
    }
  } catch (error) {
    console.error('Error stopping bulk metrics update:', error);
    showNotification('Error stopping bulk metrics update: ' + error.message, 'error');
  }
}

async function checkBulkMetricsStatus() {
  try {
    const response = await fetch('/api/posts/bulk-metrics-status');
    const status = await response.json();

    if (!status.isRunning) {
      // Update completed - Socket events will handle UI updates
      resetBulkMetricsButtons();
    } else {
      // Still running, check again in 30 seconds (less frequent since we have real-time updates)
      setTimeout(checkBulkMetricsStatus, 30000);
    }
  } catch (error) {
    console.error('Error checking bulk metrics status:', error);
    // Reset buttons on error
    resetBulkMetricsButtons();
  }
}

function resetBulkMetricsButtons() {
  console.log('🔄 Resetting bulk metrics buttons and hiding progress');

  updateAllMetricsBtn.disabled = false;
  updateAllMetricsBtn.innerHTML = '<i class="fas fa-chart-line"></i> Update All Metrics';
  stopMetricsUpdateBtn.style.display = 'none';
  stopMetricsUpdateBtn.disabled = true;

  // Hide specific bulk metrics operation
  hideUnifiedProgressBar(window.bulkMetricsOperationId || 'bulk-metrics');
  delete window.bulkMetricsOperationId;
}

// Multiple operations progress system
window.activeOperations = new Map();

// Clear all progress bars on page load
function clearAllProgressBars() {
  const progressContainer = document.getElementById('progress-container');
  const operationsList = document.getElementById('progress-operations-list');

  if (operationsList) {
    operationsList.innerHTML = '';
  }

  if (progressContainer) {
    progressContainer.classList.add('hidden');
  }

  window.activeOperations.clear();
  delete window.bulkMetricsOperationId;
  delete window.scraperOperationId;

  console.log('🧹 All progress bars cleared');
}

function showUnifiedProgressBar(options) {
  const {
    operation = 'Processing',
    total = 0,
    unit = 'items',
    operationId = null
  } = options;

  const progressContainer = document.getElementById('progress-container');
  const operationsList = document.getElementById('progress-operations-list');

  console.log('🎯 Showing progress bar for:', operation, 'ID:', operationId);

  if (!progressContainer || !operationsList) {
    console.error('❌ Progress container not found');
    return;
  }

  // Generate unique ID if not provided
  const id = operationId || `operation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  // Check if operation already exists - update instead of creating new
  if (window.activeOperations.has(id)) {
    console.log('🔄 Operation already exists, updating:', id);
    const existingData = window.activeOperations.get(id);
    existingData.operation = operation;
    existingData.total = total;
    existingData.unit = unit;

    // Update the existing element
    const existingElement = existingData.element;
    const operationSpan = existingElement.querySelector('.progress-operation');
    const statsSpan = existingElement.querySelector('.progress-stats');

    if (operationSpan) operationSpan.textContent = operation;
    if (statsSpan) statsSpan.textContent = `0 / ${total} ${unit}`;

    // Show container
    progressContainer.classList.remove('hidden');

    console.log('✅ Progress bar updated for:', operation, 'ID:', id);
    return id;
  }

  // Create new operation item
  const operationItem = document.createElement('div');
  operationItem.className = 'progress-operation-item';
  operationItem.id = `progress-${id}`;

  operationItem.innerHTML = `
    <div class="progress-info">
      <span class="progress-operation">${operation}</span>
      <span class="progress-stats">0 / ${total} ${unit}</span>
    </div>
    <div class="progress-status" style="display: none; font-size: 0.85em; color: #888; margin: 2px 0;"></div>
    <div class="progress-bar">
      <div class="progress-fill" style="width: 0%"></div>
    </div>
    <span class="progress-text">0%</span>
  `;

  // Add to operations list
  operationsList.appendChild(operationItem);

  // Show container
  progressContainer.classList.remove('hidden');

  // Store operation data
  window.activeOperations.set(id, {
    operation,
    total,
    unit,
    element: operationItem
  });

  console.log('✅ Progress bar created for:', operation, 'ID:', id);
  return id;
}

// Force show progress bar (ensures it always works)
function forceShowProgressBar(options) {
  console.log('🔥 Force showing progress bar:', options);

  // Don't hide all operations, just add new one
  return showUnifiedProgressBar(options);
}

// Legacy function for backward compatibility
function showBulkProgressBar(totalPosts, estimatedTime) {
  forceShowProgressBar({
    operation: 'Updating Metrics',
    total: totalPosts,
    unit: 'posts'
  });
}

// Update specific operation progress
function updateUnifiedProgressBar(current, total, status = null, operationId = null) {
  // If no operationId provided, try to find the most recent operation
  if (!operationId && window.activeOperations.size > 0) {
    const operations = Array.from(window.activeOperations.keys());
    operationId = operations[operations.length - 1];
  }

  if (!operationId || !window.activeOperations.has(operationId)) {
    console.warn('⚠️ Operation not found for update:', operationId);
    return;
  }

  const operationData = window.activeOperations.get(operationId);
  const operationElement = operationData.element;

  if (!operationElement) return;

  const progressStats = operationElement.querySelector('.progress-stats');
  const progressFill = operationElement.querySelector('.progress-fill');
  const progressPercentage = operationElement.querySelector('.progress-text');
  const progressStatus = operationElement.querySelector('.progress-status');

  const percentage = Math.round((current / total) * 100);

  // Update progress elements
  if (progressStats) progressStats.textContent = `${current} / ${total} ${operationData.unit}`;
  if (progressFill) progressFill.style.width = `${percentage}%`;
  if (progressPercentage) progressPercentage.textContent = `${percentage}%`;

  // Update status text if provided
  if (progressStatus) {
    if (status) {
      progressStatus.textContent = status;
      progressStatus.style.display = 'block';
    } else {
      progressStatus.style.display = 'none';
    }
  }

  console.log(`📊 Updated progress for ${operationData.operation}: ${percentage}%${status ? ` (${status})` : ''}`);

  // Also update the legacy progress function for compatibility
  updateProgress(current, total);
}

// Legacy function for backward compatibility
function updateBulkProgressBar(current, total, status = null) {
  updateUnifiedProgressBar(current, total, status);
}

// Hide specific operation or all operations
function hideUnifiedProgressBar(operationId = null) {
  const progressContainer = document.getElementById('progress-container');
  const operationsList = document.getElementById('progress-operations-list');

  if (operationId) {
    // Hide specific operation
    console.log('🔄 Hiding specific operation:', operationId);

    if (window.activeOperations.has(operationId)) {
      const operationData = window.activeOperations.get(operationId);
      if (operationData.element) {
        operationData.element.remove();
      }
      window.activeOperations.delete(operationId);
      console.log('✅ Operation removed:', operationId);
    }

    // Hide container if no operations left
    if (window.activeOperations.size === 0 && progressContainer) {
      progressContainer.classList.add('hidden');
      console.log('✅ All operations completed - container hidden');
    }
  } else {
    // Hide all operations
    console.log('🔄 Hiding all progress operations');

    if (operationsList) {
      operationsList.innerHTML = '';
    }

    if (progressContainer) {
      progressContainer.classList.add('hidden');
    }

    // Clear all active operations
    window.activeOperations.clear();

    console.log('✅ All progress bars hidden and reset');
  }
}

// Legacy function for backward compatibility
function hideBulkProgressBar() {
  hideUnifiedProgressBar();
}

// Open Browser Function
async function openBrowser() {
  console.log('Opening browser for inspection...');
  openBrowserBtn.disabled = true;
  openBrowserBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Opening Browser...';

  try {
    const response = await fetch('/api/browser/open', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = await response.json();

    if (data.success) {
      showNotification('Browser opened successfully! You can now inspect Facebook pages.', 'success');
    } else {
      showNotification(data.message || 'Failed to open browser', 'error');
    }
  } catch (error) {
    console.error('Error opening browser:', error);
    showNotification('Error opening browser: ' + error.message, 'error');
  } finally {
    openBrowserBtn.disabled = false;
    openBrowserBtn.innerHTML = '<i class="fas fa-globe"></i> Open Browser';
  }
}

// Function to update post metrics in the UI in real-time
function updatePostMetricsInUI(postId, metrics) {
  // Find the post card in the DOM
  const postCard = document.querySelector(`[data-post-id="${postId}"]`);
  if (!postCard) {
    console.log(`Post card not found for ID: ${postId}`);
    return;
  }

  // Find the engagement section
  let engagementSection = postCard.querySelector('.post-engagement');

  // If no engagement section exists, create one
  if (!engagementSection) {
    engagementSection = document.createElement('div');
    engagementSection.className = 'post-engagement';

    // Insert before the post footer
    const postFooter = postCard.querySelector('.post-footer');
    if (postFooter) {
      postCard.insertBefore(engagementSection, postFooter);
    } else {
      postCard.appendChild(engagementSection);
    }
  }

  // Create engagement HTML
  const engagementItems = [];

  if (metrics.likes > 0) {
    engagementItems.push(`<span class="engagement-item likes"><i class="far fa-thumbs-up"></i> ${formatNumber(metrics.likes)}</span>`);
  }

  if (metrics.comments > 0) {
    engagementItems.push(`<span class="engagement-item comments"><i class="far fa-comment"></i> ${formatNumber(metrics.comments)}</span>`);
  }

  if (metrics.shares > 0) {
    engagementItems.push(`<span class="engagement-item shares"><i class="far fa-share-square"></i> ${formatNumber(metrics.shares)}</span>`);
  }

  // Update the engagement section
  if (engagementItems.length > 0) {
    engagementSection.innerHTML = engagementItems.join('');
    engagementSection.style.display = 'block';
  } else {
    engagementSection.style.display = 'none';
  }

  // Also update the post in the posts array for consistency
  const postIndex = posts.findIndex(post => post.normalizedTextHash === postId);
  if (postIndex !== -1) {
    if (!posts[postIndex].engagement) {
      posts[postIndex].engagement = {};
    }
    posts[postIndex].engagement = { ...posts[postIndex].engagement, ...metrics };
    posts[postIndex].lastMetricsUpdate = new Date().toISOString();
  }

  console.log(`Updated UI for post ${postId}:`, metrics);
}

// Export team posts function - exports all team members individually
async function exportTeamPosts(format = 'json') {
  const formatName = 'JSON'; // Only JSON export now

  // Get the export button
  const clickedBtn = exportTeamBtn;
  const originalText = clickedBtn.innerHTML;

  try {
    // Show loading state
    clickedBtn.disabled = true;
    clickedBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Exporting All Members...';

    // First, get all team members
    const teamResponse = await fetch('/api/team');
    if (!teamResponse.ok) {
      throw new Error('Failed to fetch team members');
    }

    const teamMembers = await teamResponse.json();

    if (!teamMembers || teamMembers.length === 0) {
      showNotification('No team members found to export', 'warning');
      return;
    }

    // Show progress notification and log team members
    console.log('Team members to export:', teamMembers.map(m => ({ id: m.id, name: m.name })));
    showNotification(`Starting export for ${teamMembers.length} team members...`, 'info');

    // Export each member individually
    const exportResults = [];
    const errors = [];

    for (let i = 0; i < teamMembers.length; i++) {
      const member = teamMembers[i];

      // Update button text with progress
      clickedBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> Exporting ${member.name} (${i + 1}/${teamMembers.length})`;

      try {
        // Call the individual member export endpoint
        const exportResponse = await fetch(`/api/team/export-member/${member.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ format: format })
        });

        const exportResult = await exportResponse.json();

        console.log(`Export result for ${member.name}:`, exportResult);

        if (exportResult.success) {
          exportResults.push({
            memberName: member.name,
            fileName: exportResult.fileName,
            postsCount: exportResult.postsCount,
            fileSize: exportResult.fileSize,
            success: true
          });

          // Show progress notification
          showNotification(`✅ Exported ${member.name}: ${exportResult.postsCount} posts (${i + 1}/${teamMembers.length})`, 'success');
        } else {
          const errorMsg = exportResult.error || exportResult.message || 'Unknown error';
          console.warn(`Export failed for ${member.name}:`, errorMsg);
          errors.push(`${member.name}: ${errorMsg}`);
          exportResults.push({
            memberName: member.name,
            success: false,
            error: errorMsg,
            postsCount: exportResult.postsCount || 0
          });

          // Show warning notification for members with no posts
          if (exportResult.postsCount === 0) {
            showNotification(`⚠️ ${member.name}: No posts found (${i + 1}/${teamMembers.length})`, 'warning');
          } else {
            showNotification(`❌ Failed to export ${member.name} (${i + 1}/${teamMembers.length})`, 'error');
          }
        }
      } catch (memberError) {
        console.error(`Error exporting ${member.name}:`, memberError);
        errors.push(`${member.name}: ${memberError.message}`);
        exportResults.push({
          memberName: member.name,
          success: false,
          error: memberError.message
        });
      }

      // Small delay between exports to avoid overwhelming the server
      if (i < teamMembers.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Show final results
    const successCount = exportResults.filter(r => r.success).length;
    const errorCount = errors.length;

    let message = `📊 TEAM EXPORT COMPLETED (${formatName}) 📊\n\n`;
    message += `✅ Successfully exported: ${successCount} members\n`;
    if (errorCount > 0) {
      message += `❌ Failed exports: ${errorCount} members\n`;
    }
    message += `\nExport Details:\n${'='.repeat(40)}\n`;

    // Add successful exports
    exportResults.filter(r => r.success).forEach(result => {
      message += `✅ ${result.memberName}\n`;
      message += `   Posts: ${result.postsCount || 0}\n`;
      message += `   File: ${result.fileName}\n`;
      message += `   Size: ${result.fileSize}\n\n`;
    });

    // Add failed exports
    if (errorCount > 0) {
      message += `\nFailed Exports:\n${'='.repeat(20)}\n`;
      exportResults.filter(r => !r.success).forEach(result => {
        message += `❌ ${result.memberName}: ${result.error}\n`;
      });
    }

    message += `\nFiles saved to: exports folder`;

    // Show success notification
    if (errorCount === 0) {
      showNotification(`🎉 All ${successCount} team members exported successfully as ${formatName}!`, 'success');
    } else {
      showNotification(`⚠️ Export completed: ${successCount} successful, ${errorCount} failed`, 'warning');
    }

    // Show detailed info in console
    console.log(message);

    // Show alert with summary
    alert(`${errorCount === 0 ? '✅' : '⚠️'} Team Export Completed!\n\n${message}`);

  } catch (error) {
    console.error(`Error during team export:`, error);
    showNotification(`Error during team export: ${error.message}`, 'error');
  } finally {
    // Reset button state
    clickedBtn.disabled = false;
    clickedBtn.innerHTML = originalText;
  }
}

// Socket event listeners
socket.on('connect', () => {
  console.log('Connected to server');
});

socket.on('newPost', (post) => {
  // Add new post to the beginning of the array
  posts.unshift(post);
  
  // Re-apply current filters
  applyFilters();
  
  // Show notification
  showNotification('New post received!');
  
  // Show desktop notification if in Electron
  if (window.ipcRenderer) {
    const notification = new Notification('New Post Received', {
      body: post.text || post.normalizedTextHash,
      icon: '/favicon.ico'
    });
    
    notification.onclick = () => {
      if (window.ipcRenderer) {
        window.ipcRenderer.send('show-window');
      }
    };
  }
});

socket.on('scraperStatus', (data) => {
  console.log('📡 Scraper status received:', data);

  // Update scraper status first (but it won't interfere with progress now)
  updateScraperStatus(data.isRunning, data.isPaused, data.browserOpen, data.error);

  // Show progress bar immediately when scraper starts
  if (data.isRunning && !data.isPaused) {
    console.log('🚀 Scraper started via socket - showing progress bar');

    // Force show progress bar immediately with consistent ID
    window.scraperOperationId = 'scraper';
    forceShowProgressBar({
      operation: 'Scraping Pages',
      total: data.totalPages || 50, // Use provided total or estimate
      unit: 'pages',
      operationId: window.scraperOperationId
    });

    // Update with actual total if available
    if (!data.totalPages) {
      fetch('/api/status')
        .then(response => response.json())
        .then(statusData => {
          if (statusData.totalPages && window.activeOperations.has('scraper')) {
            const operationData = window.activeOperations.get('scraper');
            operationData.total = statusData.totalPages;
            const progressStats = operationData.element.querySelector('.progress-stats');
            if (progressStats) {
              progressStats.textContent = `0 / ${statusData.totalPages} pages`;
            }
          }
        })
        .catch(err => console.log('Could not get exact total pages'));
    }
  } else if (!data.isRunning) {
    console.log('🛑 Scraper stopped via socket');
    // Don't hide here - let updateScraperStatus handle it
  }
});

socket.on('scraperProgress', (data) => {
  updateProgress(data.completed, data.total);

  // Create enhanced status text with posts found, ETA, and cycle info
  let statusText = null;
  if (data.postsFound !== undefined) {
    statusText = `${data.postsFound} posts found`;

    // Add cycle information (just show current cycle number)
    if (data.currentCycle !== undefined) {
      statusText += ` • Cycle #${data.currentCycle}`;
    }

    if (data.estimatedTimeRemaining !== null && data.estimatedTimeRemaining !== undefined) {
      if (data.estimatedTimeRemaining > 0) {
        const etaText = data.estimatedTimeRemaining === 1 ? '1m' : `${data.estimatedTimeRemaining}m`;
        statusText += ` • ETA: ${etaText}`;
      } else {
        statusText += ' • Almost done';
      }
    }
  }

  // Update scraper operation progress with enhanced status
  updateUnifiedProgressBar(data.completed, data.total, statusText, window.scraperOperationId || 'scraper');
});

socket.on('nextCycle', (data) => {
  updateNextCycleTime(data.nextCycleTime, data.delayMinutes);
});

socket.on('scraperError', (data) => {
  showNotification(`Error: ${data.error}`, 'error');
  setStatusError(data.error);
});

// Bulk metrics update socket events
socket.on('bulkMetricsStarted', (data) => {
  console.log('📡 Bulk metrics started via socket - updating existing progress bar');

  // Update button states
  if (updateAllMetricsBtn) {
    updateAllMetricsBtn.disabled = true;
    updateAllMetricsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
  }
  if (stopMetricsUpdateBtn) {
    stopMetricsUpdateBtn.style.display = 'inline-block';
    stopMetricsUpdateBtn.disabled = false;
  }

  // Update existing progress bar instead of creating new one
  if (window.activeOperations.has('bulk-metrics')) {
    const operationData = window.activeOperations.get('bulk-metrics');
    operationData.operation = 'Updating Metrics';
    operationData.total = data.totalPosts;
    operationData.unit = 'posts';

    // Update the display
    const operationElement = operationData.element;
    const operationSpan = operationElement.querySelector('.progress-operation');
    const statsSpan = operationElement.querySelector('.progress-stats');

    if (operationSpan) operationSpan.textContent = 'Updating Metrics';
    if (statsSpan) statsSpan.textContent = `0 / ${data.totalPosts} posts`;

    console.log('✅ Updated existing bulk metrics progress bar');
  } else {
    console.log('⚠️ No existing bulk metrics operation found, creating new one');
    window.bulkMetricsOperationId = 'bulk-metrics';
    showUnifiedProgressBar({
      operation: 'Updating Metrics',
      total: data.totalPosts,
      unit: 'posts',
      operationId: window.bulkMetricsOperationId
    });
  }
});

socket.on('bulkMetricsProgress', (data) => {
  // Update the specific post's metrics in real-time
  updatePostMetricsInUI(data.postId, data.metrics);

  // Create enhanced status text with ETA for bulk metrics
  let statusText = null;
  if (data.estimatedTimeRemaining !== null && data.estimatedTimeRemaining !== undefined) {
    if (data.estimatedTimeRemaining > 0) {
      const etaText = data.estimatedTimeRemaining === 1 ? '1m' : `${data.estimatedTimeRemaining}m`;
      statusText = `ETA: ${etaText}`;
    } else {
      statusText = 'Almost done';
    }
  }

  // Update progress bar for bulk metrics operation with enhanced status
  updateUnifiedProgressBar(data.progress.current, data.progress.total, statusText, window.bulkMetricsOperationId || 'bulk-metrics');

  // Show progress notification every 20% (less frequent)
  if (data.progress.percentage % 20 === 0) {
    showNotification(`Progress: ${data.progress.current}/${data.progress.total} posts updated (${data.progress.percentage}%)`, 'info');
  }

  console.log(`Bulk update progress: ${data.progress.current}/${data.progress.total} (${data.progress.percentage}%)${statusText ? ` (${statusText})` : ''}`);
});

socket.on('bulkMetricsCompleted', (data) => {
  // Final progress update
  updateUnifiedProgressBar(data.updatedCount + data.errorCount, data.updatedCount + data.errorCount, 'Update completed!', window.bulkMetricsOperationId || 'bulk-metrics');

  // Show completion notification
  showNotification(`Bulk metrics update completed! Updated ${data.updatedCount} posts${data.errorCount > 0 ? `, ${data.errorCount} errors` : ''}`, 'success');

  // Reset buttons and hide progress after a short delay
  setTimeout(() => {
    resetBulkMetricsButtons();
  }, 2000);

  // Refresh all posts to ensure consistency
  fetchPosts();
});

socket.on('bulkMetricsStopped', (data) => {
  showNotification(data.message, 'info');

  // Reset buttons and hide progress after a short delay
  setTimeout(() => {
    resetBulkMetricsButtons();
  }, 1500);
});

// Member scraping progress events
socket.on('memberScrapingStarted', (data) => {
  console.log('Member scraping started:', data);
  showUnifiedProgressBar({
    operation: `Scraping ${data.memberName}`,
    total: data.totalPages,
    unit: 'pages'
  });
  showNotification(`Started scraping for ${data.memberName} (${data.totalPages} pages)`, 'info');
});

socket.on('memberScrapingProgress', (data) => {
  console.log('Member scraping progress:', data);

  // Create enhanced status text with posts found and ETA
  let statusText = `Scraping page ${data.completed} of ${data.total} for ${data.memberName}`;

  if (data.postsFound !== undefined) {
    statusText += ` • ${data.postsFound} posts found`;

    if (data.estimatedTimeRemaining !== null && data.estimatedTimeRemaining !== undefined) {
      if (data.estimatedTimeRemaining > 0) {
        const etaText = data.estimatedTimeRemaining === 1 ? '1m' : `${data.estimatedTimeRemaining}m`;
        statusText += ` • ETA: ${etaText}`;
      } else {
        statusText += ' • Almost done';
      }
    }
  }

  updateUnifiedProgressBar(data.completed, data.total, statusText);

  // Show notification every 25% for member scraping
  if (data.percentage % 25 === 0) {
    showNotification(`Scraping ${data.memberName}: ${data.completed}/${data.total} pages (${data.percentage}%)`, 'info');
  }
});

socket.on('memberScrapingCompleted', (data) => {
  console.log('Member scraping completed:', data);

  // Final progress update
  updateUnifiedProgressBar(data.totalPages, data.totalPages, 'Scraping completed!');

  showNotification(`Completed scraping for ${data.memberName}. Processed ${data.pagesProcessed}/${data.totalPages} pages.`, 'success');

  // Hide progress after a short delay
  setTimeout(() => {
    hideUnifiedProgressBar();
  }, 2000);

  // Refresh posts
  fetchPosts();
});

// Multiple posts scraping events
socket.on('memberMultipleScrapingStarted', (data) => {
  console.log('Multiple posts scraping started:', data);
  showUnifiedProgressBar({
    operation: `Collecting Posts - ${data.memberName}`,
    total: data.totalPages,
    unit: 'pages'
  });
  showNotification(`Started collecting posts from ${data.totalPages} pages for ${data.memberName}`, 'info');
});

socket.on('memberMultipleScrapingProgress', (data) => {
  console.log('Multiple posts scraping progress:', data);

  // Create enhanced status text with posts found and ETA
  let statusText = `Collecting from page ${data.completed} of ${data.total} - ${data.postsCollected} posts found`;

  if (data.estimatedTimeRemaining !== null && data.estimatedTimeRemaining !== undefined) {
    if (data.estimatedTimeRemaining > 0) {
      const etaText = data.estimatedTimeRemaining === 1 ? '1m' : `${data.estimatedTimeRemaining}m`;
      statusText += ` • ETA: ${etaText}`;
    } else {
      statusText += ' • Almost done';
    }
  }

  updateUnifiedProgressBar(data.completed, data.total, statusText);

  // Show notification every 33% for multiple scraping
  if (data.percentage % 33 === 0) {
    showNotification(`Progress: ${data.completed}/${data.total} pages, ${data.postsCollected} posts collected (${data.percentage}%)`, 'info');
  }
});

socket.on('memberMultipleScrapingCompleted', (data) => {
  console.log('Multiple posts scraping completed:', data);

  // Final progress update
  updateUnifiedProgressBar(data.totalPages, data.totalPages, `Collected ${data.postsCollected} posts!`);

  showNotification(`Completed collecting posts for ${data.memberName}. Found ${data.postsCollected} posts from ${data.pagesProcessed} pages.`, 'success');

  // Hide progress after a short delay
  setTimeout(() => {
    hideUnifiedProgressBar();
  }, 2000);

  // Refresh posts
  fetchPosts();
});

socket.on('bulkMetricsError', (data) => {
  showNotification(`Bulk metrics update error: ${data.error}`, 'error');
  resetBulkMetricsButtons();
});

// Auto-export socket events
socket.on('autoExportCompleted', (data) => {
  console.log('Auto-export completed:', data);

  if (data.success) {
    let message = 'Auto-export completed successfully!';

    if (data.csv && data.json) {
      message += ` Exported ${data.csv.length} CSV files and ${data.json.length} JSON files.`;
    } else if (data.csv) {
      message += ` Exported ${data.csv.length} CSV files.`;
    } else if (data.json) {
      message += ` Exported ${data.json.length} JSON files.`;
    }

    showNotification(message, 'success');
  } else {
    showNotification(`Auto-export completed with errors: ${data.errors.join(', ')}`, 'warning');
  }

  // Reload auto-export config to update next export time
  loadAutoExportConfig();
});

socket.on('autoExportError', (data) => {
  console.error('Auto-export error:', data);
  showNotification(`Auto-export failed: ${data.errors.join(', ')}`, 'error');
});

socket.on('autoExportStarted', (data) => {
  console.log('Auto-export started:', data);
  const message = data.source === 'manual' ?
    '🚀 Manual auto-export started...' :
    '🔄 Auto-export started...';
  showNotification(message, 'info', {
    timestamp: data.timestamp,
    source: data.source
  });
});

// Scheduled auto-export socket events
socket.on('scheduledAutoExportCompleted', (data) => {
  console.log('Scheduled auto-export completed:', data);

  if (data.success) {
    let message = '🕒 Scheduled auto-export completed successfully!';

    if (data.json && data.json.length > 0) {
      const successfulExports = data.json.filter(item => item.success !== false).length;
      message += ` Exported ${successfulExports} team member${successfulExports !== 1 ? 's' : ''}.`;
    }

    showNotification(message, 'success', {
      isScheduled: true,
      timestamp: data.timestamp,
      exportCount: data.json ? data.json.length : 0
    });
  } else {
    const errorMessage = `🕒 Scheduled auto-export completed with errors: ${data.errors.join(', ')}`;
    showNotification(errorMessage, 'warning', {
      isScheduled: true,
      timestamp: data.timestamp,
      errors: data.errors
    });
  }

  // Reload auto-export config to update next export time
  loadAutoExportConfig();
});

socket.on('scheduledAutoExportError', (data) => {
  console.error('Scheduled auto-export error:', data);
  showNotification(`🕒 Scheduled auto-export failed: ${data.errors.join(', ')}`, 'error', {
    isScheduled: true,
    timestamp: data.timestamp,
    errors: data.errors
  });

  // Reload auto-export config to update next export time
  loadAutoExportConfig();
});

socket.on('scheduledAutoExportStarted', (data) => {
  console.log('Scheduled auto-export started:', data);
  showNotification(`🕒 Scheduled auto-export started (${data.frequency} at ${data.time})`, 'info', {
    isScheduled: true,
    timestamp: data.timestamp,
    frequency: data.frequency,
    time: data.time
  });
});

// Scraper Control Functions
function startScraper() {
  console.log('🚀 Starting scraper - showing progress immediately');

  // Force show progress bar immediately with consistent ID
  window.scraperOperationId = 'scraper';
  forceShowProgressBar({
    operation: 'Starting Scraper...',
    total: 1,
    unit: 'initializing',
    operationId: window.scraperOperationId
  });

  socket.emit('startScraper');
  startScraperBtn.disabled = true;
  statusText.textContent = 'Starting scraper...';
}

function stopScraper() {
  socket.emit('stopScraper');
  stopScraperBtn.disabled = true;
  statusText.textContent = 'Stopping scraper...';
}

// Functions for pausing and resuming scraping
function pauseScraper() {
  console.log('Pausing scraper...');
  pauseScraperBtn.disabled = true;
  statusText.textContent = 'Pausing scraper...';
  
  fetch('/api/scraper/pause', {
    method: 'POST'
  })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Update UI to show paused state
        statusDot.className = 'status-dot paused';
        statusText.textContent = 'Scraper paused (browser open for inspection)';
        startScraperBtn.disabled = true;
        stopScraperBtn.disabled = false;
        pauseScraperBtn.disabled = true;
        resumeScraperBtn.disabled = false;
        
        // Show the navigation input
        navigationControl.classList.remove('hidden');
        // Hide progress when paused
        hideUnifiedProgressBar();
        
        showNotification('Scraper paused. Browser kept open for inspection.', 'warning');
      } else {
        showNotification(data.message || 'Failed to pause scraper', 'error');
        pauseScraperBtn.disabled = false;
      }
    })
    .catch(error => {
      console.error('Error pausing scraper:', error);
      showNotification('Error pausing scraper: ' + error.message, 'error');
      pauseScraperBtn.disabled = false;
    });
}

function resumeScraper() {
  console.log('Resuming scraper...');
  resumeScraperBtn.disabled = true;
  statusText.textContent = 'Resuming scraper...';
  
  // Hide the navigation control right away
  navigationControl.classList.add('hidden');
  
  fetch('/api/scraper/resume', {
    method: 'POST'
  })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // Update UI to show running state
        statusDot.className = 'status-dot online';
        statusText.textContent = 'Scraper running';
        startScraperBtn.disabled = true;
        stopScraperBtn.disabled = false;
        pauseScraperBtn.disabled = false;
        resumeScraperBtn.disabled = true;
        
        // Show progress for resumed scraper
        forceShowProgressBar({
          operation: 'Scraping Pages',
          total: 50, // Estimate, will be updated
          unit: 'pages'
        });
        
        showNotification('Scraper resumed', 'success');
      } else {
        showNotification(data.message || 'Failed to resume scraper', 'error');
        
        // Restore the paused state UI
        statusDot.className = 'status-dot paused';
        statusText.textContent = 'Scraper paused (browser open for inspection)';
        resumeScraperBtn.disabled = false;
        
        // Show navigation again since we're still paused
        navigationControl.classList.remove('hidden');
      }
    })
    .catch(error => {
      console.error('Error resuming scraper:', error);
      showNotification('Error resuming scraper: ' + error.message, 'error');
      
      // Restore the paused state UI
      statusDot.className = 'status-dot paused';
      statusText.textContent = 'Scraper paused (browser open for inspection)';
      resumeScraperBtn.disabled = false;
      
      // Show navigation again since we're still paused
      navigationControl.classList.remove('hidden');
    });
}

// Expose functions to window for Electron IPC
window.startScraper = startScraper;
window.stopScraper = stopScraper;
window.pauseScraper = pauseScraper;
window.resumeScraper = resumeScraper;

function checkScraperStatus() {
  fetch('/api/status')
    .then(response => response.json())
    .then(data => {
      updateScraperStatus(data.isRunning);
    })
    .catch(error => {
      console.error('Error fetching scraper status:', error);
      setStatusError('Failed to connect to server');
    });
}

function updateScraperStatus(running, isPaused = false, browserOpen = false, error = null) {
  isScraperRunning = running;

  console.log('🔧 updateScraperStatus called:', { running, isPaused, browserOpen, error });

  if (error) {
    setStatusError(error);
    return;
  }

  if (running) {
    // Regular running state
    statusDot.className = 'status-dot online';
    statusText.textContent = 'Scraper running';
    startScraperBtn.disabled = true;
    stopScraperBtn.disabled = false;
    pauseScraperBtn.disabled = false;
    resumeScraperBtn.disabled = true;
    navigationControl.classList.add('hidden');
    // DON'T automatically show progress container - let our unified system handle it
    console.log('🔧 Scraper running - NOT forcing progress container visible');
  } else if (isPaused && browserOpen) {
    // Paused state with browser open
    statusDot.className = 'status-dot paused';
    statusText.textContent = 'Scraper paused (browser open for inspection)';
    startScraperBtn.disabled = true;
    stopScraperBtn.disabled = false;
    pauseScraperBtn.disabled = true;
    resumeScraperBtn.disabled = false;
    navigationControl.classList.remove('hidden');
    // Hide progress when paused
    hideUnifiedProgressBar();
  } else {
    // Completely stopped state
    statusDot.className = 'status-dot offline';
    statusText.textContent = 'Scraper offline';
    startScraperBtn.disabled = false;
    stopScraperBtn.disabled = true;
    pauseScraperBtn.disabled = true;
    resumeScraperBtn.disabled = true;
    navigationControl.classList.add('hidden');
    // Hide scraper operation when stopped
    hideUnifiedProgressBar(window.scraperOperationId || 'scraper');
    delete window.scraperOperationId;
    console.log('🔧 Scraper stopped - hiding scraper progress bar');
  }
}

function setStatusError(errorMessage) {
  statusDot.className = 'status-dot error';
  statusText.textContent = 'Error: ' + (errorMessage || 'Unknown error');
  startScraperBtn.disabled = false;
  stopScraperBtn.disabled = true;
}

function updateProgress(completed, total) {
  // Check if elements exist (silently return if not found)
  if (!progressFill || !progressText) {
    // Elements not found - this is normal if we're using the new progress system
    return;
  }

  const percent = Math.floor((completed / total) * 100);
  progressFill.style.width = `${percent}%`;
  progressText.textContent = `${percent}%`;

  // Log progress for debugging
  console.log(`Scraping progress: ${completed}/${total} pages (${percent}%)`);
}

// Test function for progress bar
function testProgressBar() {
  console.log('Testing progress bar...');

  // Show progress container
  progressContainer.classList.remove('hidden');

  // Simulate progress from 0 to 100%
  let progress = 0;
  const interval = setInterval(() => {
    progress += 10;
    updateProgress(progress, 100);

    if (progress >= 100) {
      clearInterval(interval);
      console.log('Progress bar test completed');
    }
  }, 500);
}

// Make test function available globally for debugging
window.testProgressBar = testProgressBar;

function updateNextCycleTime(nextTime, delayMinutes) {
  // Format the next cycle time
  const nextDate = new Date(nextTime);
  const formattedTime = nextDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  
  nextCycleTime.textContent = formattedTime;
  nextCycleContainer.classList.remove('hidden');
  
  // Show notification
  showNotification(`Next scraping cycle in ${delayMinutes}m`);
}

// Functions
async function fetchPosts() {
  try {
    console.log('Fetching posts...');
    const response = await fetch('/api/posts');
    if (!response.ok) {
      throw new Error('Failed to fetch posts');
    }
    
    // Get posts data
    const data = await response.json();
    console.log(`Fetched ${data.length} posts`);
    
    if (!data || data.length === 0) {
      postsContainer.innerHTML = `
        <div class="no-results">
          <i class="fas fa-exclamation-circle"></i>
          <p>No posts found. Try starting the scraper to collect some posts.</p>
        </div>
      `;
      return;
    }
    
    // Store posts in our array
    posts = data;
    
    // Ensure posts have the correct structure for display
    posts = posts.map(post => {
      // Make sure we have a text field for display (either from finalFilteredText or existing text)
      const processedPost = { ...post };
      
      // Ensure we have text content to display
      if (!processedPost.text && processedPost.finalFilteredText) {
        processedPost.text = processedPost.finalFilteredText;
      }
      
      // Ensure we have a timestamp
      if (!processedPost.timestamp) {
        processedPost.timestamp = new Date().toISOString();
      }
      
      return processedPost;
    });
    
    // Sort by timestamp (newest first)
    posts.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    
    // Apply any active filters
    applyFilters();
    
  } catch (error) {
    console.error('Error fetching posts:', error);
    postsContainer.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-circle"></i>
        <p>Failed to load posts. Please try again.</p>
      </div>
    `;
  }
}

function renderPosts(postsToRender) {
  // Clear current posts
  postsContainer.innerHTML = '';

  if (postsToRender.length === 0) {
    postsContainer.innerHTML = `
      <div class="no-results">
        <i class="fas fa-search"></i>
        <p>No posts found matching your criteria.</p>
      </div>
    `;
    return;
  }

  // Calculate pagination
  totalPages = Math.ceil(postsToRender.length / postsPerPage);

  // Ensure current page is valid
  if (currentPage > totalPages) {
    currentPage = totalPages;
  }
  if (currentPage < 1) {
    currentPage = 1;
  }

  // Get posts for current page
  const startIndex = (currentPage - 1) * postsPerPage;
  const endIndex = startIndex + postsPerPage;
  const currentPagePosts = postsToRender.slice(startIndex, endIndex);

  console.log(`Rendering page ${currentPage} of ${totalPages} (${currentPagePosts.length} posts)`);

  // Add pagination controls at the top
  if (totalPages > 1) {
    const topPaginationHtml = renderPaginationControls(postsToRender.length);
    postsContainer.insertAdjacentHTML('beforeend', topPaginationHtml);
  }

  // Create posts grid container
  postsContainer.insertAdjacentHTML('beforeend', '<div class="posts-grid">');

  // Render each post using the card layout
  currentPagePosts.forEach(post => {
    const postCard = createPostCard(post);
    postsContainer.querySelector('.posts-grid').insertAdjacentHTML('beforeend', postCard);
  });

  // Close posts grid
  postsContainer.insertAdjacentHTML('beforeend', '</div>');

  // Add pagination controls at the bottom
  if (totalPages > 1) {
    const bottomPaginationHtml = renderPaginationControls(postsToRender.length);
    postsContainer.insertAdjacentHTML('beforeend', bottomPaginationHtml);
  }
}

// Render pagination controls
function renderPaginationControls(totalPosts) {
  if (totalPages <= 1) return '';

  let pagination = '<div class="pagination-container">';

  // Page info
  const startItem = (currentPage - 1) * postsPerPage + 1;
  const endItem = Math.min(currentPage * postsPerPage, totalPosts);

  pagination += `
    <div class="page-info">
      <span>Showing ${startItem}-${endItem} of ${totalPosts} posts</span>
    </div>
  `;

  pagination += '<div class="pagination">';

  // Previous button
  if (currentPage > 1) {
    pagination += `
      <button class="page-btn" onclick="changePage(${currentPage - 1})">
        <i class="fas fa-chevron-left"></i> Previous
      </button>
    `;
  }

  // Page numbers
  const startPage = Math.max(1, currentPage - 2);
  const endPage = Math.min(totalPages, currentPage + 2);

  if (startPage > 1) {
    pagination += `<button class="page-btn" onclick="changePage(1)">1</button>`;
    if (startPage > 2) {
      pagination += '<span class="page-dots">...</span>';
    }
  }

  for (let i = startPage; i <= endPage; i++) {
    const activeClass = i === currentPage ? 'active' : '';
    pagination += `
      <button class="page-btn ${activeClass}" onclick="changePage(${i})">${i}</button>
    `;
  }

  if (endPage < totalPages) {
    if (endPage < totalPages - 1) {
      pagination += '<span class="page-dots">...</span>';
    }
    pagination += `<button class="page-btn" onclick="changePage(${totalPages})">${totalPages}</button>`;
  }

  // Next button
  if (currentPage < totalPages) {
    pagination += `
      <button class="page-btn" onclick="changePage(${currentPage + 1})">
        Next <i class="fas fa-chevron-right"></i>
      </button>
    `;
  }

  pagination += '</div></div>';

  return pagination;
}

// Change page function
function changePage(page) {
  if (page < 1 || page > totalPages) return;

  currentPage = page;
  renderPosts(filteredPosts);

  // Scroll to top of posts container
  postsContainer.scrollIntoView({ behavior: 'smooth' });
}

function createPostElement(post) {
  // Clone the template
  const postElement = document.importNode(postTemplate.content, true).querySelector('.post-card');
  
  // Get source name from URL
  const sourceName = getSourceNameFromUrl(post.pageUrl);
  
  // Get initial letter for avatar
  const initial = sourceName.charAt(0).toUpperCase();
  
  // Set post data
  const sourceAvatar = postElement.querySelector('.source-avatar');
  sourceAvatar.textContent = initial;
  
  postElement.querySelector('.source-name').textContent = sourceName;
  postElement.querySelector('.post-text').textContent = post.text || post.normalizedTextHash;
  
  // Format the timestamp
  const timestamp = new Date(post.timestamp);
  const formattedTime = formatTime(timestamp);
  
  postElement.querySelector('.post-time').textContent = formattedTime;
  postElement.querySelector('.post-timestamp').textContent = formatTimestamp(timestamp);
  
  // Set link
  const sourceLink = postElement.querySelector('.source-link');
  sourceLink.href = post.pageUrl;
  
  // Add animation class for new posts
  if (isNewPost(post)) {
    postElement.classList.add('new-post');
  }
  
  return postElement;
}

function getSourceNameFromUrl(url) {
  try {
    // Extract domain or username from Facebook URL
    const urlObj = new URL(url);
    
    // Get the pathname without trailing slash
    let path = urlObj.pathname.replace(/\/$/, '');
    
    // Extract the username/page name (last part of the path)
    const parts = path.split('/');
    const name = parts[parts.length - 1] || 'Facebook Page';
    
    // Clean up and format the name
    return name.replace(/[._-]/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
      
  } catch (error) {
    return 'Facebook Page';
  }
}

function formatTime(date) {
  const now = new Date();
  const diffInSeconds = Math.floor((now - date) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} ${days === 1 ? 'day' : 'days'} ago`;
  }
}

function formatTimestamp(date) {
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

function isNewPost(post) {
  const now = new Date();
  const postDate = new Date(post.timestamp);
  const diffInMinutes = (now - postDate) / (1000 * 60);
  
  // Consider posts less than 5 minutes old as "new"
  return diffInMinutes < 5;
}

function handleSearch(e) {
  searchQuery = e.target.value.toLowerCase();
  applyFilters();
}



function applyFilters() {
  // Reset pagination when filters change
  currentPage = 1;

  // Filter posts based on search query and team member assignment
  filteredPosts = posts.filter(post => {
    // Text content to search in
    const content = (post.text || post.normalizedTextHash).toLowerCase();
    const source = getSourceNameFromUrl(post.pageUrl).toLowerCase();

    // Search filter
    const matchesSearch = searchQuery === '' ||
                          content.includes(searchQuery) ||
                          source.includes(searchQuery);

    // Team member filter
    let matchesTeamMember = currentFilter === 'all';

    if (currentFilter !== 'all' && currentFilter !== 'no-members') {
      // Find the team member by ID
      const teamMember = teamMembers.find(member => member.id === currentFilter);

      if (teamMember && teamMember.assignedPages) {
        // Check if the post's page URL matches any of the team member's assigned pages
        matchesTeamMember = teamMember.assignedPages.some(assignedPage => {
          if (typeof assignedPage === 'string') {
            // If assigned page is a string, check if post URL contains it
            return post.pageUrl && post.pageUrl.includes(assignedPage);
          } else if (assignedPage.link) {
            // If assigned page is an object with link property, match exactly
            return post.pageUrl === assignedPage.link;
          }
          return false;
        });
      }
    }

    return matchesSearch && matchesTeamMember;
  });

  // Render filtered posts
  renderPosts(filteredPosts);
}

function toggleTheme() {
  const isDarkMode = document.body.classList.toggle('dark-mode');
  
  // Update icon
  themeToggle.innerHTML = isDarkMode ? 
    '<i class="fas fa-sun"></i>' : 
    '<i class="fas fa-moon"></i>';
  
  // Save preference
  localStorage.setItem('darkMode', isDarkMode);
}

function showNotification(message, type = 'info', data = {}) {
  // Add to notification system
  addNotification(message, type, data);

  // Create notification element for toast display
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;

  // Set icon based on type
  let icon;
  switch (type) {
    case 'error':
      icon = 'fas fa-exclamation-circle';
      break;
    case 'warning':
      icon = 'fas fa-exclamation-triangle';
      break;
    case 'success':
      icon = 'fas fa-check-circle';
      break;
    default:
      icon = 'fas fa-bell';
  }

  notification.innerHTML = `
    <div class="notification-content">
      <i class="${icon}"></i>
      <p>${message}</p>
    </div>
  `;

  // Add to document
  document.body.appendChild(notification);

  // Animate in
  setTimeout(() => {
    notification.classList.add('show');
  }, 10);

  // Remove after a few seconds (longer for errors and warnings)
  let duration;
  switch (type) {
    case 'error':
      duration = 7000;
      break;
    case 'warning':
      duration = 5000;
      break;
    case 'success':
      duration = 4000;
      break;
    default:
      duration = 3000;
  }

  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      notification.remove();
    }, 300);
  }, duration);
}

// Check if a post has images and create HTML for them
function createPostImagesHTML(post) {
  if (!post.mediaUrls || post.mediaUrls.length === 0) {
    return '';
  }
  
  let imagesHTML = '<div class="post-images">';
  
  // Limit to maximum 4 images per post
  const displayedImages = post.mediaUrls.slice(0, 4);
  
  displayedImages.forEach(url => {
    imagesHTML += `<img src="${url}" alt="Post image" class="post-image" onclick="openImageModal('${url}')" />`;
  });
  
  // If there are more images than we're showing
  if (post.mediaUrls.length > 4) {
    imagesHTML += `<div class="more-images">+${post.mediaUrls.length - 4} more</div>`;
  }
  
  imagesHTML += '</div>';
  return imagesHTML;
}

// Create modal for viewing images
function setupImageModal() {
  const modal = document.createElement('div');
  modal.id = 'imageModal';
  modal.className = 'image-modal';
  modal.innerHTML = `
    <span class="close-modal">&times;</span>
    <img class="modal-content" id="modalImage">
  `;
  document.body.appendChild(modal);
  
  // Close modal when clicking the X
  modal.querySelector('.close-modal').addEventListener('click', () => {
    modal.style.display = 'none';
  });
  
  // Close modal when clicking outside the image
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.style.display = 'none';
    }
  });
}

// Open the image modal
function openImageModal(imgUrl) {
  const modal = document.getElementById('imageModal');
  const modalImg = document.getElementById('modalImage');
  modal.style.display = 'block';
  modalImg.src = imgUrl;
}

// Function to create a post card
function createPostCard(post) {
  const postDate = new Date(post.timestamp);
  const timeAgo = getTimeAgo(postDate);
  
  // Get domain name for the icon
  const domain = extractDomain(post.pageUrl);
  const domainIcon = `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
  
  // Create the images HTML if the post has images
  const imagesHTML = createPostImagesHTML(post);
  
  // Use pageName directly from the post
  const displayName = post.pageName || getSourceNameFromUrl(post.pageUrl);
  
  // Format post time if available - use combined display showing both relative and exact time
  let postTimeDisplay = '';
  if (post.enhancedPostTime) {
    postTimeDisplay = post.enhancedPostTime.combinedDisplay || post.enhancedPostTime.displayTime;
  } else if (post.postTime) {
    // Calculate enhanced time on the frontend if not available from backend
    const enhancedTime = calculateActualPostTimeClientCombined(post.postTime);
    postTimeDisplay = enhancedTime || post.postTime;
  }
  
  // Format video info if available
  let videoInfo = '';
  if (post.videoDuration) {
    videoInfo = `<span class="video-duration"><i class="fas fa-video"></i> ${post.videoDuration}</span>`;
  } else if (post.isLive) {
    videoInfo = `<span class="live-badge"><i class="fas fa-circle live-icon"></i> LIVE</span>`;
  }

  // Use the finalFilteredText field if available, or text field
  const postText = post.finalFilteredText || post.text || post.normalizedTextHash || '';

  // First letter of page name for avatar
  const firstLetter = displayName.charAt(0).toUpperCase();

  // Check if this post has a custom URL (link has been updated)
  const hasCustomUrl = post.customUrl && post.customUrl !== post.pageUrl;
  const linkUpdatedBadge = hasCustomUrl ?
    `<span class="link-updated-badge" title="Link has been updated"><i class="fas fa-link"></i></span>` : '';

  // Find which team member is assigned to this page
  const assignedMember = findTeamMemberForPage(post.pageUrl);
  console.log(`Post URL: ${post.pageUrl}, Assigned Member:`, assignedMember);
  const teamMemberLabel = assignedMember ?
    `<span class="team-member-label" title="Assigned to ${assignedMember.name}">${assignedMember.name}</span>` : '';

  // Format engagement metrics if available
  let engagementHTML = '';
  if (post.engagement) {
    const { likes = 0, comments = 0, shares = 0, views = 0 } = post.engagement;

    // Only show metrics that have values
    const engagementItems = [];

    if (likes > 0) {
      engagementItems.push(`<span class="engagement-item likes"><i class="far fa-thumbs-up"></i> ${formatNumber(likes)}</span>`);
    }

    if (comments > 0) {
      engagementItems.push(`<span class="engagement-item comments"><i class="far fa-comment"></i> ${formatNumber(comments)}</span>`);
    }

    if (shares > 0) {
      engagementItems.push(`<span class="engagement-item shares"><i class="far fa-share-square"></i> ${formatNumber(shares)}</span>`);
    }

    if (views > 0) {
      engagementItems.push(`<span class="engagement-item views"><i class="far fa-eye"></i> ${formatNumber(views)}</span>`);
    }

    // Only create the engagement section if there are metrics to show
    if (engagementItems.length > 0) {
      engagementHTML = `
        <div class="post-engagement">
          ${engagementItems.join('')}
        </div>
      `;
    }
  }
  
  return `
    <div class="post-card" data-post-id="${post.normalizedTextHash}">
      <div class="post-header">
        <div class="avatar-container">
          <div class="source-avatar">${firstLetter}</div>
        </div>
        <div class="header-content">
          <div class="header-top-row">
            <span class="page-name">${displayName}</span>
            <span class="post-original-time">${postTimeDisplay}</span>
          </div>
          <div class="post-time-row">
            <span class="post-time">${timeAgo}</span>
            ${teamMemberLabel}
            ${linkUpdatedBadge}
            ${post.lastMetricsUpdate ? `
              <span class="metrics-updated-indicator dynamic-time" data-timestamp="${post.lastMetricsUpdate}" data-post-id="${post.normalizedTextHash}" title="Metrics last updated: ${formatDate(post.lastMetricsUpdate)}">
                <i class="fas fa-sync-alt" style="color: #17a2b8; font-size: 0.8em;"></i>
                <span class="dynamic-time-text time-ago">${getTimeAgo(post.lastMetricsUpdate)}</span>
              </span>
            ` : `
              <span class="metrics-updated-indicator dynamic-time never-updated" data-post-id="${post.normalizedTextHash}" title="Metrics have never been updated">
                <i class="fas fa-sync-alt" style="color: #6c757d; font-size: 0.8em;"></i>
                <span class="dynamic-time-text" style="font-size: 0.8em; color: #6c757d;">Never updated</span>
              </span>
            `}
          </div>
        </div>
      </div>
      <div class="post-content">
        ${videoInfo}
        <p>${postText}</p>
        ${imagesHTML}
      </div>
      ${engagementHTML}
      <div class="post-footer">
        <a href="${post.customUrl || post.postUrl || post.pageUrl}" target="_blank" class="view-source-btn">
          <i class="fas fa-external-link-alt"></i> View Source
        </a>
        <button class="change-link-btn" data-post-id="${escapeHtml(post.normalizedTextHash)}" data-current-url="${escapeHtml(post.customUrl || post.postUrl || post.pageUrl)}">
          <i class="fas fa-edit"></i> Change Link
        </button>
        <button class="update-metrics-btn" data-post-id="${escapeHtml(post.normalizedTextHash)}" data-target-url="${escapeHtml(post.customUrl || post.postUrl || post.pageUrl)}">
          <i class="fas fa-chart-line"></i> Update Metrics
        </button>
        <button class="delete-post-btn" data-post-id="${escapeHtml(post.normalizedTextHash)}">
          <i class="fas fa-trash"></i> Delete Post
        </button>
      </div>
    </div>
  `;
}

// Escape HTML characters for safe insertion into HTML attributes
function escapeHtml(text) {
  if (!text) return '';

  // Use a more robust escaping method
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

// Format large numbers for display (e.g., 1,500 → 1.5K)
function formatNumber(num) {
  if (num === undefined || num === null) return '0';

  if (num >= 1000000) {
    return (num / 1000000).toFixed(1).replace(/\.0$/, '') + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K';
  }
  return num.toString();
}

// Extract domain from URL
function extractDomain(url) {
  try {
    const domain = new URL(url).hostname
      .replace('www.', '')
      .replace('.com', '')
      .replace('.net', '')
      .replace('.org', '');
    return domain.charAt(0).toUpperCase() + domain.slice(1);
  } catch (e) {
    return 'Facebook';
  }
}

// Calculate time ago
function getTimeAgo(date) {
  // Handle invalid dates
  if (!date || isNaN(new Date(date).getTime())) {
    return 'Unknown';
  }

  const now = new Date();
  const targetDate = new Date(date);
  const diffInSeconds = Math.floor((now - targetDate) / 1000);

  // Handle negative differences (future dates)
  if (diffInSeconds < 0) {
    return 'Just now';
  }

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  } else if (diffInSeconds < 604800) { // 7 days
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d ago`;
  } else if (diffInSeconds < 2592000) { // 30 days
    const weeks = Math.floor(diffInSeconds / 604800);
    return `${weeks}w ago`;
  } else if (diffInSeconds < 31536000) { // 365 days
    const months = Math.floor(diffInSeconds / 2592000);
    return `${months}mo ago`;
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return `${years}y ago`;
  }
}

// Format date for display
function formatDate(timestamp) {
  const date = new Date(timestamp);
  const options = {
    weekday: 'short',
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };
  return date.toLocaleDateString('en-US', options);
}

// Function to update all time-ago indicators in main dashboard
function updateTimeAgoIndicators() {
  const indicators = document.querySelectorAll('.time-ago');
  indicators.forEach(indicator => {
    const container = indicator.closest('.metrics-updated-indicator');
    if (container) {
      const timestamp = container.getAttribute('data-timestamp');
      if (timestamp) {
        indicator.textContent = getTimeAgo(new Date(timestamp));
      }
    }
  });
}

// Client-side function to calculate actual post time from relative time
function calculateActualPostTimeClient(relativeTime) {
  if (!relativeTime) return null;

  // Handle different relative time formats
  let match = relativeTime.match(/^(\d+)([smhd])$/i);
  if (!match) {
    // Try to match longer formats like "25 minutes ago", "2 hours ago"
    match = relativeTime.match(/(\d+)\s*(second|minute|hour|day)s?\s*ago/i);
    if (match) {
      const unit = match[2].toLowerCase().charAt(0); // Get first letter (s, m, h, d)
      match = [match[0], match[1], unit];
    }
  }

  if (!match) return null;

  const value = parseInt(match[1]);
  const unit = match[2].toLowerCase();

  // Create a date object for the current time
  const now = new Date();
  const postDate = new Date(now);

  // Subtract the appropriate amount of time based on the unit
  switch (unit) {
    case 's': // seconds
      postDate.setSeconds(now.getSeconds() - value);
      break;
    case 'm': // minutes
      postDate.setMinutes(now.getMinutes() - value);
      break;
    case 'h': // hours
      postDate.setHours(now.getHours() - value);
      break;
    case 'd': // days
      postDate.setDate(now.getDate() - value);
      break;
    default:
      return null;
  }

  // Check if it's today
  const today = new Date();
  const isToday = postDate.getDate() === today.getDate() &&
                  postDate.getMonth() === today.getMonth() &&
                  postDate.getFullYear() === today.getFullYear();

  const timeString = postDate.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const dateString = postDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });

  if (isToday) {
    return `${relativeTime} (Today at ${timeString})`;
  } else {
    return `${relativeTime} (${dateString} at ${timeString})`;
  }
}

// Combined display function for client-side calculation
function calculateActualPostTimeClientCombined(relativeTime) {
  if (!relativeTime) return null;

  // Handle different relative time formats
  let match = relativeTime.match(/^(\d+)([smhd])$/i);
  if (!match) {
    // Try to match longer formats like "25 minutes ago", "2 hours ago"
    match = relativeTime.match(/(\d+)\s*(second|minute|hour|day)s?\s*ago/i);
    if (match) {
      const unit = match[2].toLowerCase().charAt(0); // Get first letter (s, m, h, d)
      match = [match[0], match[1], unit];
    }
  }

  if (!match) return null;

  const value = parseInt(match[1]);
  const unit = match[2].toLowerCase();

  // Create a date object for the current time
  const now = new Date();
  const postDate = new Date(now);

  // Subtract the appropriate amount of time based on the unit
  switch (unit) {
    case 's': // seconds
      postDate.setSeconds(now.getSeconds() - value);
      break;
    case 'm': // minutes
      postDate.setMinutes(now.getMinutes() - value);
      break;
    case 'h': // hours
      postDate.setHours(now.getHours() - value);
      break;
    case 'd': // days
      postDate.setDate(now.getDate() - value);
      break;
    default:
      return null;
  }

  // Check if it's today
  const today = new Date();
  const isToday = postDate.getDate() === today.getDate() &&
                  postDate.getMonth() === today.getMonth() &&
                  postDate.getFullYear() === today.getFullYear();

  const timeString = postDate.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });

  const dateString = postDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });

  if (isToday) {
    return `${relativeTime} (Today at ${timeString})`;
  } else {
    return `${relativeTime} (${dateString} at ${timeString})`;
  }
}

// Start real-time updates for time indicators
function startTimeUpdates() {
  // Update every minute
  setInterval(updateTimeAgoIndicators, 60000);
}

// Define the openImageModal function in the global scope
window.openImageModal = openImageModal;

// Facebook Authentication Functions
function checkAuthStatus() {
  console.log('Checking Facebook authentication status...');
  fetch('/api/auth/status')
    .then(response => response.json())
    .then(data => {
      console.log('Auth status response:', data);
      
      // Update the auth status UI
      if (data.authenticated) {
        authStatus.innerHTML = `
          <i class="fas fa-check-circle"></i> 
          Authenticated (${data.cookiesCount} cookies)
          <div class="auth-info">Last updated: ${new Date(data.lastUpdated).toLocaleString()}</div>
        `;
        authStatus.classList.add('authenticated');
        authStatus.classList.remove('not-authenticated');
        
        // Enable/disable buttons
        loginButton.textContent = 'Re-Login to Facebook';
        saveSessionButton.disabled = true;
        clearSessionButton.disabled = false;
      } else {
        authStatus.innerHTML = `
          <i class="fas fa-times-circle"></i> 
          Not authenticated
          ${data.cookiesCount > 0 ? `<div class="auth-info">Found ${data.cookiesCount} cookies but validation failed</div>` : ''}
        `;
        authStatus.classList.add('not-authenticated');
        authStatus.classList.remove('authenticated');
        
        // Enable/disable buttons
        loginButton.textContent = 'Login to Facebook';
        saveSessionButton.disabled = true;
        clearSessionButton.disabled = data.cookiesCount === 0;
      }
    })
    .catch(error => {
      console.error('Error checking auth status:', error);
      authStatus.innerHTML = `<i class="fas fa-exclamation-triangle"></i> Error checking status: ${error.message}`;
      authStatus.classList.add('not-authenticated');
      authStatus.classList.remove('authenticated');
    });
}

function startFacebookLogin() {
  console.log('Starting Facebook login process...');
  
  // Update UI
  loginButton.disabled = true;
  loginButton.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Opening login browser...';
  authStatus.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Opening Facebook login browser...';
  authStatus.classList.remove('authenticated', 'not-authenticated');
  
  fetch('/api/auth/start', {
    method: 'POST'
  })
    .then(response => response.json())
    .then(data => {
      console.log('Login start response:', data);
      
      if (data.success) {
        // Update UI for successful browser launch
        loginButton.disabled = false;
        loginButton.innerHTML = '<i class="fab fa-facebook"></i> Login to Facebook';
        
        authStatus.innerHTML = `
          <i class="fas fa-info-circle"></i> 
          Login browser opened. Please log in to Facebook and then click "Save Session".
          <div class="auth-info">Make sure to complete the login including any CAPTCHA/verification.</div>
        `;
        
        // Enable save button
        saveSessionButton.disabled = false;
      } else {
        // Update UI for failed browser launch
        loginButton.disabled = false;
        loginButton.innerHTML = '<i class="fab fa-facebook"></i> Retry Login';
        
        authStatus.innerHTML = `
          <i class="fas fa-exclamation-triangle"></i> 
          Failed to start login browser: ${data.message || 'Unknown error'}
        `;
        authStatus.classList.add('not-authenticated');
      }
    })
    .catch(error => {
      console.error('Error starting Facebook login:', error);
      
      // Update UI for error
      loginButton.disabled = false;
      loginButton.innerHTML = '<i class="fab fa-facebook"></i> Retry Login';
      
      authStatus.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i> 
        Error starting login: ${error.message}
      `;
      authStatus.classList.add('not-authenticated');
    });
}

function saveFacebookSession() {
  console.log('Saving Facebook session...');
  
  // Update UI
  saveSessionButton.disabled = true;
  saveSessionButton.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Saving...';
  authStatus.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Saving Facebook session...';
  authStatus.classList.remove('authenticated', 'not-authenticated');
  
  fetch('/api/auth/save', {
    method: 'POST'
  })
    .then(response => {
      if (!response.ok) {
        return response.json().then(data => {
          throw new Error(data.message || `HTTP error ${response.status}`);
        });
      }
      return response.json();
    })
    .then(data => {
      console.log('Session save response:', data);
      
      // Reset button state
      saveSessionButton.disabled = true;
      saveSessionButton.innerHTML = '<i class="fas fa-save"></i> Save Session & Close Browser';
      
      if (data.success) {
        // Show success message with details
        authStatus.innerHTML = `
          <i class="fas fa-check-circle"></i> 
          ${data.message}
          <div class="auth-info">Saved ${data.cookiesCount} cookies${data.validated ? ' (validated)' : ' (not fully validated)'}</div>
        `;
        authStatus.classList.add(data.validated ? 'authenticated' : 'not-authenticated');
        
        // Enable clear button
        clearSessionButton.disabled = false;
        
        // Check auth status after a brief delay
        setTimeout(checkAuthStatus, 2000);
      } else {
        // Show error message
        authStatus.innerHTML = `
          <i class="fas fa-exclamation-triangle"></i> 
          ${data.message || 'Failed to save session'}
        `;
        authStatus.classList.add('not-authenticated');
      }
    })
    .catch(error => {
      console.error('Error saving Facebook session:', error);
      
      // Reset button state
      saveSessionButton.disabled = false;
      saveSessionButton.innerHTML = '<i class="fas fa-save"></i> Save Session & Close Browser';
      
      // Show error message
      authStatus.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i> 
        Error saving session: ${error.message}
      `;
      authStatus.classList.add('not-authenticated');
    });
}

function clearFacebookSession() {
  console.log('Clearing Facebook session...');
  
  // Update UI
  clearSessionButton.disabled = true;
  clearSessionButton.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Clearing...';
  
  fetch('/api/auth/clear', {
    method: 'POST'
  })
    .then(response => response.json())
    .then(data => {
      console.log('Session clear response:', data);
      
      // Reset button state
      clearSessionButton.disabled = true;
      clearSessionButton.innerHTML = '<i class="fas fa-trash-alt"></i> Clear Session';
      
      if (data.success) {
        // Show success message
        authStatus.innerHTML = '<i class="fas fa-info-circle"></i> Facebook session cleared';
        authStatus.classList.add('not-authenticated');
        authStatus.classList.remove('authenticated');
        
        // Check auth status after a brief delay
        setTimeout(checkAuthStatus, 1000);
      } else {
        // Show error message
        authStatus.innerHTML = `
          <i class="fas fa-exclamation-triangle"></i> 
          ${data.message || 'Failed to clear session'}
        `;
      }
    })
    .catch(error => {
      console.error('Error clearing Facebook session:', error);
      
      // Reset button state
      clearSessionButton.disabled = false;
      clearSessionButton.innerHTML = '<i class="fas fa-trash-alt"></i> Clear Session';
      
      // Show error message
      authStatus.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i> 
        Error clearing session: ${error.message}
      `;
    });
}

// Function to navigate the paused browser to a specific URL
function navigateToBrowserUrl() {
  const url = navigateUrlInput.value.trim();
  
  if (!url) {
    showNotification('Please enter a valid URL', 'warning');
    return;
  }
  
  // Make sure URL includes protocol
  let formattedUrl = url;
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    formattedUrl = `https://${url}`;
  }
  
  // Make sure it's a Facebook URL or warn the user
  if (!formattedUrl.includes('facebook.com') && !formattedUrl.includes('fb.com')) {
    if (!confirm('The URL does not appear to be a Facebook page. Continue anyway?')) {
      return;
    }
  }
  
  // Disable button during navigation
  navigateBtn.disabled = true;
  navigateBtn.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Opening...';
  
  // Update status text
  statusText.textContent = `Navigating to ${formattedUrl}...`;
  
  fetch('/api/scraper/navigate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ url: formattedUrl })
  })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showNotification(`Navigated to ${data.currentUrl}`, 'success');
        navigateUrlInput.value = '';
        
        // Update status text with current URL
        statusText.textContent = `Paused - Inspecting: ${data.currentUrl}`;
      } else {
        showNotification(data.message || 'Failed to navigate browser', 'error');
        
        // Restore status text
        statusText.textContent = 'Scraper paused (browser open for inspection)';
      }
    })
    .catch(error => {
      console.error('Error navigating browser:', error);
      showNotification('Error navigating browser: ' + error.message, 'error');
      
      // Restore status text
      statusText.textContent = 'Scraper paused (browser open for inspection)';
    })
    .finally(() => {
      // Reset button state
      navigateBtn.disabled = false;
      navigateBtn.innerHTML = '<i class="fas fa-external-link-alt"></i> Open in Browser';
    });
}

// Change Link Modal Functions
let currentPostId = null;

function openChangeLinkModal(postId, currentUrl) {
  currentPostId = postId;
  const modal = document.getElementById('changeLinkModal');
  const currentUrlInput = document.getElementById('currentUrl');
  const newUrlInput = document.getElementById('newUrl');

  // Set current URL
  currentUrlInput.value = currentUrl;

  // Clear new URL input
  newUrlInput.value = '';

  // Show modal
  modal.classList.add('show');

  // Focus on new URL input
  setTimeout(() => {
    newUrlInput.focus();
  }, 100);

  // Add escape key listener
  document.addEventListener('keydown', handleModalEscape);

  // Add click outside to close
  modal.addEventListener('click', handleModalClickOutside);
}

function closeChangeLinkModal() {
  const modal = document.getElementById('changeLinkModal');
  modal.classList.remove('show');
  currentPostId = null;

  // Remove escape key listener
  document.removeEventListener('keydown', handleModalEscape);

  // Remove click outside listener
  modal.removeEventListener('click', handleModalClickOutside);
}

function handleModalEscape(e) {
  if (e.key === 'Escape') {
    closeChangeLinkModal();
  }
}

function handleModalClickOutside(e) {
  if (e.target === e.currentTarget) {
    closeChangeLinkModal();
  }
}

function saveChangedLink() {
  const newUrlInput = document.getElementById('newUrl');
  const newUrl = newUrlInput.value.trim();

  if (!newUrl) {
    showNotification('Please enter a valid URL', 'warning');
    return;
  }

  // Validate URL format
  try {
    new URL(newUrl);
  } catch (error) {
    showNotification('Please enter a valid URL format', 'warning');
    return;
  }

  if (!currentPostId) {
    showNotification('Error: No post selected', 'error');
    return;
  }

  // Show loading state
  const saveBtn = document.querySelector('.modal-footer .btn.primary');
  const originalText = saveBtn.innerHTML;
  saveBtn.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Saving...';
  saveBtn.disabled = true;

  // Send update request to server
  fetch('/api/posts/update-url', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      postId: currentPostId,
      customUrl: newUrl
    })
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      if (data.success) {
        showNotification('Link updated successfully!', 'success');

        // Update the post card in the UI
        updatePostCardUrl(currentPostId, newUrl);

        // Close modal
        closeChangeLinkModal();
      } else {
        showNotification('Failed to update link: ' + (data.error || 'Unknown error'), 'error');
      }
    })
    .catch(error => {
      console.error('Error updating post URL:', error);
      showNotification('Error updating link: ' + error.message, 'error');
    })
    .finally(() => {
      // Reset button state
      saveBtn.innerHTML = originalText;
      saveBtn.disabled = false;
    });
}

function updatePostCardUrl(postId, newUrl) {
  // Find the post card by iterating through all post cards instead of using querySelector
  // This avoids CSS selector issues with special characters
  const postCards = document.querySelectorAll('.post-card');
  let targetPostCard = null;

  for (const postCard of postCards) {
    const cardPostId = postCard.getAttribute('data-post-id');
    if (cardPostId === postId) {
      targetPostCard = postCard;
      break;
    }
  }

  if (targetPostCard) {
    // Update the view source button href
    const viewSourceBtn = targetPostCard.querySelector('.view-source-btn');
    if (viewSourceBtn) {
      viewSourceBtn.href = newUrl;
    }

    // Update the change link button's data-current-url attribute
    const changeLinkBtn = targetPostCard.querySelector('.change-link-btn');
    if (changeLinkBtn) {
      changeLinkBtn.setAttribute('data-current-url', escapeHtml(newUrl));
    }

    // Update the update metrics button's data-target-url attribute
    const updateMetricsBtn = targetPostCard.querySelector('.update-metrics-btn');
    if (updateMetricsBtn) {
      updateMetricsBtn.setAttribute('data-target-url', escapeHtml(newUrl));
      console.log('Updated metrics button target URL to:', newUrl);
    }

    // Add or update the link updated badge
    addLinkUpdatedBadge(targetPostCard);

    // Update the post data in memory
    const postIndex = posts.findIndex(post => post.normalizedTextHash === postId);
    if (postIndex !== -1) {
      posts[postIndex].customUrl = newUrl;
    }

    console.log('Successfully updated post card URL for:', postId);
  } else {
    console.error('Could not find post card for ID:', postId);
  }
}

// Function to add link updated badge to a post card
function addLinkUpdatedBadge(postCard) {
  // Check if badge already exists
  let existingBadge = postCard.querySelector('.link-updated-badge');

  if (!existingBadge) {
    // Create the badge
    const badge = document.createElement('span');
    badge.className = 'link-updated-badge';
    badge.title = 'Link has been updated';
    badge.innerHTML = '<i class="fas fa-link"></i>';

    // Find the post-time-row and add the badge there
    let postTimeRow = postCard.querySelector('.post-time-row');

    // If post-time-row doesn't exist (older posts), create it
    if (!postTimeRow) {
      const postTime = postCard.querySelector('.post-time');
      if (postTime) {
        // Create the post-time-row wrapper
        postTimeRow = document.createElement('div');
        postTimeRow.className = 'post-time-row';

        // Move the post-time into the new wrapper
        postTime.parentNode.insertBefore(postTimeRow, postTime);
        postTimeRow.appendChild(postTime);
      }
    }

    if (postTimeRow) {
      postTimeRow.appendChild(badge);

      // Add a subtle animation to draw attention
      badge.style.animation = 'fadeInScale 0.5s ease-out';
      setTimeout(() => {
        badge.style.animation = '';
      }, 500);
    }
  }
}

// Update Post Metrics Function
function updatePostMetrics(postId, targetUrl, buttonElement) {
  // Show loading state
  const originalContent = buttonElement.innerHTML;
  buttonElement.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Updating...';
  buttonElement.disabled = true;
  buttonElement.classList.add('loading');

  console.log('Starting metrics update for:', { postId, targetUrl });
  console.log('Button element:', buttonElement);

  // Send request to server to scrape metrics
  fetch('/api/posts/update-metrics', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      postId: postId,
      targetUrl: targetUrl
    })
  })
    .then(response => {
      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (!response.ok) {
        return response.text().then(text => {
          console.error('Server response error:', text);
          throw new Error(`HTTP error ${response.status}: ${text}`);
        });
      }
      return response.json();
    })
    .then(data => {
      console.log('Server response data:', data);

      // DETAILED DEBUG: Log the exact metrics received
      if (data.metrics) {
        console.log('=== RECEIVED METRICS DEBUG ===');
        console.log('data.metrics.likes =', data.metrics.likes);
        console.log('data.metrics.comments =', data.metrics.comments);
        console.log('data.metrics.shares =', data.metrics.shares);
        console.log('data.metrics.postTime =', data.metrics.postTime);
        console.log('Raw metrics object:', JSON.stringify(data.metrics));
        console.log('=== END RECEIVED METRICS DEBUG ===');
      }

      if (data.success) {
        const hasPostTime = data.metrics && data.metrics.postTime;
        const successMessage = hasPostTime ?
          'Metrics and post time updated successfully!' :
          'Metrics updated successfully!';
        showNotification(successMessage, 'success');

        // Update the post card in the UI with new metrics
        // Find the post data to check if it's a video
        const postData = posts.find(post => post.normalizedTextHash === postId);
        updatePostCardMetrics(postId, data.metrics, postData);

        // Update the "last updated" indicator
        updatePostLastUpdatedIndicator(postId);

        console.log('Metrics updated:', data.metrics);
        if (hasPostTime) {
          console.log('Post time updated to:', data.metrics.postTime);
        }

        // Refresh posts data from server to ensure UI stays in sync
        console.log('Refreshing posts data from server...');
        fetchPosts().then(() => {
          console.log('Posts data refreshed successfully');
        }).catch(error => {
          console.error('Error refreshing posts data:', error);
        });
      } else {
        console.error('Server returned error:', data.error);
        showNotification('Failed to update metrics: ' + (data.error || 'Unknown error'), 'error');
      }
    })
    .catch(error => {
      console.error('Error updating post metrics:', error);
      console.error('Error stack:', error.stack);
      showNotification('Error updating metrics: ' + error.message, 'error');
    })
    .finally(() => {
      // Reset button state
      buttonElement.innerHTML = originalContent;
      buttonElement.disabled = false;
      buttonElement.classList.remove('loading');
    });
}

// Update Post Card Metrics in UI
function updatePostCardMetrics(postId, newMetrics, postData = null) {
  console.log('updatePostCardMetrics called with:', { postId, newMetrics, postData });

  // Check if this is a video post
  const isVideoPost = postData && postData.videoDuration &&
                     postData.videoDuration.trim() !== "" &&
                     postData.videoDuration !== "0:00 / 0:00";

  console.log(`Post type: ${isVideoPost ? 'video' : 'picture'} (videoDuration: "${postData?.videoDuration || 'N/A'}")`);

  // DETAILED DEBUG: Log exactly what we're about to update
  console.log('=== UI UPDATE DEBUG ===');
  console.log('About to update UI with:');
  console.log('- LIKES field will get value:', newMetrics.likes);
  console.log('- COMMENTS field will get value:', newMetrics.comments);
  if (!isVideoPost) {
    console.log('- SHARES field will get value:', newMetrics.shares);
  } else {
    console.log('- SHARES field will be HIDDEN (video post)');
  }
  console.log('=== END UI UPDATE DEBUG ===');

  // Find the post card by iterating through all post cards
  const postCards = document.querySelectorAll('.post-card');
  let targetPostCard = null;

  console.log(`Found ${postCards.length} post cards`);

  for (const postCard of postCards) {
    const cardPostId = postCard.getAttribute('data-post-id');
    console.log(`Checking card with ID: ${cardPostId} against target: ${postId}`);
    if (cardPostId === postId) {
      targetPostCard = postCard;
      console.log('Found matching post card!');
      break;
    }
  }

  if (targetPostCard) {
    // Update engagement numbers in the UI
    let engagementSection = targetPostCard.querySelector('.post-engagement');
    console.log('Engagement section found:', !!engagementSection);

    // If no engagement section exists, create one
    if (!engagementSection && newMetrics && (newMetrics.likes > 0 || newMetrics.comments > 0 || newMetrics.shares > 0 || newMetrics.views > 0)) {
      console.log('Creating new engagement section');
      engagementSection = document.createElement('div');
      engagementSection.className = 'post-engagement';

      // Insert before the post-footer
      const postFooter = targetPostCard.querySelector('.post-footer');
      if (postFooter) {
        targetPostCard.insertBefore(engagementSection, postFooter);
      } else {
        targetPostCard.appendChild(engagementSection);
      }
    }

    if (engagementSection && newMetrics) {
      // Find engagement items by their specific classes
      const likesElement = engagementSection.querySelector('.engagement-item.likes');
      const commentsElement = engagementSection.querySelector('.engagement-item.comments');
      const sharesElement = engagementSection.querySelector('.engagement-item.shares');
      const viewsElement = engagementSection.querySelector('.engagement-item.views');

      console.log('Elements found:', {
        likes: !!likesElement,
        comments: !!commentsElement,
        shares: !!sharesElement,
        views: !!viewsElement
      });

      // Update likes
      if (newMetrics.likes !== undefined && newMetrics.likes > 0) {
        if (likesElement) {
          // Update existing likes element
          const oldText = likesElement.textContent;
          likesElement.innerHTML = `<i class="far fa-thumbs-up"></i> ${formatNumber(newMetrics.likes)}`;
          console.log(`Updated likes from "${oldText}" to "${formatNumber(newMetrics.likes)}"`);
        } else {
          // Create new likes element if it doesn't exist
          const newLikesElement = document.createElement('span');
          newLikesElement.className = 'engagement-item likes';
          newLikesElement.innerHTML = `<i class="far fa-thumbs-up"></i> ${formatNumber(newMetrics.likes)}`;
          engagementSection.appendChild(newLikesElement);
          console.log(`Created new likes element with ${formatNumber(newMetrics.likes)}`);
        }
      }

      // Update comments
      if (newMetrics.comments !== undefined && newMetrics.comments > 0) {
        if (commentsElement) {
          // Update existing comments element
          const oldText = commentsElement.textContent;
          commentsElement.innerHTML = `<i class="far fa-comment"></i> ${formatNumber(newMetrics.comments)}`;
          console.log(`Updated comments from "${oldText}" to "${formatNumber(newMetrics.comments)}"`);
        } else {
          // Create new comments element if it doesn't exist
          const newCommentsElement = document.createElement('span');
          newCommentsElement.className = 'engagement-item comments';
          newCommentsElement.innerHTML = `<i class="far fa-comment"></i> ${formatNumber(newMetrics.comments)}`;
          engagementSection.appendChild(newCommentsElement);
          console.log(`Created new comments element with ${formatNumber(newMetrics.comments)}`);
        }
      }

      // Update shares (only for picture posts, hide for video posts)
      if (!isVideoPost && newMetrics.shares !== undefined && newMetrics.shares >= 0) {
        if (sharesElement) {
          // Update existing shares element
          const oldText = sharesElement.textContent;
          sharesElement.innerHTML = `<i class="far fa-share-square"></i> ${formatNumber(newMetrics.shares)}`;
          sharesElement.style.display = 'inline-block'; // Make sure it's visible
          console.log(`Updated shares from "${oldText}" to "${formatNumber(newMetrics.shares)}"`);
        } else {
          // Create new shares element if it doesn't exist
          const newSharesElement = document.createElement('span');
          newSharesElement.className = 'engagement-item shares';
          newSharesElement.innerHTML = `<i class="far fa-share-square"></i> ${formatNumber(newMetrics.shares)}`;
          engagementSection.appendChild(newSharesElement);
          console.log(`Created new shares element with ${formatNumber(newMetrics.shares)}`);
        }
      } else if (isVideoPost && sharesElement) {
        // Hide shares element for video posts
        sharesElement.style.display = 'none';
        console.log('Hidden shares element for video post');
      }

      // Update views (only for video posts)
      if (isVideoPost && newMetrics.views !== undefined && newMetrics.views > 0) {
        if (viewsElement) {
          // Update existing views element
          const oldText = viewsElement.textContent;
          viewsElement.innerHTML = `<i class="far fa-eye"></i> ${formatNumber(newMetrics.views)}`;
          console.log(`Updated views from "${oldText}" to "${formatNumber(newMetrics.views)}"`);
        } else {
          // Create new views element if it doesn't exist
          const newViewsElement = document.createElement('span');
          newViewsElement.className = 'engagement-item views';
          newViewsElement.innerHTML = `<i class="far fa-eye"></i> ${formatNumber(newMetrics.views)}`;
          engagementSection.appendChild(newViewsElement);
          console.log(`Created new views element with ${formatNumber(newMetrics.views)}`);
        }
      } else if (!isVideoPost && viewsElement) {
        // Hide views element for non-video posts
        viewsElement.style.display = 'none';
        console.log('Hidden views element for non-video post');
      }

      // Add visual feedback for updated metrics
      engagementSection.style.animation = 'pulse 0.5s ease-in-out';
      setTimeout(() => {
        engagementSection.style.animation = '';
      }, 500);
    }

    // Update post time if provided
    if (newMetrics.postTime) {
      const postTimeElement = targetPostCard.querySelector('.post-time');
      if (postTimeElement) {
        const oldTime = postTimeElement.textContent;
        postTimeElement.textContent = newMetrics.postTime;
        console.log(`Updated post time from "${oldTime}" to "${newMetrics.postTime}"`);

        // Add visual feedback for updated time
        postTimeElement.style.animation = 'pulse 0.5s ease-in-out';
        setTimeout(() => {
          postTimeElement.style.animation = '';
        }, 500);
      } else {
        console.log('Post time element not found in card');
      }
    }

    // Update the post data in memory
    const postIndex = posts.findIndex(post => post.normalizedTextHash === postId);
    if (postIndex !== -1) {
      if (!posts[postIndex].engagement) {
        posts[postIndex].engagement = {};
      }
      posts[postIndex].engagement = { ...posts[postIndex].engagement, ...newMetrics };

      // Update post time in memory if provided
      if (newMetrics.postTime) {
        posts[postIndex].postTime = newMetrics.postTime;
        console.log('Updated post time in memory:', newMetrics.postTime);
      }
    }

    console.log('Successfully updated post card metrics for:', postId);
  } else {
    console.error('Could not find post card for metrics update:', postId);
  }
}

// Delete Post Modal Functions
let currentDeletePostId = null;

function openDeletePostModal(postId) {
  currentDeletePostId = postId;
  const modal = document.getElementById('deletePostModal');
  const previewContent = document.getElementById('deletePostPreview');

  // Find the post data
  const post = posts.find(p => p.normalizedTextHash === postId);
  if (!post) {
    showNotification('Error: Post not found', 'error');
    return;
  }

  // Create preview content
  const postText = post.finalFilteredText || post.text || post.normalizedTextHash || '';
  const truncatedText = postText.length > 150 ? postText.substring(0, 150) + '...' : postText;
  const displayName = post.pageName || getSourceNameFromUrl(post.pageUrl);

  previewContent.innerHTML = `
    <div class="preview-post-header">
      <strong>${displayName}</strong>
    </div>
    <div class="preview-post-text">${truncatedText}</div>
  `;

  // Show modal
  modal.classList.add('show');

  // Add escape key listener
  document.addEventListener('keydown', handleDeleteModalEscape);

  // Add click outside to close
  modal.addEventListener('click', handleDeleteModalClickOutside);
}

function closeDeletePostModal() {
  const modal = document.getElementById('deletePostModal');
  modal.classList.remove('show');
  currentDeletePostId = null;

  // Remove escape key listener
  document.removeEventListener('keydown', handleDeleteModalEscape);

  // Remove click outside listener
  modal.removeEventListener('click', handleDeleteModalClickOutside);
}

function handleDeleteModalEscape(e) {
  if (e.key === 'Escape') {
    closeDeletePostModal();
  }
}

function handleDeleteModalClickOutside(e) {
  if (e.target === e.currentTarget) {
    closeDeletePostModal();
  }
}

function confirmDeletePost() {
  if (!currentDeletePostId) {
    showNotification('Error: No post selected for deletion', 'error');
    return;
  }

  // Show loading state
  const deleteBtn = document.querySelector('.delete-post-modal .btn.danger');
  const originalText = deleteBtn.innerHTML;
  deleteBtn.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Deleting...';
  deleteBtn.disabled = true;

  // Send delete request to server
  fetch('/api/posts/delete', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      postId: currentDeletePostId
    })
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error ${response.status}`);
      }
      return response.json();
    })
    .then(data => {
      if (data.success) {
        showNotification('Post deleted successfully!', 'success');

        // Remove the post card from the UI
        removePostCardFromUI(currentDeletePostId);

        // Remove from posts array
        const postIndex = posts.findIndex(post => post.normalizedTextHash === currentDeletePostId);
        if (postIndex !== -1) {
          posts.splice(postIndex, 1);
        }

        // Close modal
        closeDeletePostModal();

        // Update posts count display
        updatePostsCount();
      } else {
        showNotification('Failed to delete post: ' + (data.error || 'Unknown error'), 'error');
      }
    })
    .catch(error => {
      console.error('Error deleting post:', error);
      showNotification('Error deleting post: ' + error.message, 'error');
    })
    .finally(() => {
      // Reset button state
      deleteBtn.innerHTML = originalText;
      deleteBtn.disabled = false;
    });
}

function removePostCardFromUI(postId) {
  // Find the post card by iterating through all post cards
  const postCards = document.querySelectorAll('.post-card');
  let targetPostCard = null;

  for (const postCard of postCards) {
    const cardPostId = postCard.getAttribute('data-post-id');
    if (cardPostId === postId) {
      targetPostCard = postCard;
      break;
    }
  }

  if (targetPostCard) {
    // Add fade out animation
    targetPostCard.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
    targetPostCard.style.opacity = '0';
    targetPostCard.style.transform = 'scale(0.95)';

    // Remove after animation
    setTimeout(() => {
      targetPostCard.remove();
    }, 300);

    console.log('Successfully removed post card for:', postId);
  } else {
    console.error('Could not find post card for deletion:', postId);
  }
}

function updatePostsCount() {
  // Update any posts count displays if they exist
  const postsCountElements = document.querySelectorAll('.posts-count');
  postsCountElements.forEach(element => {
    element.textContent = posts.length;
  });
}

// Start dynamic time updates for "last updated" indicators
function startDynamicTimeUpdates() {
    // Update every minute
    setInterval(updateAllDynamicTimes, 60000);

    // Initial update after 5 seconds to ensure DOM is ready
    setTimeout(updateAllDynamicTimes, 5000);
}

// Update all dynamic time indicators
function updateAllDynamicTimes() {
    const dynamicTimeElements = document.querySelectorAll('.dynamic-time');

    dynamicTimeElements.forEach(element => {
        const timestamp = element.getAttribute('data-timestamp');
        const postId = element.getAttribute('data-post-id');

        if (timestamp && !element.classList.contains('never-updated')) {
            const timeText = element.querySelector('.dynamic-time-text');
            if (timeText) {
                const newTimeText = getTimeAgo(timestamp);

                // Only update if the text has changed
                if (timeText.textContent !== newTimeText) {
                    timeText.textContent = newTimeText;

                    // Add a subtle animation to indicate the update
                    timeText.style.transition = 'color 0.3s ease';
                    timeText.style.color = '#28a745';

                    setTimeout(() => {
                        timeText.style.color = '';
                    }, 1000);
                }
            }
        }
    });
}

// Enhanced getTimeAgo function for more precise time calculations
function getTimeAgo(timestamp) {
    const now = new Date();
    const past = new Date(timestamp);
    const diffMs = now - past;

    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);

    if (diffSeconds < 30) {
        return 'Just now';
    } else if (diffSeconds < 60) {
        return `${diffSeconds}s ago`;
    } else if (diffMinutes < 60) {
        return `${diffMinutes}m ago`;
    } else if (diffHours < 24) {
        return `${diffHours}h ago`;
    } else if (diffDays < 7) {
        return `${diffDays}d ago`;
    } else if (diffWeeks < 4) {
        return `${diffWeeks}w ago`;
    } else if (diffMonths < 12) {
        return `${diffMonths}mo ago`;
    } else {
        return past.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: diffDays > 365 ? 'numeric' : undefined
        });
    }
}

// Update post's last updated indicator after metrics update
function updatePostLastUpdatedIndicator(postId) {
    const postCard = document.querySelector(`[data-post-id="${postId}"]`);
    if (!postCard) return;

    const dynamicTimeElement = postCard.querySelector('.dynamic-time');
    if (dynamicTimeElement) {
        const now = new Date().toISOString();

        // Update the timestamp
        dynamicTimeElement.setAttribute('data-timestamp', now);
        dynamicTimeElement.classList.remove('never-updated');

        // Update the text
        const timeText = dynamicTimeElement.querySelector('.dynamic-time-text');
        if (timeText) {
            timeText.textContent = 'Just now';
            timeText.style.color = '#28a745';
            timeText.style.fontWeight = '600';

            // Reset color after animation
            setTimeout(() => {
                timeText.style.color = '';
                timeText.style.fontWeight = '';
            }, 2000);
        }

        // Update the icon color
        const icon = dynamicTimeElement.querySelector('i');
        if (icon) {
            icon.style.color = '#17a2b8';
        }
    }
}

// Date Filter Modal Functions
function showDateFilterModal() {
  const modal = document.getElementById('date-filter-modal');
  if (modal) {
    modal.style.display = 'block';

    // Set default end date to now
    const now = new Date();
    const endDateInput = document.getElementById('end-date');
    if (endDateInput) {
      endDateInput.value = now.toISOString().slice(0, 16);
    }

    // Set default start date to 24 hours ago
    const startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const startDateInput = document.getElementById('start-date');
    if (startDateInput) {
      startDateInput.value = startDate.toISOString().slice(0, 16);
    }

    // Add event listeners for filter options
    setupDateFilterListeners();
    updateFilterPreview();
  }
}

function closeDateFilterModal() {
  const modal = document.getElementById('date-filter-modal');
  if (modal) {
    modal.style.display = 'none';
  }
}

function setupDateFilterListeners() {
  const filterOptions = document.querySelectorAll('input[name="dateFilter"]');
  const hoursInput = document.getElementById('hours-input');
  const dateRangeInputs = document.getElementById('date-range-inputs');

  filterOptions.forEach(option => {
    option.addEventListener('change', function() {
      if (this.value === 'range') {
        dateRangeInputs.style.display = 'block';
      } else {
        dateRangeInputs.style.display = 'none';
      }
      updateFilterPreview();
    });
  });

  if (hoursInput) {
    hoursInput.addEventListener('input', updateFilterPreview);
  }

  const startDateInput = document.getElementById('start-date');
  const endDateInput = document.getElementById('end-date');

  if (startDateInput) startDateInput.addEventListener('change', updateFilterPreview);
  if (endDateInput) endDateInput.addEventListener('change', updateFilterPreview);
}

function updateFilterPreview() {
  const selectedFilter = document.querySelector('input[name="dateFilter"]:checked');
  const previewText = document.getElementById('preview-text');

  if (!selectedFilter || !previewText) return;

  let text = '';

  switch (selectedFilter.value) {
    case 'all':
      text = 'All posts will be updated';
      break;
    case 'today':
      text = 'Only posts extracted today will be updated';
      break;
    case 'hours':
      const hours = document.getElementById('hours-input').value || 24;
      text = `Only posts extracted in the last ${hours} hours will be updated`;
      break;
    case 'range':
      const startDate = document.getElementById('start-date').value;
      const endDate = document.getElementById('end-date').value;
      if (startDate && endDate) {
        const start = new Date(startDate).toLocaleDateString();
        const end = new Date(endDate).toLocaleDateString();
        text = `Only posts extracted between ${start} and ${end} will be updated`;
      } else {
        text = 'Please select both start and end dates';
      }
      break;
  }

  previewText.textContent = text;
}

async function startFilteredBulkUpdate() {
  const selectedFilter = document.querySelector('input[name="dateFilter"]:checked');

  if (!selectedFilter) {
    showNotification('Please select a filter option', 'error');
    return;
  }

  let dateFilter = {
    type: selectedFilter.value
  };

  // Add specific filter parameters
  switch (selectedFilter.value) {
    case 'hours':
      const hours = parseInt(document.getElementById('hours-input').value) || 24;
      dateFilter.hours = hours;
      break;
    case 'range':
      const startDate = document.getElementById('start-date').value;
      const endDate = document.getElementById('end-date').value;
      if (!startDate || !endDate) {
        showNotification('Please select both start and end dates', 'error');
        return;
      }
      dateFilter.startDate = startDate;
      dateFilter.endDate = endDate;
      break;
  }

  // Close the modal
  closeDateFilterModal();

  try {
    console.log('📊 Starting filtered bulk metrics update with filter:', dateFilter);

    // Force show progress bar immediately with consistent ID
    window.bulkMetricsOperationId = 'bulk-metrics';
    forceShowProgressBar({
      operation: 'Starting Update...',
      total: 1,
      unit: 'initializing',
      operationId: window.bulkMetricsOperationId
    });

    // Update button states immediately
    updateAllMetricsBtn.disabled = true;
    updateAllMetricsBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
    stopMetricsUpdateBtn.style.display = 'inline-block';
    stopMetricsUpdateBtn.disabled = false;

    const response = await fetch('/api/posts/update-all-metrics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ dateFilter })
    });

    const result = await response.json();

    if (result.success) {
      let filterDescription = '';
      switch (dateFilter.type) {
        case 'all':
          filterDescription = 'all posts';
          break;
        case 'today':
          filterDescription = 'posts extracted today';
          break;
        case 'hours':
          filterDescription = `posts extracted in the last ${dateFilter.hours} hours`;
          break;
        case 'range':
          const start = new Date(dateFilter.startDate).toLocaleDateString();
          const end = new Date(dateFilter.endDate).toLocaleDateString();
          filterDescription = `posts extracted between ${start} and ${end}`;
          break;
      }

      showNotification(`Bulk metrics update started for ${filterDescription}! Processing ${result.totalPosts} posts. Estimated time: ${result.estimatedTime}`, 'success');

      // Update existing progress bar with actual data (don't create new one)
      if (window.activeOperations.has(window.bulkMetricsOperationId)) {
        const operationData = window.activeOperations.get(window.bulkMetricsOperationId);
        operationData.operation = 'Updating Metrics';
        operationData.total = result.totalPosts;
        operationData.unit = 'posts';

        // Update the display
        const operationElement = operationData.element;
        const operationSpan = operationElement.querySelector('.progress-operation');
        const statsSpan = operationElement.querySelector('.progress-stats');

        if (operationSpan) operationSpan.textContent = 'Updating Metrics';
        if (statsSpan) statsSpan.textContent = `0 / ${result.totalPosts} posts`;
      }

      // Check status periodically
      checkBulkMetricsStatus();
    } else {
      // Hide progress bar on error
      hideUnifiedProgressBar();
      // Reset button states
      updateAllMetricsBtn.disabled = false;
      updateAllMetricsBtn.innerHTML = '<i class="fas fa-chart-line"></i> Update All Metrics';
      stopMetricsUpdateBtn.style.display = 'none';
      stopMetricsUpdateBtn.disabled = true;

      showNotification('Failed to start bulk metrics update: ' + result.error, 'error');
    }
  } catch (error) {
    console.error('Error starting filtered bulk metrics update:', error);

    // Hide progress bar on error
    hideUnifiedProgressBar();
    // Reset button states
    updateAllMetricsBtn.disabled = false;
    updateAllMetricsBtn.innerHTML = '<i class="fas fa-chart-line"></i> Update All Metrics';
    stopMetricsUpdateBtn.style.display = 'none';
    stopMetricsUpdateBtn.disabled = true;

    showNotification('Error starting bulk metrics update: ' + error.message, 'error');
  }
}

// Close modal when clicking outside
window.addEventListener('click', function(event) {
  const modal = document.getElementById('date-filter-modal');
  if (event.target === modal) {
    closeDateFilterModal();
  }
});

// Make functions available globally
window.openChangeLinkModal = openChangeLinkModal;
window.closeChangeLinkModal = closeChangeLinkModal;
window.saveChangedLink = saveChangedLink;
window.updatePostMetrics = updatePostMetrics;
window.openDeletePostModal = openDeletePostModal;
window.closeDeletePostModal = closeDeletePostModal;
window.confirmDeletePost = confirmDeletePost;
window.updatePostLastUpdatedIndicator = updatePostLastUpdatedIndicator;
window.showDateFilterModal = showDateFilterModal;
window.closeDateFilterModal = closeDateFilterModal;
window.startFilteredBulkUpdate = startFilteredBulkUpdate;