# PacketStream Proxy Integration Guide

This guide explains how to set up and use PacketStream proxies with your Facebook scraping and metrics project.

## 🚀 Quick Setup

### 1. Configure Your Proxy

Edit your `.env` file and replace the placeholder with your actual PacketStream proxy credentials:

```env
# Replace with your actual PacketStream proxy credentials
PACKETSTREAM_PROXY=proxy.packetstream.io:31111:YOUR_USERNAME:YOUR_PASSWORD_session-YOUR_SESSION_ID
```

**Format:** `host:port:username:password_session-sessionid`

**Example:** `proxy.packetstream.io:31111:alijaafar:UexHX6M2yDw38CKd_session-81l0l3hP`

### 2. Multiple Proxies (Optional)

If you have multiple proxies, you can add them as a comma-separated list:

```env
PACKETSTREAM_PROXIES=proxy1.packetstream.io:31111:user1:pass1_session-id1,proxy2.packetstream.io:31111:user2:pass2_session-id2
```

### 3. Test Your Setup

Run the proxy test script to verify everything is working:

```bash
node test-proxy.js
```

## 🎯 What Uses Proxies

The proxy integration automatically applies to:

### ✅ Scraping Operations
- **Main Facebook scraping** - All page visits and data extraction
- **Individual member scraping** - Team member specific scraping
- **Metrics scraping** - Post metrics and engagement data collection
- **Debug operations** - Facebook page inspection and debugging

### ✅ Image Downloads
- **Media extraction** - Downloading images and videos from posts
- **Local storage** - Saving media files with proxy protection

### ❌ What Does NOT Use Proxies
- **Database operations** - MySQL connections remain direct
- **Local file operations** - Reading/writing local files
- **Internal API calls** - Communication between app components

## 🛠️ Proxy Management

### Web Interface

Access the proxy management interface at:
```
http://localhost:3000/proxy-manager.html
```

Features:
- View proxy statistics and status
- Test individual or all proxies
- Add/remove proxies dynamically
- Reset failed proxies
- Toggle proxy rotation

### API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/proxy/stats` | GET | Get proxy statistics |
| `/api/proxy/test` | POST | Test current proxy connectivity |
| `/api/proxy/test-all` | POST | Test all configured proxies |
| `/api/proxy/reset-failed` | POST | Reset failed proxy status |
| `/api/proxy/toggle-rotation` | POST | Enable/disable proxy rotation |
| `/api/proxy/add` | POST | Add a new proxy |
| `/api/proxy/remove` | POST | Remove a proxy |

## ⚙️ Configuration Options

### Environment Variables

```env
# Proxy rotation (default: true)
PROXY_ROTATION_ENABLED=true

# Maximum retry attempts (default: 3)
PROXY_MAX_RETRIES=3

# Retry delay in milliseconds (default: 1000)
PROXY_RETRY_DELAY=1000
```

### Proxy Rotation

When enabled, the system automatically rotates between available proxies for each request. This helps:
- Distribute load across proxies
- Avoid rate limiting
- Improve reliability

## 🔧 Technical Details

### Puppeteer Integration

All Puppeteer browser instances are automatically configured with:
- Proxy server settings
- Authentication credentials
- Optimized launch arguments

### Axios Integration

HTTP requests use proxy-enabled Axios instances with:
- Automatic proxy rotation
- Request/response interceptors
- Error handling and retry logic

### Statistics Tracking

The system tracks for each proxy:
- Total requests made
- Success/failure rates
- Average response times
- Last usage timestamp
- Current status (active/failed)

## 🚨 Troubleshooting

### Common Issues

1. **"Proxy connection ended before receiving CONNECT response"**
   - Check your proxy credentials
   - Verify the proxy server is accessible
   - Ensure your PacketStream account is active

2. **"All proxies have failed"**
   - Use the reset failed proxies feature
   - Check your internet connection
   - Verify proxy credentials are correct

3. **Slow performance**
   - Check proxy response times in the management interface
   - Consider adding more proxies
   - Verify proxy server location

### Testing Commands

```bash
# Test proxy integration
node test-proxy.js

# Start the server and check proxy manager
npm run server
# Then visit: http://localhost:3000/proxy-manager.html
```

## 📊 Monitoring

### Real-time Monitoring

The proxy manager interface provides real-time monitoring of:
- Active vs failed proxies
- Current IP address
- Request success rates
- Response times

### Logs

Proxy operations are logged with prefixes:
- `🔄` - Proxy rotation
- `✅` - Successful operations
- `❌` - Failed operations
- `⚠️` - Warnings

## 🔒 Security Notes

- Proxy credentials are stored in environment variables
- Never commit actual credentials to version control
- Use session-based authentication when possible
- Monitor proxy usage to avoid account suspension

## 📈 Performance Tips

1. **Use multiple proxies** for better load distribution
2. **Enable rotation** to avoid rate limiting
3. **Monitor response times** and remove slow proxies
4. **Reset failed proxies** periodically
5. **Use session IDs** for better proxy performance

## 🆘 Support

If you encounter issues:

1. Check the proxy manager interface for status
2. Run the test script to diagnose problems
3. Review the server logs for error messages
4. Verify your PacketStream account status
5. Test with a single proxy first before adding multiple

---

**Note:** This integration is specifically designed for PacketStream proxies. Other proxy providers may require modifications to the authentication format.
