<?php
/**
 * Database Configuration Manager
 * Reads configuration from .env file and provides database connection
 */

class DatabaseConfig {
    private static $config = null;
    private static $connection = null;
    
    public static function loadConfig() {
        if (self::$config !== null) {
            return self::$config;
        }
        
        $envFile = __DIR__ . '/.env';
        $config = [
            'DB_HOST' => 'localhost',
            'DB_PORT' => '3306',
            'DB_NAME' => 'facebook_db',
            'DB_USER' => 'root',
            'DB_PASSWORD' => '',
            'DB_SSL' => 'false',
            'AUTO_SYNC_ENABLED' => 'true',
            'AUTO_SYNC_INTERVAL' => '300000',
            'USE_DATABASE' => 'true'
        ];
        
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '#') === 0) continue; // Skip comments
                if (strpos($line, '=') === false) continue; // Skip invalid lines
                
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if present
                if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
                    (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
                    $value = substr($value, 1, -1);
                }
                
                $config[$key] = $value;
            }
        }
        
        self::$config = $config;
        return $config;
    }
    
    public static function getConnection() {
        if (self::$connection !== null) {
            return self::$connection;
        }
        
        $config = self::loadConfig();
        
        try {
            $dsn = "mysql:host={$config['DB_HOST']};port={$config['DB_PORT']};charset=utf8mb4";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];
            
            // Add SSL options if enabled
            if (strtolower($config['DB_SSL']) === 'true') {
                if (!empty($config['DB_SSL_CA_PATH'])) {
                    $options[PDO::MYSQL_ATTR_SSL_CA] = $config['DB_SSL_CA_PATH'];
                }
                if (!empty($config['DB_SSL_CERT_PATH'])) {
                    $options[PDO::MYSQL_ATTR_SSL_CERT] = $config['DB_SSL_CERT_PATH'];
                }
                if (!empty($config['DB_SSL_KEY_PATH'])) {
                    $options[PDO::MYSQL_ATTR_SSL_KEY] = $config['DB_SSL_KEY_PATH'];
                }
                $options[PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT] = false;
            }
            
            $pdo = new PDO($dsn, $config['DB_USER'], $config['DB_PASSWORD'], $options);
            
            // Create database if it doesn't exist
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['DB_NAME']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $pdo->exec("USE `{$config['DB_NAME']}`");
            
            self::$connection = $pdo;
            return $pdo;
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    public static function testConnection() {
        try {
            $pdo = self::getConnection();
            $stmt = $pdo->query("SELECT 1");
            return [
                'success' => true,
                'message' => 'Database connection successful',
                'server_info' => $pdo->getAttribute(PDO::ATTR_SERVER_INFO),
                'server_version' => $pdo->getAttribute(PDO::ATTR_SERVER_VERSION)
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Database connection failed',
                'error' => $e->getMessage()
            ];
        }
    }
    
    public static function getConfig($key = null) {
        $config = self::loadConfig();
        return $key ? ($config[$key] ?? null) : $config;
    }
}

// Auto-initialize database tables on first connection
function initializeDatabaseTables() {
    try {
        $pdo = DatabaseConfig::getConnection();
        
        // Check if tables exist
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('members', $tables) || !in_array('pages', $tables) || !in_array('posts', $tables)) {
            // Run the database schema
            $schemaFile = __DIR__ . '/database_schema.sql';
            if (file_exists($schemaFile)) {
                $sql = file_get_contents($schemaFile);
                // Remove USE statement as we already selected the database
                $sql = preg_replace('/USE\s+\w+;/i', '', $sql);
                $pdo->exec($sql);
                error_log("Database tables initialized successfully");
            }
        }
        
        return true;
    } catch (Exception $e) {
        error_log("Failed to initialize database tables: " . $e->getMessage());
        return false;
    }

    public static function saveConfig($newConfig) {
        $envFile = __DIR__ . '/.env';
        $lines = [];

        // Add header comment
        $lines[] = "# MySQL Database Configuration";
        $lines[] = "# Updated: " . date('Y-m-d H:i:s');
        $lines[] = "";

        // Add configuration values
        foreach ($newConfig as $key => $value) {
            $lines[] = "{$key}={$value}";
        }

        $content = implode("\n", $lines);

        if (file_put_contents($envFile, $content) !== false) {
            self::$config = null; // Reset cached config
            return true;
        }

        return false;
    }
}

// Auto-initialize database tables on first connection
function initializeDatabaseTables() {
    try {
        $pdo = DatabaseConfig::getConnection();

        // Check if tables exist
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

        if (!in_array('members', $tables) || !in_array('pages', $tables) || !in_array('posts', $tables)) {
            // Run the database schema
            $schemaFile = __DIR__ . '/database_schema.sql';
            if (file_exists($schemaFile)) {
                $sql = file_get_contents($schemaFile);
                // Remove USE statement as we already selected the database
                $sql = preg_replace('/USE\s+\w+;/i', '', $sql);

                // Split by semicolon and execute each statement
                $statements = array_filter(array_map('trim', explode(';', $sql)));
                foreach ($statements as $statement) {
                    if (!empty($statement) && !preg_match('/^\s*--/', $statement)) {
                        try {
                            $pdo->exec($statement);
                        } catch (PDOException $e) {
                            // Log but don't fail on duplicate table errors
                            if (strpos($e->getMessage(), 'already exists') === false) {
                                error_log("SQL Error: " . $e->getMessage() . " in statement: " . substr($statement, 0, 100));
                            }
                        }
                    }
                }
                error_log("Database tables initialized successfully");
            }
        }

        return true;
    } catch (Exception $e) {
        error_log("Failed to initialize database tables: " . $e->getMessage());
        return false;
    }
}
?>
