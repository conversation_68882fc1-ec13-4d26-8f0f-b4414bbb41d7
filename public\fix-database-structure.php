<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

$conn = new mysqli("localhost", "root", "", "facebook_db");
$response = ['success' => false, 'message' => '', 'debug' => []];

try {
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    $response['debug'][] = "Connected to MySQL successfully";
    
    // Drop existing tables to recreate with correct structure
    $tables = ['posts', 'pages', 'members'];
    foreach ($tables as $table) {
        $conn->query("DROP TABLE IF EXISTS $table");
        $response['debug'][] = "Dropped table '$table' if it existed";
    }
    
    // Create members table
    $membersSql = "
        CREATE TABLE members (
            id VARCHAR(50) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            role VARCHAR(100),
            assigned_pages_count INT DEFAULT 0,
            total_posts_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($conn->query($membersSql)) {
        $response['debug'][] = "✅ Created 'members' table";
    } else {
        throw new Exception("Error creating members table: " . $conn->error);
    }
    
    // Create pages table
    $pagesSql = "
        CREATE TABLE pages (
            id VARCHAR(50) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            url VARCHAR(500),
            member_id VARCHAR(50),
            posts_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($conn->query($pagesSql)) {
        $response['debug'][] = "✅ Created 'pages' table";
    } else {
        throw new Exception("Error creating pages table: " . $conn->error);
    }
    
    // Create posts table
    $postsSql = "
        CREATE TABLE posts (
            id VARCHAR(100) PRIMARY KEY,
            page_id VARCHAR(50),
            member_id VARCHAR(50),
            caption TEXT,
            likes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            views_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            post_url VARCHAR(500),
            page_url VARCHAR(500),
            page_name VARCHAR(255),
            post_date DATETIME,
            last_metrics_update DATETIME,
            extraction_timestamp DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE SET NULL,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    if ($conn->query($postsSql)) {
        $response['debug'][] = "✅ Created 'posts' table";
    } else {
        throw new Exception("Error creating posts table: " . $conn->error);
    }
    
    $response['success'] = true;
    $response['message'] = 'Database structure fixed successfully!';
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
}

$conn->close();
echo json_encode($response, JSON_PRETTY_PRINT);
?>
