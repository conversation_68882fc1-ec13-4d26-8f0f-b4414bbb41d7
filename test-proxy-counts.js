// Test script to verify proxy counts are correct
const proxyManager = require('./proxy-manager');

console.log('🔍 Testing Proxy Count Fix...\n');

// Get the raw data
const allStats = proxyManager.getProxyStats(); // All proxies with stats (including failed)
const activeStats = proxyManager.getProxyStatistics(); // Only active proxies
const failedStats = proxyManager.getPermanentlyFailedProxies(); // Only failed proxies

console.log('📊 Raw Proxy Manager Data:');
console.log(`   Active Proxies Array: ${proxyManager.proxies.length}`);
console.log(`   Permanently Failed Set: ${proxyManager.permanentlyFailedProxies.size}`);
console.log(`   Total Proxy Stats: ${proxyManager.proxyStats.size}`);

console.log('\n📈 Method Results:');
console.log(`   getProxyStats() (ALL): ${Object.keys(allStats).length}`);
console.log(`   getProxyStatistics() (ACTIVE): ${activeStats.length}`);
console.log(`   getPermanentlyFailedProxies() (FAILED): ${failedStats.length}`);

console.log('\n🧮 Expected Calculations:');
console.log(`   Total = Active + Failed: ${proxyManager.proxies.length} + ${proxyManager.permanentlyFailedProxies.size} = ${proxyManager.proxies.length + proxyManager.permanentlyFailedProxies.size}`);

console.log('\n✅ Verification:');
const expectedTotal = proxyManager.proxies.length + proxyManager.permanentlyFailedProxies.size;
const actualActiveFromMethod = activeStats.length;
const actualFailedFromMethod = failedStats.length;

if (actualActiveFromMethod === proxyManager.proxies.length) {
    console.log(`   ✅ Active count matches: ${actualActiveFromMethod}`);
} else {
    console.log(`   ❌ Active count mismatch: Expected ${proxyManager.proxies.length}, Got ${actualActiveFromMethod}`);
}

if (actualFailedFromMethod === proxyManager.permanentlyFailedProxies.size) {
    console.log(`   ✅ Failed count matches: ${actualFailedFromMethod}`);
} else {
    console.log(`   ❌ Failed count mismatch: Expected ${proxyManager.permanentlyFailedProxies.size}, Got ${actualFailedFromMethod}`);
}

if (actualActiveFromMethod + actualFailedFromMethod === expectedTotal) {
    console.log(`   ✅ Total count matches: ${actualActiveFromMethod + actualFailedFromMethod}`);
} else {
    console.log(`   ❌ Total count mismatch: Expected ${expectedTotal}, Got ${actualActiveFromMethod + actualFailedFromMethod}`);
}

console.log('\n🔍 Sample Active Proxies:');
activeStats.slice(0, 3).forEach((stat, index) => {
    const sessionId = stat.proxy.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown';
    console.log(`   ${index + 1}. Session ${sessionId} - Requests: ${stat.requests}, Success Rate: ${stat.requests > 0 ? (stat.successes / stat.requests * 100).toFixed(1) : 0}%`);
});

console.log('\n🔍 Sample Failed Proxies:');
failedStats.slice(0, 3).forEach((stat, index) => {
    const sessionId = stat.proxy.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown';
    console.log(`   ${index + 1}. Session ${sessionId} - Requests: ${stat.requests}, Failures: ${stat.failures}`);
});
