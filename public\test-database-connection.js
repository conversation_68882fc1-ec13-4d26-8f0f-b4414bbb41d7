// Database Connection Test Suite

// Test configuration
const TEST_CONFIG = {
    baseURL: window.location.origin,
    databaseAPI: '/api/database',
    timeout: 10000
};

// Utility functions
function updateTestStatus(testId, status, message, data = null) {
    const testItem = document.getElementById(testId);
    if (!testItem) return;

    // Remove existing status classes
    testItem.classList.remove('success', 'error', 'warning', 'loading');
    
    // Add new status class
    testItem.classList.add(status);
    
    // Update icon
    const icon = testItem.querySelector('.status-icon');
    if (icon) {
        icon.className = 'status-icon';
        switch (status) {
            case 'success':
                icon.className += ' fas fa-check-circle';
                break;
            case 'error':
                icon.className += ' fas fa-times-circle';
                break;
            case 'warning':
                icon.className += ' fas fa-exclamation-triangle';
                break;
            case 'loading':
                icon.className += ' fas fa-spinner';
                break;
        }
    }
    
    // Remove existing result div
    const existingResult = testItem.querySelector('.test-result');
    if (existingResult) {
        existingResult.remove();
    }
    
    // Add result div if message provided
    if (message) {
        const resultDiv = document.createElement('div');
        resultDiv.className = `test-result ${status}`;
        resultDiv.textContent = message;
        
        if (data) {
            const dataDiv = document.createElement('div');
            dataDiv.className = 'json-display';
            dataDiv.textContent = JSON.stringify(data, null, 2);
            resultDiv.appendChild(dataDiv);
        }
        
        testItem.appendChild(resultDiv);
    }
}

function makeRequest(url, options = {}) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), TEST_CONFIG.timeout);
    
    return fetch(url, {
        ...options,
        signal: controller.signal
    }).finally(() => {
        clearTimeout(timeoutId);
    });
}

// Individual test functions
async function testServerRunning() {
    const testId = 'test-server-running';
    updateTestStatus(testId, 'loading', 'Testing server connection...');
    
    try {
        const response = await makeRequest(`${TEST_CONFIG.baseURL}/api/posts`);
        
        if (response.ok) {
            const data = await response.json();
            updateTestStatus(testId, 'success', `Server is running. Found ${data.length} posts in JSON.`, {
                status: response.status,
                postsCount: data.length
            });
        } else {
            updateTestStatus(testId, 'error', `Server responded with status: ${response.status}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `Server connection failed: ${error.message}`);
    }
}

async function testMySQLConnection() {
    const testId = 'test-mysql-connection';
    updateTestStatus(testId, 'loading', 'Testing MySQL connection...');
    
    try {
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/sync-status`);
        
        if (response.ok) {
            const data = await response.json();
            updateTestStatus(testId, 'success', 'MySQL connection successful!', data);
        } else if (response.status === 503) {
            updateTestStatus(testId, 'error', 'Database not available (503). MySQL might not be running or configured.');
        } else {
            const errorText = await response.text();
            updateTestStatus(testId, 'error', `MySQL connection failed: ${response.status} - ${errorText}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `MySQL connection test failed: ${error.message}`);
    }
}

async function testDatabaseExists() {
    const testId = 'test-database-exists';
    updateTestStatus(testId, 'loading', 'Checking if facebook_db exists...');
    
    try {
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/test-database`);
        
        if (response.ok) {
            const data = await response.json();
            updateTestStatus(testId, 'success', 'Database facebook_db exists and is accessible!', data);
        } else {
            updateTestStatus(testId, 'error', `Database check failed: ${response.status}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'warning', `Could not verify database existence: ${error.message}`);
    }
}

async function testTablesExist() {
    const testId = 'test-tables-exist';
    updateTestStatus(testId, 'loading', 'Checking if required tables exist...');
    
    try {
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/test-tables`);
        
        if (response.ok) {
            const data = await response.json();
            const allTablesExist = data.posts && data.pages && data.members;
            
            if (allTablesExist) {
                updateTestStatus(testId, 'success', 'All required tables exist!', data);
            } else {
                updateTestStatus(testId, 'warning', 'Some tables are missing. You may need to run the setup script.', data);
            }
        } else {
            updateTestStatus(testId, 'error', `Table check failed: ${response.status}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `Table existence test failed: ${error.message}`);
    }
}

async function testTableStructure() {
    const testId = 'test-table-structure';
    updateTestStatus(testId, 'loading', 'Checking table structure...');
    
    try {
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/test-structure`);
        
        if (response.ok) {
            const data = await response.json();
            updateTestStatus(testId, 'success', 'Table structure verified!', data);
        } else {
            updateTestStatus(testId, 'error', `Structure check failed: ${response.status}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'warning', `Structure test not available: ${error.message}`);
    }
}

async function testPostsAPI() {
    const testId = 'test-api-posts';
    updateTestStatus(testId, 'loading', 'Testing posts API...');
    
    try {
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/posts`);
        
        if (response.ok) {
            const data = await response.json();
            updateTestStatus(testId, 'success', `Posts API working! Found ${data.length} posts.`, {
                count: data.length,
                sample: data.slice(0, 2)
            });
        } else if (response.status === 503) {
            updateTestStatus(testId, 'warning', 'Database not available, but API endpoint exists.');
        } else {
            const errorText = await response.text();
            updateTestStatus(testId, 'error', `Posts API failed: ${response.status} - ${errorText}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `Posts API test failed: ${error.message}`);
    }
}

async function testPagesAPI() {
    const testId = 'test-api-pages';
    updateTestStatus(testId, 'loading', 'Testing pages API...');
    
    try {
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/pages`);
        
        if (response.ok) {
            const data = await response.json();
            updateTestStatus(testId, 'success', `Pages API working! Found ${data.length} pages.`, {
                count: data.length,
                sample: data.slice(0, 2)
            });
        } else if (response.status === 503) {
            updateTestStatus(testId, 'warning', 'Database not available, but API endpoint exists.');
        } else {
            const errorText = await response.text();
            updateTestStatus(testId, 'error', `Pages API failed: ${response.status} - ${errorText}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `Pages API test failed: ${error.message}`);
    }
}

async function testMembersAPI() {
    const testId = 'test-api-members';
    updateTestStatus(testId, 'loading', 'Testing members API...');
    
    try {
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/members`);
        
        if (response.ok) {
            const data = await response.json();
            updateTestStatus(testId, 'success', `Members API working! Found ${data.length} members.`, {
                count: data.length,
                sample: data.slice(0, 2)
            });
        } else if (response.status === 503) {
            updateTestStatus(testId, 'warning', 'Database not available, but API endpoint exists.');
        } else {
            const errorText = await response.text();
            updateTestStatus(testId, 'error', `Members API failed: ${response.status} - ${errorText}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `Members API test failed: ${error.message}`);
    }
}

async function testSyncStatusAPI() {
    const testId = 'test-api-sync-status';
    updateTestStatus(testId, 'loading', 'Testing sync status API...');
    
    try {
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/sync-status`);
        
        if (response.ok) {
            const data = await response.json();
            updateTestStatus(testId, 'success', 'Sync status API working!', data);
        } else {
            const errorText = await response.text();
            updateTestStatus(testId, 'error', `Sync status API failed: ${response.status} - ${errorText}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `Sync status API test failed: ${error.message}`);
    }
}

async function testCreateTables() {
    const testId = 'test-create-tables';
    updateTestStatus(testId, 'loading', 'Attempting to create tables...');

    try {
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/quick-setup`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.ok) {
            const data = await response.json();
            updateTestStatus(testId, 'success', 'Database setup completed!', data);
        } else {
            const errorText = await response.text();
            updateTestStatus(testId, 'error', `Database setup failed: ${response.status} - ${errorText}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `Database setup test failed: ${error.message}`);
    }
}

async function testInsertSample() {
    const testId = 'test-insert-sample';
    updateTestStatus(testId, 'loading', 'Inserting sample data...');
    
    try {
        const sampleData = {
            member: {
                id: 'test_member_' + Date.now(),
                name: 'Test Member',
                email: '<EMAIL>',
                role: 'Tester'
            }
        };
        
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/test-insert`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(sampleData)
        });
        
        if (response.ok) {
            const data = await response.json();
            updateTestStatus(testId, 'success', 'Sample data inserted successfully!', data);
        } else {
            const errorText = await response.text();
            updateTestStatus(testId, 'error', `Sample insert failed: ${response.status} - ${errorText}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `Sample insert test failed: ${error.message}`);
    }
}

async function testSyncOperation() {
    const testId = 'test-sync-operation';
    updateTestStatus(testId, 'loading', 'Testing sync operation...');
    
    try {
        const response = await makeRequest(`${TEST_CONFIG.databaseAPI}/sync-from-json`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                updateTestStatus(testId, 'success', 'Sync operation completed successfully!', data);
            } else {
                updateTestStatus(testId, 'error', 'Sync operation failed.', data);
            }
        } else {
            const errorText = await response.text();
            updateTestStatus(testId, 'error', `Sync operation failed: ${response.status} - ${errorText}`);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `Sync operation test failed: ${error.message}`);
    }
}

async function testJSONFiles() {
    const testId = 'test-json-files';
    updateTestStatus(testId, 'loading', 'Checking JSON files...');
    
    try {
        const [postsResponse, pagesResponse, membersResponse] = await Promise.all([
            makeRequest('/api/posts'),
            makeRequest('/api/pages'),
            makeRequest('/api/team')
        ]);
        
        const results = {
            posts: postsResponse.ok ? (await postsResponse.json()).length : 'Error',
            pages: pagesResponse.ok ? (await pagesResponse.json()).length : 'Error',
            members: membersResponse.ok ? (await membersResponse.json()).length : 'Error'
        };
        
        const allOk = postsResponse.ok && pagesResponse.ok && membersResponse.ok;
        
        if (allOk) {
            updateTestStatus(testId, 'success', 'All JSON files are accessible!', results);
        } else {
            updateTestStatus(testId, 'warning', 'Some JSON files may have issues.', results);
        }
    } catch (error) {
        updateTestStatus(testId, 'error', `JSON files test failed: ${error.message}`);
    }
}

// Main test runner
async function runAllTests() {
    console.log('Starting comprehensive database test suite...');
    
    // Run tests in logical order
    await testServerRunning();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testJSONFiles();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testMySQLConnection();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testDatabaseExists();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testTablesExist();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testTableStructure();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testPostsAPI();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testPagesAPI();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testMembersAPI();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testSyncStatusAPI();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    console.log('All tests completed!');
}

function clearResults() {
    const testItems = document.querySelectorAll('.test-item');
    testItems.forEach(item => {
        item.classList.remove('success', 'error', 'warning', 'loading');
        const result = item.querySelector('.test-result');
        if (result) {
            result.remove();
        }
        const icon = item.querySelector('.status-icon');
        if (icon) {
            icon.className = 'status-icon fas fa-circle';
        }
    });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('Database Connection Test Suite loaded');
});
