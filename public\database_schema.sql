-- Facebook Database Schema
-- Run this SQL to create the required tables

USE facebook_db;

-- Members table
CREATE TABLE IF NOT EXISTS members (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    role VARCHAR(100) DEFAULT 'Member',
    assigned_pages_count INT DEFAULT 0,
    total_posts_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Pages table
CREATE TABLE IF NOT EXISTS pages (
    id VARCHAR(100) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    member_id VARCHAR(50),
    posts_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (member_id) REFERENCES members(id) ON DELETE SET NULL
);

-- Posts table
CREATE TABLE IF NOT EXISTS posts (
    id VARCHAR(100) PRIMARY KEY,
    page_id VARCHAR(100),
    member_id VARCHAR(50),
    caption TEXT,
    media_id VARCHAR(255),
    likes_count INT DEFAULT 0,
    comments_count INT DEFAULT 0,
    views_count INT DEFAULT 0,
    shares_count INT DEFAULT 0,
    post_url TEXT,
    page_url TEXT,
    page_name VARCHAR(255),
    post_date DATETIME,
    last_metrics_update DATETIME,
    extraction_timestamp DATETIME,
    enhanced_post_time JSON,
    engagement JSON,
    final_filtered_text TEXT,
    normalized_text_hash VARCHAR(100),
    custom_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE SET NULL,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL,
    INDEX idx_page_id (page_id),
    INDEX idx_member_id (member_id),
    INDEX idx_post_date (post_date),
    INDEX idx_extraction_timestamp (extraction_timestamp),
    INDEX idx_last_metrics_update (last_metrics_update),
    INDEX idx_media_id (media_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_posts_page_member ON posts(page_id, member_id);
CREATE INDEX IF NOT EXISTS idx_pages_member ON pages(member_id);
CREATE INDEX IF NOT EXISTS idx_posts_metrics_update ON posts(last_metrics_update);

-- Create views for easier data access
CREATE OR REPLACE VIEW posts_with_details AS
SELECT
    p.id as post_id,
    p.page_id,
    p.member_id,
    p.caption,
    p.media_id,
    p.likes_count,
    p.comments_count,
    p.views_count,
    p.shares_count,
    p.post_url,
    p.page_url,
    p.page_name,
    p.post_date,
    p.last_metrics_update,
    p.extraction_timestamp,
    pg.name as page_name_from_pages,
    pg.url as page_url_from_pages,
    m.name as member_name,
    m.email as member_email,
    m.role as member_role
FROM posts p
LEFT JOIN pages pg ON p.page_id = pg.id
LEFT JOIN members m ON p.member_id = m.id;

CREATE OR REPLACE VIEW pages_with_details AS
SELECT 
    pg.id as page_id,
    pg.name as page_name,
    pg.url as page_url,
    pg.member_id,
    pg.posts_count,
    m.name as member_name,
    m.email as member_email,
    m.role as member_role,
    pg.created_at,
    pg.updated_at
FROM pages pg
LEFT JOIN members m ON pg.member_id = m.id;

CREATE OR REPLACE VIEW members_with_stats AS
SELECT 
    m.id as member_id,
    m.name as member_name,
    m.email,
    m.role,
    m.assigned_pages_count,
    m.total_posts_count,
    COUNT(DISTINCT pg.id) as actual_pages_count,
    COUNT(DISTINCT p.id) as actual_posts_count,
    SUM(p.likes_count) as total_likes,
    SUM(p.comments_count) as total_comments,
    SUM(p.views_count) as total_views,
    SUM(p.shares_count) as total_shares,
    MAX(p.last_metrics_update) as latest_metrics_update,
    m.created_at,
    m.updated_at
FROM members m
LEFT JOIN pages pg ON m.id = pg.member_id
LEFT JOIN posts p ON m.id = p.member_id
GROUP BY m.id, m.name, m.email, m.role, m.assigned_pages_count, m.total_posts_count, m.created_at, m.updated_at;

-- Stored procedures for data synchronization
DELIMITER //

CREATE OR REPLACE PROCEDURE SyncPostsFromJSON()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE post_id VARCHAR(100);
    DECLARE page_id VARCHAR(100);
    DECLARE member_id VARCHAR(50);
    
    -- Update posts count for pages
    UPDATE pages pg SET posts_count = (
        SELECT COUNT(*) FROM posts p WHERE p.page_id = pg.id
    );
    
    -- Update posts count for members
    UPDATE members m SET total_posts_count = (
        SELECT COUNT(*) FROM posts p WHERE p.member_id = m.id
    );
    
    -- Update assigned pages count for members
    UPDATE members m SET assigned_pages_count = (
        SELECT COUNT(*) FROM pages pg WHERE pg.member_id = m.id
    );
    
END //

CREATE OR REPLACE PROCEDURE GetDashboardStats()
BEGIN
    SELECT 
        (SELECT COUNT(*) FROM posts) as total_posts,
        (SELECT COUNT(*) FROM pages) as total_pages,
        (SELECT COUNT(*) FROM members) as total_members,
        (SELECT SUM(likes_count) FROM posts) as total_likes,
        (SELECT SUM(comments_count) FROM posts) as total_comments,
        (SELECT SUM(views_count) FROM posts) as total_views,
        (SELECT SUM(shares_count) FROM posts) as total_shares,
        (SELECT MAX(last_metrics_update) FROM posts) as latest_update;
END //

DELIMITER ;

-- Insert sample data if tables are empty (optional)
-- You can remove this section if you don't want sample data

-- Sample members
INSERT IGNORE INTO members (id, name, email, role) VALUES
('member_1', 'John Doe', '<EMAIL>', 'Admin'),
('member_2', 'Jane Smith', '<EMAIL>', 'Editor'),
('member_3', 'Mike Johnson', '<EMAIL>', 'Member');

-- Sample pages
INSERT IGNORE INTO pages (id, name, url, member_id) VALUES
('page_1', 'Sample Page 1', 'https://facebook.com/samplepage1', 'member_1'),
('page_2', 'Sample Page 2', 'https://facebook.com/samplepage2', 'member_2'),
('page_3', 'Sample Page 3', 'https://facebook.com/samplepage3', 'member_3');

-- Run initial sync
CALL SyncPostsFromJSON();
