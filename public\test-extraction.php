<?php
// Test Extraction Process
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

$response = ['success' => false, 'tests' => [], 'debug' => []];

// Test 1: Database Connection
$response['tests']['database'] = false;
try {
    $conn = new mysqli("localhost", "root", "", "facebook_db");
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    $response['tests']['database'] = true;
    $response['debug'][] = "✅ Database connection successful";
    
    // Test database tables
    $tables = ['members', 'pages', 'posts'];
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $response['debug'][] = "✅ Table '$table' exists";
        } else {
            $response['debug'][] = "❌ Table '$table' missing";
        }
    }
    
} catch (Exception $e) {
    $response['debug'][] = "❌ Database error: " . $e->getMessage();
}

// Test 2: Electron App Connection
$response['tests']['electron_app'] = false;
$electronUrl = 'http://localhost:3000';

// Test different ports
$ports = [3000, 3001, 8080, 5000];
foreach ($ports as $port) {
    $testUrl = "http://localhost:$port/api/posts";
    $response['debug'][] = "Testing Electron app on port $port...";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'method' => 'GET',
            'header' => 'Content-Type: application/json'
        ]
    ]);
    
    $data = @file_get_contents($testUrl, false, $context);
    if ($data !== false) {
        $posts = json_decode($data, true);
        if (is_array($posts)) {
            $response['tests']['electron_app'] = true;
            $response['debug'][] = "✅ Found Electron app on port $port with " . count($posts) . " posts";
            $response['electron_port'] = $port;
            $response['sample_post'] = isset($posts[0]) ? $posts[0] : null;
            break;
        }
    }
}

if (!$response['tests']['electron_app']) {
    $response['debug'][] = "❌ Could not connect to Electron app on any port";
    $response['debug'][] = "Make sure your Electron app is running and accessible";
}

// Test 3: API Endpoints
if ($response['tests']['electron_app']) {
    $port = $response['electron_port'];
    $endpoints = [
        'posts' => "/api/posts",
        'pages' => "/api/pages", 
        'team' => "/api/team"
    ];
    
    foreach ($endpoints as $name => $endpoint) {
        $url = "http://localhost:$port$endpoint";
        $data = @file_get_contents($url, false, $context);
        if ($data !== false) {
            $json = json_decode($data, true);
            if (is_array($json)) {
                $response['tests'][$name] = true;
                $response['debug'][] = "✅ $name endpoint working: " . count($json) . " items";
            } else {
                $response['tests'][$name] = false;
                $response['debug'][] = "❌ $name endpoint returned invalid JSON";
            }
        } else {
            $response['tests'][$name] = false;
            $response['debug'][] = "❌ $name endpoint not accessible";
        }
    }
}

// Overall success
$response['success'] = $response['tests']['database'] && $response['tests']['electron_app'];

if ($response['success']) {
    $response['message'] = "✅ All tests passed! Ready for extraction.";
} else {
    $response['message'] = "❌ Some tests failed. Check debug info.";
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
