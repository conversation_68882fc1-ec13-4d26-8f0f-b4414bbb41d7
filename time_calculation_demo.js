// Time Calculation Demonstration
// This shows exactly how the system calculates post times

console.log('='.repeat(60));
console.log('📅 POST TIME CALCULATION DEMONSTRATION');
console.log('='.repeat(60));

// Get current time
const now = new Date();
console.log(`🕐 Current Time: ${now.toLocaleString()}`);
console.log('');

// Test cases from your Facebook data
const testCases = [
    { relative: '25m', description: 'Post from Al Taghier Channel' },
    { relative: '2h', description: 'Typical recent post' },
    { relative: '1h', description: 'Post that was updated in logs' },
    { relative: '4d', description: 'Post from تشرين 24' },
    { relative: '132h', description: 'Older post (5+ days)' }
];

console.log('📊 CALCULATION EXAMPLES:');
console.log('-'.repeat(60));

testCases.forEach((testCase, index) => {
    const { relative, description } = testCase;
    
    // Parse the relative time
    const match = relative.match(/^(\d+)([smhd])$/i);
    if (!match) return;
    
    const value = parseInt(match[1]);
    const unit = match[2].toLowerCase();
    
    // Calculate the post time by subtracting from current time
    const postDate = new Date(now);
    
    switch (unit) {
        case 's': // seconds
            postDate.setSeconds(now.getSeconds() - value);
            break;
        case 'm': // minutes
            postDate.setMinutes(now.getMinutes() - value);
            break;
        case 'h': // hours
            postDate.setHours(now.getHours() - value);
            break;
        case 'd': // days
            postDate.setDate(now.getDate() - value);
            break;
    }
    
    // Check if it's today
    const isToday = postDate.getDate() === now.getDate() &&
                    postDate.getMonth() === now.getMonth() &&
                    postDate.getFullYear() === now.getFullYear();
    
    const timeString = postDate.toLocaleTimeString('en-US', { 
        hour: 'numeric',
        minute: '2-digit',
        hour12: true 
    });
    
    const dateString = postDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
    
    // Format the final display
    const finalDisplay = isToday ? 
        `${relative} (Today at ${timeString})` : 
        `${relative} (${dateString} at ${timeString})`;
    
    console.log(`${index + 1}. ${description}`);
    console.log(`   Facebook shows: "${relative}"`);
    console.log(`   Calculation: ${now.toLocaleString()} - ${value} ${unit === 'm' ? 'minutes' : unit === 'h' ? 'hours' : unit === 'd' ? 'days' : 'seconds'}`);
    console.log(`   Result: ${postDate.toLocaleString()}`);
    console.log(`   Display: "${finalDisplay}"`);
    console.log('');
});

console.log('✅ KEY POINTS:');
console.log('• System takes Facebook\'s relative time (like "25m")');
console.log('• Subtracts that amount from the CURRENT time');
console.log('• Shows both the original time AND the calculated exact time');
console.log('• Updates in real-time as current time changes');
console.log('');

console.log('🔄 LIVE EXAMPLE:');
console.log('If Facebook shows "25m" and current time is 8:30 PM:');
console.log('Calculation: 8:30 PM - 25 minutes = 8:05 PM');
console.log('Display: "25m (Today at 8:05 PM)"');
console.log('');

console.log('⚡ This calculation happens automatically during metrics updates!');
console.log('='.repeat(60));
