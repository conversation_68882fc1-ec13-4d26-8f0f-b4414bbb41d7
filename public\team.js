// Global variables
let teamMembers = [];
let pages = [];
let selectedMember = null;
let editingMember = null;

// Add global error handler for fetch requests
const originalFetch = window.fetch;
window.fetch = function(...args) {
    return originalFetch.apply(this, args)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response;
        })
        .catch(error => {
            console.error('Fetch error:', error);
            if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                showNotification('Server connection lost - please check if the server is running', 'error');
            } else if (error.message.includes('HTTP 408')) {
                showNotification('Request timeout - operation took too long', 'warning');
            } else if (error.message.includes('HTTP 500')) {
                showNotification('Server error - please try again', 'error');
            }
            throw error;
        });
};

// Function to check server health
async function checkServerHealth() {
    try {
        const response = await fetch('/api/health');
        const health = await response.json();
        console.log('Server health:', health);

        if (health.status === 'healthy') {
            return true;
        } else {
            console.warn('Server is unhealthy:', health);
            showNotification('Server is experiencing issues', 'warning');
            return false;
        }
    } catch (error) {
        console.error('Health check failed:', error);
        showNotification('Cannot connect to server', 'error');
        return false;
    }
}

// Check server health on page load
document.addEventListener('DOMContentLoaded', () => {
    checkServerHealth();
    // Check health every 5 minutes
    setInterval(checkServerHealth, 5 * 60 * 1000);
});
let currentMemberPosts = []; // Store all posts for current member
let currentTimeFilter = 'all'; // Current time filter
let isMemberScraping = false;
let currentMemberScraping = null;
let allPosts = []; // Store all posts for statistics
let memberStats = {}; // Store calculated member statistics

// Pagination variables
let currentPage = 1;
const postsPerPage = 10;
let totalPages = 1;
let currentFilteredPosts = [];

// Function to update all time-ago indicators
function updateTimeAgoIndicators() {
    const indicators = document.querySelectorAll('.time-ago');
    indicators.forEach(indicator => {
        const container = indicator.closest('.metrics-updated-indicator');
        if (container) {
            const timestamp = container.getAttribute('data-timestamp');
            if (timestamp) {
                indicator.textContent = getTimeAgo(timestamp);
            }
        }
    });
}

// Start real-time updates for time indicators
function startTimeUpdates() {
    // Update every minute
    setInterval(updateTimeAgoIndicators, 60000);
}

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    initializeTheme();
    loadTeamMembers();
    loadPages();
    loadStats();
    checkMemberScrapingStatus();
    updatePostsPerPageSetting(); // Load current posts per page setting
    checkMemberMetricsStatus(); // Check if metrics update is running
    startTimeUpdates(); // Start real-time time updates
});

// Load team members from API
async function loadTeamMembers() {
    try {
        const response = await fetch('/api/team');
        if (response.ok) {
            teamMembers = await response.json();
            calculateMemberStats(); // Recalculate stats when members are loaded
            renderTeamMembers();
            updateStats();
            updateBestPerformer(); // Update best performer when members change
            initializeWithUrlParams(); // Check for URL parameters after loading
        } else {
            console.error('Failed to load team members');
        }
    } catch (error) {
        console.error('Error loading team members:', error);
    }
}

// Load pages from API
async function loadPages() {
    try {
        const response = await fetch('/api/pages');
        if (response.ok) {
            pages = await response.json();
            updateStats();
        } else {
            console.error('Failed to load pages');
        }
    } catch (error) {
        console.error('Error loading pages:', error);
    }
}

// Load general stats
async function loadStats() {
    try {
        const response = await fetch('/api/posts');
        if (response.ok) {
            allPosts = await response.json();
            calculateOverallStats();
            calculateMemberStats();
            updateBestPerformer();
        }
    } catch (error) {
        console.error('Error loading posts stats:', error);
    }
}

// Calculate overall statistics
function calculateOverallStats() {
    const totalPosts = allPosts.length;

    // Update UI
    document.getElementById('totalPosts').textContent = formatNumber(totalPosts);
}

// Calculate statistics for each member
function calculateMemberStats() {
    memberStats = {};

    teamMembers.forEach(member => {
        const memberPosts = getMemberPosts(member);
        const stats = calculateMemberStatistics(memberPosts);
        memberStats[member.id] = stats;
    });
}

// Get posts for a specific member based on assigned pages
function getMemberPosts(member) {
    if (!member.assignedPages || member.assignedPages.length === 0) {
        return [];
    }

    return allPosts.filter(post => {
        return member.assignedPages.some(assignedPage => {
            const pageUrl = typeof assignedPage === 'string' ? assignedPage : assignedPage.link;
            return post.pageUrl && post.pageUrl.includes(pageUrl);
        });
    });
}

// Calculate detailed statistics for a member
function calculateMemberStatistics(posts) {
    const stats = {
        totalPosts: posts.length,
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0,
        totalViews: 0,
        avgLikes: 0,
        avgComments: 0,
        avgEngagement: 0,
        bestPost: null,
        recentPosts: 0,
        performance: 'average'
    };

    if (posts.length === 0) {
        return stats;
    }

    let maxEngagement = 0;
    const now = new Date();
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    posts.forEach(post => {
        if (post.engagement) {
            const likes = post.engagement.likes || 0;
            const comments = post.engagement.comments || 0;
            const shares = post.engagement.shares || 0;
            const views = post.engagement.views || 0;

            stats.totalLikes += likes;
            stats.totalComments += comments;
            stats.totalShares += shares;
            stats.totalViews += views;

            const totalEngagement = likes + comments + shares;
            if (totalEngagement > maxEngagement) {
                maxEngagement = totalEngagement;
                stats.bestPost = post;
            }
        }

        // Count recent posts (last 7 days)
        if (new Date(post.timestamp) >= weekAgo) {
            stats.recentPosts++;
        }
    });

    // Calculate averages
    stats.avgLikes = Math.round(stats.totalLikes / posts.length);
    stats.avgComments = Math.round(stats.totalComments / posts.length);
    stats.avgEngagement = Math.round((stats.totalLikes + stats.totalComments + stats.totalShares) / posts.length);

    // Determine performance level
    stats.performance = getPerformanceLevel(stats);

    return stats;
}

// Determine performance level based on statistics
function getPerformanceLevel(stats) {
    const engagementScore = stats.avgEngagement;
    const postFrequency = stats.recentPosts;

    if (engagementScore >= 100 && postFrequency >= 3) {
        return 'excellent';
    } else if (engagementScore >= 50 && postFrequency >= 2) {
        return 'good';
    } else if (engagementScore >= 20 || postFrequency >= 1) {
        return 'average';
    } else {
        return 'low';
    }
}

// Update stats display
function updateStats() {
    document.getElementById('totalMembers').textContent = teamMembers.length;
    document.getElementById('totalPages').textContent = pages.length;
    
    // Calculate assigned pages (unique pages assigned to any member)
    const assignedPagesSet = new Set();
    teamMembers.forEach(member => {
        member.assignedPages.forEach(page => {
            if (typeof page === 'string') {
                assignedPagesSet.add(page);
            } else if (page.link) {
                assignedPagesSet.add(page.link);
            }
        });
    });
    document.getElementById('assignedPages').textContent = assignedPagesSet.size;
}

// Render team members list
function renderTeamMembers() {
    const container = document.getElementById('teamMembersList');

    if (teamMembers.length === 0) {
        container.innerHTML = `
            <div style="text-align: center; padding: 20px; color: var(--text-muted);">
                <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 10px;"></i>
                <p>No team members yet. Add your first team member!</p>
            </div>
        `;
        return;
    }

    container.innerHTML = teamMembers.map(member => {
        const stats = memberStats[member.id] || {};
        const performanceBadge = getPerformanceBadge(stats.performance);

        return `
            <div class="member-card ${selectedMember?.id === member.id ? 'selected' : ''}"
                 onclick="selectMember('${member.id}')">
                <div class="member-info">
                    <div class="member-details">
                        <h4>
                            ${member.name}
                            ${performanceBadge}
                        </h4>
                        <p>${member.role}${member.email ? ` • ${member.email}` : ''}</p>
                    </div>
                    <div class="member-actions">
                        <button class="btn-icon scrape" onclick="scrapeMember('${member.id}', event)" title="Scrape Member Pages (Latest Posts)" ${member.assignedPages.length === 0 ? 'disabled' : ''}>
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn-icon scrape-multiple" onclick="scrapeMultipleMemberPosts('${member.id}', event)" title="Scrape ${currentPostsPerPage} Posts from Each Page" ${member.assignedPages.length === 0 ? 'disabled' : ''}>
                            <i class="fas fa-layer-group"></i>
                        </button>
                        <button class="btn-icon delete-posts" onclick="deleteMemberPosts('${member.id}', event)" title="Delete All Posts for this Member">
                            <i class="fas fa-broom"></i>
                        </button>
                        <button class="btn-icon edit" onclick="editMember('${member.id}', event)" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon delete" onclick="deleteMember('${member.id}', event)" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                ${member.assignedPages.length > 0 ? `
                    <div class="assigned-pages">
                        <strong>Pages:</strong>
                        ${member.assignedPages.map(page => {
                            const pageName = typeof page === 'string' ?
                                (pages.find(p => p.link.includes(page))?.name || 'Unknown Page') :
                                (page.name || 'Unknown Page');
                            return `<span class="page-tag">${pageName}</span>`;
                        }).join('')}
                    </div>
                ` : ''}
                ${stats.totalPosts > 0 ? `
                    <div class="member-stats">
                        <div class="member-stat">
                            <div class="member-stat-number">${formatNumber(stats.totalPosts)}</div>
                            <div class="member-stat-label">Posts</div>
                        </div>
                        <div class="member-stat">
                            <div class="member-stat-number">${formatNumber(stats.totalLikes)}</div>
                            <div class="member-stat-label">Likes</div>
                        </div>
                        <div class="member-stat">
                            <div class="member-stat-number">${formatNumber(stats.totalComments)}</div>
                            <div class="member-stat-label">Comments</div>
                        </div>
                        <div class="member-stat">
                            <div class="member-stat-number">${formatNumber(stats.avgEngagement)}</div>
                            <div class="member-stat-label">Avg Eng.</div>
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }).join('');
}

// Get performance badge HTML
function getPerformanceBadge(performance) {
    const badges = {
        excellent: '<span class="member-performance-badge performance-excellent"><i class="fas fa-star"></i> Excellent</span>',
        good: '<span class="member-performance-badge performance-good"><i class="fas fa-thumbs-up"></i> Good</span>',
        average: '<span class="member-performance-badge performance-average"><i class="fas fa-minus"></i> Average</span>',
        low: '<span class="member-performance-badge performance-low"><i class="fas fa-arrow-down"></i> Low</span>'
    };

    return badges[performance] || badges.average;
}

// Update best performer section
function updateBestPerformer() {
    const period = document.getElementById('performancePeriod')?.value || 'all';
    const bestPerformerSection = document.getElementById('bestPerformerSection');
    const bestPerformerContent = document.getElementById('bestPerformerContent');

    if (teamMembers.length === 0 || Object.keys(memberStats).length === 0) {
        bestPerformerSection.style.display = 'none';
        return;
    }

    // Use the already calculated memberStats for better accuracy
    // Find best performer based on overall performance
    let bestMember = null;
    let bestScore = -1;

    teamMembers.forEach(member => {
        const stats = memberStats[member.id];
        if (stats && stats.totalPosts > 0) {
            // Calculate performance score balancing volume and engagement
            // Give significant weight to total posts (volume of work) and total engagement
            const totalEngagement = stats.totalLikes + stats.totalComments + (stats.totalShares || 0);
            const score = (stats.totalPosts * 0.4) + (totalEngagement * 0.4) + (stats.avgEngagement * 0.2);
            console.log(`${member.name}: Score=${score.toFixed(2)}, Posts=${stats.totalPosts}, TotalEng=${totalEngagement}, AvgEng=${stats.avgEngagement}`);

            if (score > bestScore) {
                bestScore = score;
                bestMember = { member, stats };
            }
        }
    });

    if (!bestMember) {
        bestPerformerContent.innerHTML = `
            <div style="text-align: center; padding: 20px; color: rgba(255, 255, 255, 0.8);">
                <i class="fas fa-chart-line" style="font-size: 2rem; margin-bottom: 10px;"></i>
                <p>No performance data available for ${getPeriodDisplayName(period)}</p>
            </div>
        `;
        bestPerformerSection.style.display = 'block';
        return;
    }

    const { member, stats } = bestMember;
    const avatar = member.name.charAt(0).toUpperCase();

    bestPerformerContent.innerHTML = `
        <div class="performer-info">
            <div class="performer-avatar">
                ${avatar}
            </div>
            <div class="performer-details">
                <h4>${member.name}</h4>
                <p>${member.role} • ${stats.totalPosts} posts all time</p>
            </div>
            <div class="performer-stats">
                <div class="performer-stat">
                    <div class="performer-stat-number">${formatNumber(stats.totalLikes)}</div>
                    <div class="performer-stat-label">Likes</div>
                </div>
                <div class="performer-stat">
                    <div class="performer-stat-number">${formatNumber(stats.totalComments)}</div>
                    <div class="performer-stat-label">Comments</div>
                </div>
                <div class="performer-stat">
                    <div class="performer-stat-number">${formatNumber(stats.avgEngagement)}</div>
                    <div class="performer-stat-label">Avg Engagement</div>
                </div>
            </div>
        </div>
    `;

    bestPerformerSection.style.display = 'block';
}

// Filter posts by time period
function filterPostsByPeriod(posts, period) {
    if (period === 'all') {
        return posts;
    }

    const now = new Date();
    let startDate;

    switch (period) {
        case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            break;
        case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
        default:
            return posts;
    }

    return posts.filter(post => new Date(post.timestamp) >= startDate);
}

// Get period display name
function getPeriodDisplayName(period) {
    const names = {
        all: 'All Time',
        today: 'Today',
        week: 'This Week',
        month: 'This Month'
    };
    return names[period] || 'All Time';
}

// Format numbers for display (e.g., 1000 -> 1K, 1000000 -> 1M)
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

// Refresh team statistics in real-time
async function refreshTeamStatistics() {
    try {
        console.log('Refreshing team statistics...');

        // Reload all posts data
        console.log('Fetching posts data...');
        const response = await fetch('/api/posts');
        console.log('Posts response status:', response.status);

        if (response.ok) {
            const newPosts = await response.json();
            console.log('Loaded posts:', newPosts.length);
            allPosts = newPosts;

            // Recalculate all statistics
            console.log('Calculating overall stats...');
            calculateOverallStats();

            console.log('Calculating member stats...');
            calculateMemberStats();

            // Update member cards with new statistics
            console.log('Rendering team members...');
            renderTeamMembers();

            // Update best performer
            console.log('Updating best performer...');
            updateBestPerformer();

            // Update general stats
            updateStats();

            console.log('Team statistics refreshed successfully - Posts:', allPosts.length, 'Members:', teamMembers.length);
        } else {
            console.error('Failed to fetch posts:', response.status, response.statusText);
            throw new Error(`Failed to fetch posts: ${response.status}`);
        }
    } catch (error) {
        console.error('Error refreshing team statistics:', error);
        throw error; // Re-throw to be caught by caller
    }
}

// Manual refresh function with safety checks
async function manualRefreshStats() {
    console.log('=== MANUAL REFRESH STATS CALLED ===');

    const refreshBtn = document.getElementById('refreshStatsBtn');

    if (!refreshBtn) {
        console.error('Refresh button not found');
        showNotification('Error: Refresh button not found', 'error');
        return;
    }

    try {
        console.log('Starting manual refresh...');

        // Show loading state immediately
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Refreshing...';

        // Check if any metrics updates are currently running
        const [bulkStatus, memberStatus] = await Promise.all([
            fetch('/api/posts/bulk-metrics-status').then(r => r.json()).catch(e => ({ isRunning: false })),
            fetch('/api/member-metrics-status').then(r => r.json()).catch(e => ({ isRunning: false }))
        ]);

        if (bulkStatus.isRunning || memberStatus.isRunning) {
            showNotification('Cannot refresh while metrics update is running. Please wait for completion.', 'warning');
            return;
        }

        // Perform full refresh
        await refreshTeamStatistics();

        // If a member is currently selected, reload their posts too
        if (selectedMember) {
            console.log('Reloading posts for selected member:', selectedMember.name);
            await loadMemberPosts(selectedMember.id);
        }

        // Show success notification
        showNotification('Team statistics refreshed successfully!', 'success');
        console.log('=== REFRESH COMPLETED SUCCESSFULLY ===');

    } catch (error) {
        console.error('Error during manual refresh:', error);
        showNotification('Error refreshing statistics. Please try again.', 'error');
    } finally {
        // Reset button state
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Refresh Stats';
        }
    }
}

// Select a team member
async function selectMember(memberId) {
    const previousMember = selectedMember;
    selectedMember = teamMembers.find(m => m.id === memberId);
    renderTeamMembers(); // Re-render to show selection

    // Smart button state management when switching members
    if (!isMemberMetricsUpdating) {
        // No update running, safe to reset to normal state
        resetMetricsUpdateButton();
    } else {
        // Update is running, check if it's for the newly selected member
        console.log(`Metrics update is running for: ${currentMetricsUpdateMember}, switching to: ${selectedMember?.name}`);

        const updateBtn = document.getElementById('updateMemberMetricsBtn');
        if (updateBtn) {
            if (currentMetricsUpdateMember === selectedMember?.name) {
                // Switching to the member being updated - show disabled updating state
                console.log('Switching to member being updated - showing disabled updating state');
                updateBtn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Updating...';
                updateBtn.classList.add('disabled');
                updateBtn.disabled = true;
            } else {
                // Switching to a different member - show normal button (but metrics update is running for another member)
                console.log('Switching to different member - showing normal button');
                updateBtn.classList.remove('updating', 'stopping', 'disabled');
                updateBtn.disabled = false;
                updateBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Update Metrics';
            }
        }
    }

    if (selectedMember) {
        await loadMemberPosts(memberId);
    }
}

// Load posts for selected member
async function loadMemberPosts(memberId) {
    try {
        const response = await fetch(`/api/team/${memberId}/posts`);
        if (response.ok) {
            const data = await response.json();
            renderMemberPosts(data.member, data.posts);
        } else {
            console.error('Failed to load member posts');
        }
    } catch (error) {
        console.error('Error loading member posts:', error);
    }
}

// Render member posts
function renderMemberPosts(member, posts) {
    const title = document.getElementById('memberPostsTitle');
    const content = document.getElementById('memberPostsContent');
    const exportControls = document.getElementById('memberExportControls');

    // Store posts for filtering and reset pagination
    currentMemberPosts = posts;
    currentTimeFilter = 'all'; // Reset filter when switching members
    resetPagination(); // Reset pagination when switching members

    title.innerHTML = `<i class="fas fa-file-alt"></i> ${member.name}'s Posts (${posts.length})`;

    // Show export controls if member has posts
    if (posts.length > 0) {
        exportControls.style.display = 'block';
        setupExportDropdown();
        updateTimeBasedStats(posts);
        resetTimeFilterButtons(); // Reset filter button states
    } else {
        exportControls.style.display = 'none';
    }

    if (posts.length === 0) {
        content.innerHTML = `
            <div class="no-selection">
                <i class="fas fa-inbox" style="font-size: 3rem; color: var(--text-muted);"></i>
                <p>No posts found for ${member.name}</p>
                <p style="font-size: 0.9rem; color: var(--text-muted);">
                    ${member.assignedPages.length === 0 ?
                        'This member has no assigned pages.' :
                        'Posts will appear here when the scraper finds content from assigned pages.'}
                </p>
            </div>
        `;
        return;
    }

    // Use pagination for rendering posts
    renderFilteredPosts(posts);
}

// Utility functions
function getPageName(pageUrl) {
    const page = pages.find(p => p.link === pageUrl);
    return page ? page.name : 'Unknown Page';
}

function formatDate(timestamp) {
    const date = new Date(timestamp);
    const options = {
        weekday: 'short', // Mon, Tue, Wed, etc.
        year: 'numeric',
        month: 'numeric',
        day: 'numeric'
    };
    return date.toLocaleDateString('en-US', options);
}

// Client-side function to calculate actual post time from relative time
function calculateActualPostTimeClient(relativeTime) {
    if (!relativeTime) return null;

    // Handle different relative time formats
    let match = relativeTime.match(/^(\d+)([smhd])$/i);
    if (!match) {
        // Try to match longer formats like "25 minutes ago", "2 hours ago"
        match = relativeTime.match(/(\d+)\s*(second|minute|hour|day)s?\s*ago/i);
        if (match) {
            const unit = match[2].toLowerCase().charAt(0); // Get first letter (s, m, h, d)
            match = [match[0], match[1], unit];
        }
    }

    if (!match) return null;

    const value = parseInt(match[1]);
    const unit = match[2].toLowerCase();

    // Create a date object for the current time
    const now = new Date();
    const postDate = new Date(now);

    // Subtract the appropriate amount of time based on the unit
    switch (unit) {
        case 's': // seconds
            postDate.setSeconds(now.getSeconds() - value);
            break;
        case 'm': // minutes
            postDate.setMinutes(now.getMinutes() - value);
            break;
        case 'h': // hours
            postDate.setHours(now.getHours() - value);
            break;
        case 'd': // days
            postDate.setDate(now.getDate() - value);
            break;
        default:
            return null;
    }

    // Check if it's today
    const today = new Date();
    const isToday = postDate.getDate() === today.getDate() &&
                    postDate.getMonth() === today.getMonth() &&
                    postDate.getFullYear() === today.getFullYear();

    const timeString = postDate.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });

    const dateString = postDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });

    if (isToday) {
        return `${relativeTime} (Today at ${timeString})`;
    } else {
        return `${relativeTime} (${dateString} at ${timeString})`;
    }
}

// Helper function to get actual date object from relative time
function getActualPostDate(relativeTime) {
    if (!relativeTime) return null;

    // Handle different relative time formats
    let match = relativeTime.match(/^(\d+)([smhd])$/i);
    if (!match) {
        // Try to match longer formats like "25 minutes ago", "2 hours ago"
        match = relativeTime.match(/(\d+)\s*(second|minute|hour|day)s?\s*ago/i);
        if (match) {
            const unit = match[2].toLowerCase().charAt(0); // Get first letter (s, m, h, d)
            match = [match[0], match[1], unit];
        }
    }

    if (!match) return null;

    const value = parseInt(match[1]);
    const unit = match[2].toLowerCase();

    // Create a date object for the current time
    const now = new Date();
    const postDate = new Date(now);

    // Subtract the appropriate amount of time based on the unit
    switch (unit) {
        case 's': // seconds
            postDate.setSeconds(now.getSeconds() - value);
            break;
        case 'm': // minutes
            postDate.setMinutes(now.getMinutes() - value);
            break;
        case 'h': // hours
            postDate.setHours(now.getHours() - value);
            break;
        case 'd': // days
            postDate.setDate(now.getDate() - value);
            break;
        default:
            return null;
    }

    return postDate;
}

// Combined display function for client-side calculation
function calculateActualPostTimeClientCombined(relativeTime) {
    if (!relativeTime) return null;

    // Handle different relative time formats
    let match = relativeTime.match(/^(\d+)([smhd])$/i);
    if (!match) {
        // Try to match longer formats like "25 minutes ago", "2 hours ago"
        match = relativeTime.match(/(\d+)\s*(second|minute|hour|day)s?\s*ago/i);
        if (match) {
            const unit = match[2].toLowerCase().charAt(0); // Get first letter (s, m, h, d)
            match = [match[0], match[1], unit];
        }
    }

    if (!match) return null;

    const value = parseInt(match[1]);
    const unit = match[2].toLowerCase();

    // Create a date object for the current time
    const now = new Date();
    const postDate = new Date(now);

    // Subtract the appropriate amount of time based on the unit
    switch (unit) {
        case 's': // seconds
            postDate.setSeconds(now.getSeconds() - value);
            break;
        case 'm': // minutes
            postDate.setMinutes(now.getMinutes() - value);
            break;
        case 'h': // hours
            postDate.setHours(now.getHours() - value);
            break;
        case 'd': // days
            postDate.setDate(now.getDate() - value);
            break;
        default:
            return null;
    }

    // Check if it's today
    const today = new Date();
    const isToday = postDate.getDate() === today.getDate() &&
                    postDate.getMonth() === today.getMonth() &&
                    postDate.getFullYear() === today.getFullYear();

    const timeString = postDate.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });

    const dateString = postDate.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });

    if (isToday) {
        return `${relativeTime} (Today at ${timeString})`;
    } else {
        return `${relativeTime} (${dateString} at ${timeString})`;
    }
}

function getTimeAgo(timestamp) {
    // Handle invalid timestamps
    if (!timestamp || isNaN(new Date(timestamp).getTime())) {
        return 'Unknown';
    }

    const now = new Date();
    const past = new Date(timestamp);
    const diffInSeconds = Math.floor((now - past) / 1000);

    // Handle negative differences (future dates)
    if (diffInSeconds < 0) {
        return 'just now';
    }

    if (diffInSeconds < 60) {
        return 'just now';
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
        return `${diffInMinutes}m ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
        return `${diffInHours}h ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) {
        return `${diffInDays}d ago`;
    }

    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) {
        return `${diffInWeeks}w ago`;
    }

    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
        return `${diffInMonths}mo ago`;
    }

    const diffInYears = Math.floor(diffInDays / 365);
    return `${diffInYears}y ago`;
}

function formatNumber(num) {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// Update time-based statistics
function updateTimeBasedStats(posts) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekStart = new Date(today);
    // Start of this week (Saturday) - if today is Saturday (6), start is today, otherwise go back to previous Saturday
    const daysSinceSaturday = (today.getDay() + 1) % 7; // Saturday = 0, Sunday = 1, Monday = 2, etc.
    weekStart.setDate(today.getDate() - daysSinceSaturday);
    const monthStart = new Date(now.getFullYear(), now.getMonth(), 1); // Start of this month

    let todayCount = 0;
    let weekCount = 0;
    let monthCount = 0;

    posts.forEach(post => {
        // Use enhanced post time if available, otherwise fall back to timestamp
        let postDate;

        if (post.enhancedPostTime && post.enhancedPostTime.fullDate) {
            // Use the calculated Facebook post time
            postDate = new Date(post.enhancedPostTime.fullDate);
        } else if (post.postTime && post.postTime !== 'Unknown') {
            // Calculate post time from relative time
            postDate = getActualPostDate(post.postTime);
            if (!postDate) {
                // Fallback to scraping timestamp if calculation fails
                postDate = new Date(post.timestamp);
            }
        } else {
            // Fallback to scraping timestamp
            postDate = new Date(post.timestamp);
        }

        const postDateOnly = new Date(postDate.getFullYear(), postDate.getMonth(), postDate.getDate());

        // Count today's posts
        if (postDateOnly.getTime() === today.getTime()) {
            todayCount++;
        }

        // Count this week's posts
        if (postDate >= weekStart) {
            weekCount++;
        }

        // Count this month's posts
        if (postDate >= monthStart) {
            monthCount++;
        }
    });

    // Update the UI
    const allElement = document.getElementById('allCount');
    const todayElement = document.getElementById('todayCount');
    const weekElement = document.getElementById('weekCount');
    const monthElement = document.getElementById('monthCount');

    if (allElement) {
        allElement.textContent = posts.length;
    }

    if (todayElement) {
        todayElement.textContent = todayCount;
        // Highlight if there are posts today
        const todayStatElement = todayElement.closest('.time-stat');
        if (todayCount > 0) {
            todayStatElement.classList.add('highlight');
        } else {
            todayStatElement.classList.remove('highlight');
        }
    }

    if (weekElement) {
        weekElement.textContent = weekCount;
        // Highlight if there are posts this week
        const weekStatElement = weekElement.closest('.time-stat');
        if (weekCount > 0) {
            weekStatElement.classList.add('highlight');
        } else {
            weekStatElement.classList.remove('highlight');
        }
    }

    if (monthElement) {
        monthElement.textContent = monthCount;
        // Highlight if there are posts this month
        const monthStatElement = monthElement.closest('.time-stat');
        if (monthCount > 0) {
            monthStatElement.classList.add('highlight');
        } else {
            monthStatElement.classList.remove('highlight');
        }
    }

    console.log(`Time-based stats for ${selectedMember?.name}: Today: ${todayCount}, Week: ${weekCount}, Month: ${monthCount}`);
}

// Modal functions
function openAddMemberModal() {
    editingMember = null;
    document.getElementById('modalTitle').textContent = 'Add Team Member';
    document.getElementById('memberForm').reset();
    loadPagesForAssignment();
    document.getElementById('memberModal').style.display = 'block';
}

function editMember(memberId, event) {
    event.stopPropagation();
    editingMember = teamMembers.find(m => m.id === memberId);
    
    document.getElementById('modalTitle').textContent = 'Edit Team Member';
    document.getElementById('memberName').value = editingMember.name;
    document.getElementById('memberEmail').value = editingMember.email || '';
    document.getElementById('memberRole').value = editingMember.role;
    
    loadPagesForAssignment(editingMember.assignedPages);
    document.getElementById('memberModal').style.display = 'block';
}

function closeMemberModal() {
    document.getElementById('memberModal').style.display = 'none';
    editingMember = null;
}

function loadPagesForAssignment(assignedPages = []) {
    const container = document.getElementById('pagesAssignment');
    
    if (pages.length === 0) {
        container.innerHTML = '<p style="color: var(--text-muted);">No pages available. Add pages first.</p>';
        return;
    }
    
    container.innerHTML = pages.map(page => {
        const isAssigned = assignedPages.some(assigned => {
            if (typeof assigned === 'string') {
                return page.link.includes(assigned);
            }
            return assigned.link === page.link;
        });
        
        return `
            <div class="page-checkbox">
                <input type="checkbox" id="page_${page.link}" value="${page.link}" ${isAssigned ? 'checked' : ''}>
                <label for="page_${page.link}">${page.name}</label>
            </div>
        `;
    }).join('');
}

// Form submission
document.getElementById('memberForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const name = document.getElementById('memberName').value.trim();
    const email = document.getElementById('memberEmail').value.trim();
    const role = document.getElementById('memberRole').value;
    
    // Get assigned pages
    const assignedPages = [];
    document.querySelectorAll('#pagesAssignment input[type="checkbox"]:checked').forEach(checkbox => {
        const page = pages.find(p => p.link === checkbox.value);
        if (page) {
            assignedPages.push(page);
        }
    });
    
    const memberData = { name, email, role, assignedPages };
    
    try {
        let response;
        if (editingMember) {
            // Update existing member
            response = await fetch(`/api/team/${editingMember.id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(memberData)
            });
        } else {
            // Add new member
            response = await fetch('/api/team', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(memberData)
            });
        }
        
        if (response.ok) {
            closeMemberModal();
            await loadTeamMembers();
            showNotification(editingMember ? 'Member updated successfully!' : 'Member added successfully!', 'success');
        } else {
            const error = await response.json();
            showNotification(error.error || 'Failed to save member', 'error');
        }
    } catch (error) {
        console.error('Error saving member:', error);
        showNotification('Error saving member', 'error');
    }
});

// Delete member
async function deleteMember(memberId, event) {
    event.stopPropagation();
    
    const member = teamMembers.find(m => m.id === memberId);
    if (!confirm(`Are you sure you want to delete ${member.name}?`)) {
        return;
    }
    
    try {
        const response = await fetch(`/api/team/${memberId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            await loadTeamMembers();
            if (selectedMember?.id === memberId) {
                selectedMember = null;
                document.getElementById('memberPostsContent').innerHTML = `
                    <div class="no-selection">
                        <i class="fas fa-user-circle" style="font-size: 3rem; color: var(--text-muted);"></i>
                        <p>Select a team member to view their assigned pages and posts</p>
                    </div>
                `;
                document.getElementById('memberPostsTitle').innerHTML = '<i class="fas fa-file-alt"></i> Select a Member';
            }
            showNotification('Member deleted successfully!', 'success');
        } else {
            showNotification('Failed to delete member', 'error');
        }
    } catch (error) {
        console.error('Error deleting member:', error);
        showNotification('Error deleting member', 'error');
    }
}

// Notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: bold;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;
    
    // Set background color based on type
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#28a745';
            break;
        case 'error':
            notification.style.backgroundColor = '#dc3545';
            break;
        default:
            notification.style.backgroundColor = '#007bff';
    }
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Theme management functions
function initializeTheme() {
    const themeToggle = document.querySelector('.theme-toggle');
    const savedTheme = localStorage.getItem('darkMode');

    if (savedTheme === 'true') {
        document.body.classList.add('dark-mode');
        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
    } else {
        themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
    }
}

function toggleTheme() {
    const themeToggle = document.querySelector('.theme-toggle');
    const isDarkMode = document.body.classList.toggle('dark-mode');

    if (isDarkMode) {
        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        localStorage.setItem('darkMode', 'true');
    } else {
        themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        localStorage.setItem('darkMode', 'false');
    }
}

// Export functionality
function setupExportDropdown() {
    const exportDropdown = document.querySelector('.export-dropdown-team');
    if (!exportDropdown) return;

    let dropdownTimeout;

    // Remove existing event listeners to prevent duplicates
    exportDropdown.replaceWith(exportDropdown.cloneNode(true));
    const newDropdown = document.querySelector('.export-dropdown-team');

    // Show dropdown on hover
    newDropdown.addEventListener('mouseenter', () => {
        clearTimeout(dropdownTimeout);
        newDropdown.classList.add('active');
    });

    // Hide dropdown when mouse leaves, but with a delay
    newDropdown.addEventListener('mouseleave', () => {
        dropdownTimeout = setTimeout(() => {
            newDropdown.classList.remove('active');
        }, 200);
    });

    // Keep dropdown open when hovering over options
    const exportOptions = newDropdown.querySelector('.export-options-team');
    if (exportOptions) {
        exportOptions.addEventListener('mouseenter', () => {
            clearTimeout(dropdownTimeout);
        });

        exportOptions.addEventListener('mouseleave', () => {
            dropdownTimeout = setTimeout(() => {
                newDropdown.classList.remove('active');
            }, 200);
        });
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
        if (!newDropdown.contains(e.target)) {
            newDropdown.classList.remove('active');
        }
    });
}

// Export member data function
async function exportMemberData(format) {
    if (!selectedMember) {
        showNotification('No member selected', 'error');
        return;
    }

    try {
        // Close dropdown
        const dropdown = document.querySelector('.export-dropdown-team');
        if (dropdown) {
            dropdown.classList.remove('active');
        }

        // Show loading state
        showNotification(`Exporting ${selectedMember.name}'s data as ${format.toUpperCase()}...`, 'info');

        const response = await fetch(`/api/team/export-member/${selectedMember.id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ format })
        });

        const result = await response.json();

        if (result.success) {
            if (result.postsCount === 0) {
                showNotification(`${result.memberName} has no posts to export`, 'info');
            } else {
                // Show detailed success message
                const message = `✅ ${format.toUpperCase()} Export Completed!\n\n` +
                    `Member: ${result.memberName}\n` +
                    `Posts: ${result.postsCount}\n` +
                    `Likes: ${result.totalEngagement.likes.toLocaleString()}\n` +
                    `Comments: ${result.totalEngagement.comments.toLocaleString()}\n` +
                    `Shares: ${result.totalEngagement.shares.toLocaleString()}\n\n` +
                    `File: ${result.fileName}\n` +
                    `Location: ${result.exportDirectory}`;

                showNotification(`${result.memberName}'s data exported successfully!`, 'success');

                // Show detailed info in console and alert
                console.log('📊 MEMBER DATA EXPORT COMPLETED 📊');
                console.log('='.repeat(50));
                console.log(message);
                console.log('='.repeat(50));

                alert(message);
            }
        } else {
            showNotification(`Failed to export ${selectedMember.name}'s data: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error exporting member data:', error);
        showNotification(`Error exporting ${selectedMember.name}'s data: ${error.message}`, 'error');
    }
}

// Time-based filtering functions
function filterPostsByTime(timeFilter) {
    if (!currentMemberPosts || currentMemberPosts.length === 0) return;

    currentTimeFilter = timeFilter;

    // Reset pagination when filter changes
    resetPagination();

    // Update button states
    document.querySelectorAll('.time-stat').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-filter="${timeFilter}"]`).classList.add('active');

    // Filter posts based on time period
    let filteredPosts = [];

    if (timeFilter === 'all') {
        filteredPosts = currentMemberPosts;
    } else {
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const weekStart = new Date(today);
        // Start of this week (Saturday) - if today is Saturday (6), start is today, otherwise go back to previous Saturday
        const daysSinceSaturday = (today.getDay() + 1) % 7; // Saturday = 0, Sunday = 1, Monday = 2, etc.
        weekStart.setDate(today.getDate() - daysSinceSaturday);
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

        filteredPosts = currentMemberPosts.filter(post => {
            // Use enhanced post time if available, otherwise fall back to timestamp
            let postDate;

            if (post.enhancedPostTime && post.enhancedPostTime.fullDate) {
                // Use the calculated Facebook post time
                postDate = new Date(post.enhancedPostTime.fullDate);
            } else if (post.postTime && post.postTime !== 'Unknown') {
                // Calculate post time from relative time using helper function
                postDate = getActualPostDate(post.postTime);
                if (!postDate) {
                    // Fallback to scraping timestamp if calculation fails
                    postDate = new Date(post.timestamp);
                }
            } else {
                // Fallback to scraping timestamp
                postDate = new Date(post.timestamp);
            }

            const postDateOnly = new Date(postDate.getFullYear(), postDate.getMonth(), postDate.getDate());

            switch (timeFilter) {
                case 'today':
                    return postDateOnly.getTime() === today.getTime();
                case 'week':
                    return postDate >= weekStart;
                case 'month':
                    return postDate >= monthStart;
                default:
                    return true;
            }
        });
    }

    // Update the title to show filtered count
    const title = document.getElementById('memberPostsTitle');
    const filterText = timeFilter === 'all' ? '' : ` (${getFilterDisplayName(timeFilter)})`;
    title.innerHTML = `<i class="fas fa-file-alt"></i> ${selectedMember.name}'s Posts${filterText} (${filteredPosts.length})`;

    // Render filtered posts
    renderFilteredPosts(filteredPosts);

    console.log(`Filtered ${selectedMember.name}'s posts by ${timeFilter}: ${filteredPosts.length} posts`);
}

function getFilterDisplayName(filter) {
    switch (filter) {
        case 'today': return 'Today';
        case 'week': return 'This Week';
        case 'month': return 'This Month';
        default: return 'All';
    }
}

function resetTimeFilterButtons() {
    // Reset all filter buttons
    document.querySelectorAll('.time-stat').forEach(btn => {
        btn.classList.remove('active');
    });
    // Set "All" as active
    document.querySelector('[data-filter="all"]').classList.add('active');
}

function renderFilteredPosts(posts) {
    const content = document.getElementById('memberPostsContent');

    // Store filtered posts for pagination
    currentFilteredPosts = posts;

    if (posts.length === 0) {
        const filterName = getFilterDisplayName(currentTimeFilter);
        content.innerHTML = `
            <div class="no-selection">
                <i class="fas fa-calendar-times" style="font-size: 3rem; color: var(--text-muted);"></i>
                <p>No posts found for ${filterName.toLowerCase()}</p>
                <p style="font-size: 0.9rem; color: var(--text-muted);">
                    Try selecting a different time period or check back later.
                </p>
            </div>
        `;
        return;
    }

    // Calculate pagination
    totalPages = Math.ceil(posts.length / postsPerPage);

    // Ensure current page is valid
    if (currentPage > totalPages) {
        currentPage = totalPages;
    }
    if (currentPage < 1) {
        currentPage = 1;
    }

    // Get posts for current page
    const startIndex = (currentPage - 1) * postsPerPage;
    const endIndex = startIndex + postsPerPage;
    const currentPagePosts = posts.slice(startIndex, endIndex);

    content.innerHTML = `
        <div class="posts-grid">
            ${currentPagePosts.map(post => `
                <div class="post-card-mini">
                    <div class="post-card-header">
                        <div class="post-meta">
                            <div class="post-source">
                                <i class="fas fa-globe"></i>
                                ${getPageName(post.pageUrl)}
                            </div>
                            <div class="post-time">
                                <i class="fas fa-clock"></i>
                                ${post.enhancedPostTime ? (post.enhancedPostTime.combinedDisplay || post.enhancedPostTime.displayTime) : calculateActualPostTimeClientCombined(post.postTime) || post.postTime || getTimeAgo(post.timestamp)}
                            </div>
                        </div>
                    </div>
                    <div class="post-content">
                        ${truncateText(post.finalFilteredText || post.pageName || 'No content', 120)}
                    </div>
                    <div class="post-footer">
                        <div class="post-engagement">
                            <div class="engagement-stats">
                                <div class="engagement-item">
                                    <i class="fas fa-heart" style="color: #e74c3c;"></i>
                                    <span class="engagement-number">${formatNumber(post.engagement?.likes || 0)}</span>
                                </div>
                                <div class="engagement-item">
                                    <i class="fas fa-comment" style="color: #3498db;"></i>
                                    <span class="engagement-number">${formatNumber(post.engagement?.comments || 0)}</span>
                                </div>
                                ${post.engagement?.shares !== undefined ? `
                                    <div class="engagement-item">
                                        <i class="fas fa-share" style="color: #2ecc71;"></i>
                                        <span class="engagement-number">${formatNumber(post.engagement.shares)}</span>
                                    </div>
                                ` : ''}
                                ${post.engagement?.views !== undefined && post.videoDuration ? `
                                    <div class="engagement-item">
                                        <i class="fas fa-eye" style="color: #9b59b6;"></i>
                                        <span class="engagement-number">${formatNumber(post.engagement.views)}</span>
                                    </div>
                                ` : ''}
                                ${post.videoDuration ? `
                                    <div class="engagement-item">
                                        <i class="fas fa-video" style="color: #f39c12;"></i>
                                        <span class="engagement-number">${post.videoDuration}</span>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="post-actions">
                                ${post.postUrl ? `
                                    <button class="post-action-btn" onclick="window.open('${post.postUrl}', '_blank')" title="View on Facebook">
                                        <i class="fab fa-facebook"></i>
                                    </button>
                                ` : ''}
                                ${post.lastMetricsUpdate ? `
                                    <button class="post-action-btn" title="Metrics updated: ${getTimeAgo(post.lastMetricsUpdate)}">
                                        <i class="fas fa-sync-alt" style="color: #17a2b8;"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
        ${renderPagination()}
    `;
}

// Render pagination controls
function renderPagination() {
    if (totalPages <= 1) {
        return ''; // No pagination needed for single page
    }

    return `
        <div class="pagination-container">
            <div class="pagination-info">
                Showing ${((currentPage - 1) * postsPerPage) + 1}-${Math.min(currentPage * postsPerPage, currentFilteredPosts.length)} of ${currentFilteredPosts.length} posts
            </div>
            <div class="pagination-controls">
                <button class="pagination-btn" onclick="goToPage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i> Previous
                </button>
                <span class="pagination-current">
                    Page ${currentPage} of ${totalPages}
                </span>
                <button class="pagination-btn" onclick="goToPage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>
                    Next <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    `;
}

// Navigate to specific page
function goToPage(page) {
    if (page < 1 || page > totalPages || page === currentPage) {
        return;
    }

    currentPage = page;
    renderFilteredPosts(currentFilteredPosts);
}

// Reset pagination when switching members or filters
function resetPagination() {
    currentPage = 1;
    totalPages = 1;
    currentFilteredPosts = [];
}

// Individual member scraping functions
async function scrapeMember(memberId, event) {
    event.stopPropagation();

    if (isMemberScraping) {
        showNotification('Individual member scraping is already in progress', 'warning');
        return;
    }

    const member = teamMembers.find(m => m.id === memberId);
    if (!member) {
        showNotification('Team member not found', 'error');
        return;
    }

    if (member.assignedPages.length === 0) {
        showNotification('This team member has no assigned pages', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/team/${memberId}/scrape`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`Started scraping for ${result.memberName} (${result.assignedPages} pages)`, 'success');
            isMemberScraping = true;
            currentMemberScraping = { id: memberId, name: result.memberName };
            updateScrapingUI();
        } else {
            showNotification(result.error || 'Failed to start member scraping', 'error');
        }
    } catch (error) {
        console.error('Error starting member scraping:', error);
        showNotification('Error starting member scraping', 'error');
    }
}

// Get current posts per page setting
let currentPostsPerPage = 5; // Default value

async function updatePostsPerPageSetting() {
    try {
        const response = await fetch('/api/config');
        const config = await response.json();
        if (config.postsPerPage) {
            currentPostsPerPage = config.postsPerPage;
            updateButtonTexts();
        }
    } catch (error) {
        console.error('Error fetching config:', error);
    }
}

// Update button texts with current posts per page setting
function updateButtonTexts() {
    const buttons = document.querySelectorAll('.btn-icon.scrape-multiple');
    buttons.forEach(button => {
        button.title = `Scrape ${currentPostsPerPage} Posts from Each Page`;
    });
}

// NEW FUNCTION: Scrape multiple posts from team member pages
async function scrapeMultipleMemberPosts(memberId, event) {
    event.stopPropagation();

    if (isMemberScraping) {
        showNotification('Member scraping is already in progress', 'warning');
        return;
    }

    const member = teamMembers.find(m => m.id === memberId);
    if (!member) {
        showNotification('Team member not found', 'error');
        return;
    }

    if (member.assignedPages.length === 0) {
        showNotification('This team member has no assigned pages', 'warning');
        return;
    }

    try {
        const response = await fetch(`/api/team/${memberId}/scrape-multiple`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            showNotification(`🚀 Started collecting posts from each page for ${result.memberName} (${result.assignedPages} pages)`, 'success');
            isMemberScraping = true;
            currentMemberScraping = { id: memberId, name: result.memberName };
            updateScrapingUI();
        } else {
            showNotification(result.error || 'Failed to start multiple posts scraping', 'error');
        }
    } catch (error) {
        console.error('Error starting multiple posts scraping:', error);
        showNotification('Error starting multiple posts scraping', 'error');
    }
}

// DEBUG FUNCTION: Check team member URLs
async function debugMemberUrls(memberId, event) {
    event.stopPropagation();

    try {
        const response = await fetch(`/api/team/${memberId}/debug-urls`);
        const result = await response.json();

        if (result.success) {
            console.log('🐛 Debug URLs for member:', result.member);

            let debugInfo = `🐛 DEBUG INFO for ${result.member.name}\n\n`;
            debugInfo += `📋 Assigned Pages (${result.member.assignedPages.length}):\n`;
            result.member.assignedPages.forEach((page, index) => {
                debugInfo += `${index + 1}. ${typeof page === 'string' ? page : JSON.stringify(page)}\n`;
            });

            debugInfo += `\n🔗 Extracted URLs (${result.member.extractedUrls.length}):\n`;
            result.member.extractedUrls.forEach((url, index) => {
                debugInfo += `${index + 1}. ${url}\n`;
            });

            alert(debugInfo);

            // Also log to console for detailed inspection
            console.table(result.member.assignedPages);
            console.table(result.member.extractedUrls);

        } else {
            showNotification(result.error || 'Failed to debug URLs', 'error');
        }
    } catch (error) {
        console.error('Error debugging URLs:', error);
        showNotification('Error debugging URLs', 'error');
    }
}

// DEBUG FUNCTION: Inspect Facebook page content
async function debugFacebookPage(memberId, event) {
    event.stopPropagation();

    const member = teamMembers.find(m => m.id === memberId);
    if (!member || member.assignedPages.length === 0) {
        showNotification('No pages assigned to debug', 'warning');
        return;
    }

    const pageUrl = member.assignedPages[0];
    const url = typeof pageUrl === 'string' ? pageUrl : pageUrl.link;

    try {
        showNotification(`🐛 Debugging Facebook page: ${url}`, 'info');

        const response = await fetch('/api/debug-facebook-page', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ url })
        });

        const result = await response.json();

        if (result.success) {
            console.log('🐛 DEBUG Results:', result.pageInfo);

            let debugInfo = `🐛 FACEBOOK PAGE DEBUG RESULTS\n\n`;
            debugInfo += `📍 URL: ${result.pageInfo.url}\n`;
            debugInfo += `📄 Title: ${result.pageInfo.title}\n`;
            debugInfo += `🔢 Total Elements: ${result.pageInfo.totalElements}\n\n`;

            debugInfo += `📊 ELEMENT COUNTS:\n`;
            debugInfo += `• Articles [role="article"]: ${result.pageInfo.selectors.articles}\n`;
            debugInfo += `• Feed Units [data-pagelet="FeedUnit"]: ${result.pageInfo.selectors.feedUnits}\n`;
            debugInfo += `• Story Messages [data-ad-rendering-role="story_message"]: ${result.pageInfo.selectors.storyMessages}\n`;
            debugInfo += `• Data Test IDs [data-testid]: ${result.pageInfo.selectors.dataTestIds}\n`;
            debugInfo += `• Aria Labels [aria-label]: ${result.pageInfo.selectors.ariaLabels}\n`;
            debugInfo += `• Total Divs: ${result.pageInfo.selectors.divs}\n\n`;

            debugInfo += `🏗️ PAGE STRUCTURE:\n`;
            debugInfo += `• Has Main: ${result.pageInfo.hasMain}\n`;
            debugInfo += `• Has Feed: ${result.pageInfo.hasFeed}\n`;
            debugInfo += `• Has Navigation: ${result.pageInfo.hasNavigation}\n\n`;

            if (result.pageInfo.sampleElements.length > 0) {
                debugInfo += `📋 SAMPLE ELEMENTS:\n`;
                result.pageInfo.sampleElements.slice(0, 5).forEach((el, i) => {
                    debugInfo += `${i + 1}. ${el.selector} - ${el.tagName}\n`;
                    debugInfo += `   Aria: ${el.ariaLabel}\n`;
                    debugInfo += `   Text: ${el.textPreview}...\n\n`;
                });
            }

            debugInfo += `📸 Screenshot saved: ${result.screenshotSaved}`;

            alert(debugInfo);
            showNotification(`✅ Debug completed! Check console and screenshot: ${result.screenshotSaved}`, 'success');

        } else {
            showNotification(`❌ Debug failed: ${result.error}`, 'error');
        }
    } catch (error) {
        console.error('Error debugging Facebook page:', error);
        showNotification('Error debugging Facebook page', 'error');
    }
}

async function stopMemberScraping() {
    console.log('🛑 stopMemberScraping() called - attempting to stop member scraping');
    try {
        const response = await fetch('/api/team/stop-scraping', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();
        console.log('Stop scraping API response:', result);

        if (result.success) {
            showNotification('Individual member scraping stopped', 'info');
            console.log('✅ Stop request successful - calling resetScrapingUI()');
            // Immediately reset UI state as backup (socket event should also trigger this)
            resetScrapingUI();
        } else {
            console.log('❌ Stop request failed:', result.message);
            showNotification(result.message || 'No scraping to stop', 'warning');
        }
    } catch (error) {
        console.error('Error stopping member scraping:', error);
        showNotification('Error stopping member scraping', 'error');
    }
}

function updateScrapingUI() {
    // Update scraping buttons state
    const scrapeButtons = document.querySelectorAll('.btn-icon.scrape');
    scrapeButtons.forEach(btn => {
        btn.disabled = isMemberScraping;
        if (isMemberScraping) {
            btn.title = 'Scraping in progress...';
        } else {
            btn.title = 'Scrape Member Pages';
        }
    });

    // Show/hide stop scraping button
    const stopBtn = document.getElementById('stopMemberScrapingBtn');
    if (stopBtn) {
        stopBtn.style.display = isMemberScraping ? 'block' : 'none';
    }

    // Update member cards to show scraping status
    if (isMemberScraping && currentMemberScraping) {
        const memberCard = document.querySelector(`[onclick="selectMember('${currentMemberScraping.id}')"]`);
        if (memberCard) {
            memberCard.style.border = '2px solid #007bff';
            memberCard.style.background = 'rgba(0, 123, 255, 0.1)';
        }
    }
}

function resetScrapingUI() {
    console.log('🔄 resetScrapingUI() called - resetting member scraping UI state');
    isMemberScraping = false;
    currentMemberScraping = null;

    // Reset scraping buttons
    const scrapeButtons = document.querySelectorAll('.btn-icon.scrape');
    console.log(`Found ${scrapeButtons.length} scrape buttons to reset`);
    scrapeButtons.forEach(btn => {
        btn.disabled = false;
        btn.title = 'Scrape Member Pages';
    });

    // Hide stop scraping button
    const stopBtn = document.getElementById('stopMemberScrapingBtn');
    if (stopBtn) {
        console.log('Hiding stop scraping button');
        stopBtn.style.display = 'none';
    } else {
        console.warn('Stop scraping button not found!');
    }

    // Reset member card styles
    const memberCards = document.querySelectorAll('.member-card');
    console.log(`Found ${memberCards.length} member cards to reset`);
    memberCards.forEach(card => {
        card.style.border = '';
        card.style.background = '';
    });

    console.log('✅ resetScrapingUI() completed');
}

async function checkMemberScrapingStatus() {
    try {
        const response = await fetch('/api/team/scraping-status');
        const status = await response.json();

        if (status.isMemberScraping && status.currentMember) {
            isMemberScraping = true;
            currentMemberScraping = status.currentMember;
            updateScrapingUI();
            showNotification(`Scraping in progress for ${status.currentMember.name}`, 'info');
        }
    } catch (error) {
        console.error('Error checking scraping status:', error);
    }
}

// Socket.io event listeners for member scraping
if (typeof io !== 'undefined') {
    const socket = io({
        timeout: 60000, // 60 seconds timeout
        reconnection: true,
        reconnectionDelay: 1000,
        reconnectionAttempts: 5
    });

    // Add connection monitoring
    socket.on('connect_error', (error) => {
        console.error('Socket connection error:', error);
        showNotification('Connection error - please refresh the page', 'error');
    });

    socket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason);
        if (reason === 'io server disconnect') {
            // Server disconnected, try to reconnect
            socket.connect();
        }
    });

    socket.on('reconnect', (attemptNumber) => {
        console.log('Socket reconnected after', attemptNumber, 'attempts');
        showNotification('Connection restored', 'success');
    });

    socket.on('reconnect_error', (error) => {
        console.error('Socket reconnection error:', error);
        showNotification('Reconnection failed - please refresh the page', 'error');
    });

    socket.on('memberScrapingStarted', (data) => {
        console.log('Member scraping started:', data);
        isMemberScraping = true;
        currentMemberScraping = { id: data.memberId, name: data.memberName };
        updateScrapingUI();
        showNotification(`Started scraping for ${data.memberName} (${data.totalPages} pages)`, 'success');
    });

    socket.on('memberScrapingProgress', (data) => {
        console.log('Member scraping progress:', data);
        showNotification(`Scraping ${data.memberName}: ${data.completed}/${data.total} pages (${data.percentage}%)`, 'info');
    });

    socket.on('memberScrapingCompleted', (data) => {
        console.log('Member scraping completed:', data);
        resetScrapingUI();
        showNotification(`Completed scraping for ${data.memberName}. Processed ${data.pagesProcessed}/${data.totalPages} pages.`, 'success');

        // Refresh posts if the completed member is currently selected
        if (selectedMember && selectedMember.id === data.memberId) {
            loadMemberPosts(data.memberId);
        }
    });

    socket.on('memberScrapingStopped', (data) => {
        console.log('📡 Socket event: memberScrapingStopped received:', data);
        console.log('🔄 Calling resetScrapingUI() from socket event');
        resetScrapingUI();
        showNotification(data.message || 'Member scraping stopped', 'warning');
    });

    socket.on('memberScrapingError', (data) => {
        console.log('Member scraping error:', data);
        resetScrapingUI();
        showNotification(`Error scraping ${data.memberName}: ${data.error}`, 'error');
    });

    // Socket event listeners for multiple posts scraping
    socket.on('memberMultipleScrapingStarted', (data) => {
        console.log('📡 Socket: Multiple posts scraping started', data);
        isMemberScraping = true;
        currentMemberScraping = { id: data.memberId, name: data.memberName };
        updateScrapingUI();
        showNotification(`🔄 Started collecting posts from ${data.totalPages} pages for ${data.memberName}`, 'info');
    });

    socket.on('memberMultipleScrapingProgress', (data) => {
        console.log('📡 Socket: Multiple posts scraping progress', data);
        showNotification(`📊 Progress: ${data.completed}/${data.total} pages, ${data.postsCollected} posts collected (${data.percentage}%)`, 'info');
    });

    socket.on('memberMultipleScrapingCompleted', (data) => {
        console.log('📡 Socket: Multiple posts scraping completed', data);
        showNotification(`✅ Multiple posts scraping completed for ${data.memberName}! Collected ${data.totalPostsCollected} posts from ${data.pagesProcessed}/${data.totalPages} pages.`, 'success');
        resetScrapingUI();

        // Refresh posts and statistics after completion
        setTimeout(() => {
            loadPosts();
            loadTeamMembers();
            refreshTeamStatistics();

            // If the completed member is currently selected, reload their posts
            if (selectedMember && selectedMember.id === data.memberId) {
                loadMemberPosts(data.memberId);
            }
        }, 1000);
    });

    socket.on('memberMultipleScrapingError', (data) => {
        console.log('📡 Socket: Multiple posts scraping error', data);
        showNotification(`❌ Error in multiple posts scraping for ${data.memberName}: ${data.error}`, 'error');
        resetScrapingUI();
    });

    // Metrics update real-time events
    console.log('=== SETTING UP SOCKET LISTENERS ===');
    console.log('Socket connected:', socket.connected);
    console.log('Socket ID:', socket.id);

    // Add connection debugging
    socket.on('connect', () => {
        console.log('Socket connected successfully!', socket.id);
    });

    socket.on('disconnect', () => {
        console.log('Socket disconnected!');
    });

    socket.on('metricsUpdateStarted', (data) => {
        console.log('Metrics update started:', data);
        showNotification(`Started updating metrics for ${data.totalPosts} posts...`, 'info');
    });

    socket.on('metricsUpdateProgress', (data) => {
        console.log('Metrics update progress:', data);
        const percentage = Math.round((data.completed / data.total) * 100);
        showNotification(`Updating metrics: ${data.completed}/${data.total} posts (${percentage}%)`, 'info');
    });

    // Add a catch-all listener to see if ANY events are being received
    socket.onAny((eventName, ...args) => {
        console.log(`=== SOCKET EVENT RECEIVED: ${eventName} ===`, args);
    });

    socket.on('metricsUpdateCompleted', (data) => {
        console.log('=== METRICS UPDATE COMPLETED EVENT RECEIVED ===');
        console.log('Metrics update completed:', data);
        console.log('Current member:', selectedMember?.name);
        console.log('Current updating member:', currentMetricsUpdateMember);

        // Always show notification for completed updates
        showNotification(`Metrics update completed for ${data.memberName}! Updated ${data.updatedCount} posts`, 'success');

        // Refresh team statistics and best performer
        refreshTeamStatistics();

        // AUTOMATICALLY execute the stop button functionality when metrics update completes
        console.log('Auto-executing stop button functionality after completion');

        // Check if the global stop button exists and is visible
        const globalStopBtn = document.getElementById('globalStopMetricsBtn');
        if (globalStopBtn && globalStopBtn.style.display !== 'none') {
            console.log('Global stop button found and visible - auto-clicking it');
            // Automatically trigger the stop button click
            globalStopBtn.click();
        } else {
            console.log('Global stop button not found or not visible - manually resetting');
            // Fallback: manually reset the button state
            resetMetricsUpdateButton();
        }

        // If the completed member is currently selected, reload their posts
        if (selectedMember && data.memberName === selectedMember.name) {
            console.log('Reloading posts for completed member');
            loadMemberPosts(selectedMember.id);
        } else {
            console.log('Member name mismatch or no selected member');
        }
    });

    socket.on('metricsUpdateError', (data) => {
        console.log('Metrics update error:', data);

        // Always show notification for errors
        showNotification(`Error updating metrics for ${data.memberName}: ${data.error}`, 'error');

        // Always reset the global state when any member's metrics update has an error
        console.log('Auto-stopping metrics update after error');
        resetMetricsUpdateButton();
    });

    socket.on('metricsUpdateStopped', (data) => {
        console.log('Metrics update stopped:', data);

        // Always show notification for stopped updates
        showNotification(`Metrics update stopped for ${data.memberName}: ${data.message}`, 'warning');

        // Only reset button if this stop is for the currently selected member
        if (selectedMember && data.memberName === selectedMember.name) {
            resetMetricsUpdateButton();
        } else {
            console.log(`Stop event for ${data.memberName}, but current member is ${selectedMember?.name}`);
        }
    });

    // Bulk metrics update events
    socket.on('bulkMetricsProgress', (data) => {
        // Throttle updates during bulk operations - only update every 10%
        if (data.progress && data.progress.percentage % 10 === 0) {
            refreshTeamStatistics();
        }
    });

    socket.on('bulkMetricsCompleted', (data) => {
        // Final refresh when bulk update is completed
        refreshTeamStatistics();
        showNotification(`Bulk metrics update completed! Updated ${data.updatedCount} posts`, 'success');
    });

    socket.on('bulkMetricsError', (data) => {
        showNotification(`Bulk metrics update error: ${data.error}`, 'error');
    });

    socket.on('bulkMetricsStopped', (data) => {
        showNotification(data.message, 'info');
    });

    // WORKAROUND: Backup event listener to force button reset
    socket.on('forceResetMetricsButton', (data) => {
        console.log('=== FORCE RESET BUTTON EVENT RECEIVED ===');
        console.log('Force reset data:', data);

        // Force reset the button regardless of state
        console.log('Forcing button reset due to backup signal');
        resetMetricsUpdateButton();

        showNotification('Metrics update completed - button reset', 'success');
    });
}

// Global variables to track metrics update state
let isMemberMetricsUpdating = false;
let metricsUpdateTimeout = null;
let currentMetricsUpdateMember = null; // Track which member is being updated
let metricsUpdatePollingInterval = null; // Track polling interval

// Update metrics for current member's posts
async function updateMemberMetrics() {
    if (!selectedMember) {
        showNotification('Please select a team member first', 'error');
        return;
    }

    const updateBtn = document.getElementById('updateMemberMetricsBtn');

    // Check if metrics update is already running for any member
    if (isMemberMetricsUpdating) {
        showNotification(`Metrics update is already running for ${currentMetricsUpdateMember}. Please stop it first using the global stop button.`, 'warning');
        return;
    }

    const originalText = updateBtn.innerHTML;

    // Set updating state
    isMemberMetricsUpdating = true;
    currentMetricsUpdateMember = selectedMember.name;

    // Update button to show updating state with animation
    updateBtn.classList.add('updating');
    updateBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Updating...';

    try {
        // Get posts for the selected member
        const memberPosts = currentMemberPosts;
        if (!memberPosts || memberPosts.length === 0) {
            showNotification('No posts found for this member', 'warning');
            return;
        }

        // Extract post URLs for metrics update
        const postUrls = memberPosts
            .filter(post => post.postUrl && post.postUrl.trim() !== '')
            .map(post => post.postUrl);

        if (postUrls.length === 0) {
            showNotification('No valid post URLs found for metrics update', 'warning');
            return;
        }

        showNotification(`Starting metrics update for ${postUrls.length} posts...`, 'info');

        // Call the metrics update API
        const response = await fetch('/api/update-metrics', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                postUrls: postUrls,
                memberName: selectedMember.name
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            // Keep the button in updating state but don't allow stopping from here
            updateBtn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Updating...';
            updateBtn.classList.remove('updating');
            updateBtn.classList.add('disabled');
            updateBtn.disabled = true;

            // Show global stop button
            showGlobalStopButton();

            // Start polling to check if metrics update is complete (every 2 seconds)
            console.log('Starting polling to check metrics update status');
            startMetricsUpdatePolling();

            showNotification(`Metrics update started for ${postUrls.length} posts. Use the global stop button to stop.`, 'info');
        } else {
            throw new Error(result.error || 'Unknown error occurred');
        }

    } catch (error) {
        console.error('Error updating member metrics:', error);
        showNotification(`Error updating metrics: ${error.message}`, 'error');
        resetMetricsUpdateButton();
    }
}

// Start polling to check metrics update status
function startMetricsUpdatePolling() {
    console.log('Starting metrics update polling...');

    // Clear any existing polling
    if (metricsUpdatePollingInterval) {
        clearInterval(metricsUpdatePollingInterval);
    }

    metricsUpdatePollingInterval = setInterval(async () => {
        try {
            console.log('Polling metrics update status...');
            const response = await fetch('/api/member-metrics-status');
            if (response.ok) {
                const data = await response.json();
                console.log('Polling result:', data);

                // If metrics update is no longer running, reset the buttons
                if (!data.isRunning) {
                    console.log('Metrics update completed - stopping polling and resetting buttons');
                    stopMetricsUpdatePolling();
                    resetMetricsUpdateButton();

                    // Refresh data to show updated metrics
                    refreshTeamStatistics();
                    if (selectedMember) {
                        loadMemberPosts(selectedMember.id);
                    }

                    showNotification('Metrics update completed!', 'success');
                }
            }
        } catch (error) {
            console.error('Error polling metrics status:', error);
        }
    }, 2000); // Poll every 2 seconds
}

// Stop polling
function stopMetricsUpdatePolling() {
    console.log('Stopping metrics update polling');
    if (metricsUpdatePollingInterval) {
        clearInterval(metricsUpdatePollingInterval);
        metricsUpdatePollingInterval = null;
    }
}

// Reset metrics update button to original state
function resetMetricsUpdateButton() {
    console.log('=== RESETTING METRICS UPDATE BUTTON ===');
    console.log('Current state - isMemberMetricsUpdating:', isMemberMetricsUpdating);
    console.log('Current state - currentMetricsUpdateMember:', currentMetricsUpdateMember);

    const updateBtn = document.getElementById('updateMemberMetricsBtn');

    if (!updateBtn) {
        console.error('Update metrics button not found!');
        return;
    }

    isMemberMetricsUpdating = false;
    currentMetricsUpdateMember = null;

    // Stop polling
    stopMetricsUpdatePolling();

    // Clear any existing timeout
    if (metricsUpdateTimeout) {
        clearTimeout(metricsUpdateTimeout);
        metricsUpdateTimeout = null;
        console.log('Cleared metrics update timeout');
    }

    updateBtn.classList.remove('updating', 'stopping', 'disabled');
    updateBtn.disabled = false;
    updateBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Update Metrics';
    console.log('Update button reset to original state');

    // Hide global stop button
    console.log('About to hide global stop button...');
    hideGlobalStopButton();

    console.log('=== BUTTON RESET COMPLETE ===');
}

// Global stop button functions
function showGlobalStopButton() {
    const globalStopBtn = document.getElementById('globalStopMetricsBtn');
    if (globalStopBtn) {
        globalStopBtn.style.display = 'inline-block';
        globalStopBtn.innerHTML = `<i class="fas fa-stop"></i> Stop ${currentMetricsUpdateMember}`;
    }
}

function hideGlobalStopButton() {
    console.log('Hiding global stop button');
    const globalStopBtn = document.getElementById('globalStopMetricsBtn');
    if (globalStopBtn) {
        globalStopBtn.style.display = 'none';
        console.log('Global stop button hidden successfully');
    } else {
        console.error('Global stop button not found!');
    }
}

// Global stop metrics update function
async function stopGlobalMetricsUpdate() {
    console.log('Global stop button clicked - attempting to stop metrics update');

    if (!currentMetricsUpdateMember) {
        showNotification('No metrics update is currently running', 'warning');
        return;
    }

    try {
        const response = await fetch('/api/stop-member-metrics', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                memberName: currentMetricsUpdateMember
            })
        });

        const result = await response.json();

        if (response.ok && result.success) {
            showNotification(`Metrics update stopped for ${currentMetricsUpdateMember}`, 'warning');
            // Immediately reset button state
            resetMetricsUpdateButton();
        } else {
            showNotification(result.message || 'Failed to stop metrics update', 'error');
            // Reset button on failure too
            resetMetricsUpdateButton();
        }
    } catch (error) {
        console.error('Error stopping metrics update:', error);
        showNotification('Error stopping metrics update', 'error');
        // Reset button on error
        resetMetricsUpdateButton();
    }
}

// Check member metrics update status on page load
async function checkMemberMetricsStatus() {
    try {
        const response = await fetch('/api/member-metrics-status');
        if (response.ok) {
            const data = await response.json();
            console.log('Member metrics status:', data);

            if (data.isRunning && data.memberName) {
                // Restore the running state
                isMemberMetricsUpdating = true;
                currentMetricsUpdateMember = data.memberName;

                // Show global stop button
                showGlobalStopButton();

                // If the currently selected member is the one being updated, show disabled state
                if (selectedMember && selectedMember.name === data.memberName) {
                    const updateBtn = document.getElementById('updateMemberMetricsBtn');
                    if (updateBtn) {
                        updateBtn.innerHTML = '<i class="fas fa-sync-alt fa-spin"></i> Updating...';
                        updateBtn.classList.add('disabled');
                        updateBtn.disabled = true;
                    }
                }

                console.log(`Restored metrics update state for: ${data.memberName}`);
            }
        }
    } catch (error) {
        console.error('Error checking member metrics status:', error);
    }
}

// Delete all posts for a specific team member
async function deleteMemberPosts(memberId, event) {
    event.stopPropagation(); // Prevent member selection

    const member = teamMembers.find(m => m.id === memberId);
    if (!member) {
        showNotification('Team member not found', 'error');
        return;
    }

    // Show confirmation dialog
    const confirmed = confirm(
        `Are you sure you want to delete ALL posts for "${member.name}"?\n\n` +
        `This will permanently remove all posts from their assigned pages and cannot be undone.\n\n` +
        `Click OK to proceed or Cancel to abort.`
    );

    if (!confirmed) {
        return;
    }

    try {
        console.log(`Deleting all posts for member: ${member.name} (${memberId})`);

        // Show loading state
        showNotification(`Deleting posts for ${member.name}...`, 'info');

        const response = await fetch(`/api/team/delete-member-posts/${memberId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            showNotification(
                `Successfully deleted ${result.deletedCount} posts for ${member.name}`,
                'success'
            );

            // Refresh team statistics
            refreshTeamStatistics();

            // If this member is currently selected, reload their posts (should be empty now)
            if (selectedMember && selectedMember.id === memberId) {
                loadMemberPosts(memberId);
            }

            console.log(`Deleted ${result.deletedCount} posts for ${member.name}`);
        } else {
            throw new Error(result.error || 'Failed to delete posts');
        }

    } catch (error) {
        console.error('Error deleting member posts:', error);
        showNotification(`Error deleting posts for ${member.name}: ${error.message}`, 'error');
    }
}





// Check URL parameters for member selection
function checkUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const memberId = urlParams.get('member');

    if (memberId && teamMembers.length > 0) {
        // Find the member and select them
        const member = teamMembers.find(m => m.id === memberId);
        if (member) {
            console.log(`Auto-selecting member from URL: ${member.name}`);
            selectMember(memberId);

            // Clear the URL parameter
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
        }
    }
}

// Initialize URL parameter checking after team members are loaded
function initializeWithUrlParams() {
    // Wait a bit for team members to load, then check URL params
    setTimeout(() => {
        checkUrlParameters();
    }, 1000);
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('memberModal');
    if (event.target === modal) {
        closeMemberModal();
    }
}
