<?php
// Simple Database Test
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

$response = ['success' => false, 'tests' => [], 'debug' => []];

// Test 1: Basic PHP
$response['tests']['php'] = true;
$response['debug'][] = "✅ PHP is working";

// Test 2: Database Connection
$response['tests']['database'] = false;
try {
    $response['debug'][] = "Attempting database connection...";
    $conn = new mysqli("localhost", "root", "", "facebook_db");
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    $response['tests']['database'] = true;
    $response['debug'][] = "✅ Database connection successful";
    
    // Test if tables exist
    $result = $conn->query("SHOW TABLES");
    $tables = [];
    while ($row = $result->fetch_array()) {
        $tables[] = $row[0];
    }
    $response['debug'][] = "✅ Found tables: " . implode(", ", $tables);
    
    $conn->close();
    
} catch (Exception $e) {
    $response['debug'][] = "❌ Database error: " . $e->getMessage();
}

// Test 3: Check if we can reach Electron app
$response['tests']['electron'] = false;
$response['debug'][] = "Testing Electron app connection...";

$ports = [3000, 3001, 8080, 5000];
foreach ($ports as $port) {
    $url = "http://localhost:$port/api/posts";
    $response['debug'][] = "Trying port $port...";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 3,
            'method' => 'GET',
            'ignore_errors' => true
        ]
    ]);
    
    $data = @file_get_contents($url, false, $context);
    if ($data !== false) {
        $posts = json_decode($data, true);
        if (is_array($posts)) {
            $response['tests']['electron'] = true;
            $response['debug'][] = "✅ Found Electron app on port $port with " . count($posts) . " posts";
            $response['electron_port'] = $port;
            break;
        }
    }
}

if (!$response['tests']['electron']) {
    $response['debug'][] = "❌ Could not connect to Electron app on any port";
}

$response['success'] = $response['tests']['php'] && $response['tests']['database'];
$response['message'] = $response['success'] ? "Basic tests passed" : "Some tests failed";

echo json_encode($response, JSON_PRETTY_PRINT);
?>
