<?php
require_once 'config.php';

try {
    // Get configuration
    $config = DatabaseConfig::loadConfig();

    // Create connection using mysqli for backward compatibility
    $conn = new mysqli(
        $config['DB_HOST'],
        $config['DB_USER'],
        $config['DB_PASSWORD'],
        $config['DB_NAME'],
        (int)$config['DB_PORT']
    );

    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // Set charset
    $conn->set_charset("utf8mb4");

    // Create database if it doesn't exist
    $conn->query("CREATE DATABASE IF NOT EXISTS `{$config['DB_NAME']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $conn->select_db($config['DB_NAME']);

    // Initialize tables if needed
    initializeDatabaseTables();

} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());

    // For API calls, return JSON error
    if (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false) {
        header('Content-Type: application/json');
        echo json_encode([
            'error' => 'Database connection failed',
            'message' => $e->getMessage(),
            'suggestion' => 'Please check your .env file configuration'
        ]);
        exit;
    }

    die("Database connection failed: " . $e->getMessage() . "\nPlease check your .env file configuration.");
}
?>