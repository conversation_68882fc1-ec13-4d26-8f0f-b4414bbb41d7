<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Metrics Extraction</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        input[type="url"] {
            width: 70%;
            padding: 10px;
            margin-right: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .results {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            border: 1px solid #ddd;
        }
        .section {
            margin-bottom: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .item {
            background: white;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            border-left: 3px solid #007bff;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug Metrics Extraction</h1>
    
    <div class="container">
        <h2>Test URL</h2>
        <input type="url" id="urlInput" placeholder="Enter Facebook post URL to test..." value="https://www.facebook.com/AlHadath/">
        <button onclick="debugMetrics()">Debug Metrics</button>
    </div>

    <div id="results"></div>

    <script>
        async function debugMetrics() {
            const url = document.getElementById('urlInput').value;
            const resultsDiv = document.getElementById('results');
            
            if (!url) {
                resultsDiv.innerHTML = '<div class="error">Please enter a URL</div>';
                return;
            }

            resultsDiv.innerHTML = '<div class="loading">🔄 Testing metrics extraction... (Browser will open)</div>';

            try {
                const response = await fetch('/api/debug-metrics', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ url: url })
                });

                const data = await response.json();

                if (data.success) {
                    displayResults(data.debugInfo, url);
                } else {
                    resultsDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Network error: ${error.message}</div>`;
            }
        }

        function displayResults(debugInfo, url) {
            const resultsDiv = document.getElementById('results');
            
            let html = `
                <div class="results">
                    <h2>🎯 Debug Results for: ${url}</h2>
                    
                    <div class="section">
                        <h3>📊 Target Spans (span.xkrqix3.x1sur9pj)</h3>
                        <p><strong>Found ${debugInfo.allSpansWithClasses.length} spans with target classes</strong></p>
                        ${debugInfo.allSpansWithClasses.map(span => `
                            <div class="item">
                                <strong>Text:</strong> "${span.text}"<br>
                                <strong>HTML:</strong> ${span.innerHTML}
                            </div>
                        `).join('')}
                    </div>

                    <div class="section">
                        <h3>💬 Comment Spans</h3>
                        <p><strong>Found ${debugInfo.commentSpans.length} spans containing "comment"</strong></p>
                        ${debugInfo.commentSpans.map(span => `
                            <div class="item">
                                <strong>Text:</strong> "${span.text}"<br>
                                <strong>HTML:</strong> ${span.innerHTML}
                            </div>
                        `).join('')}
                    </div>

                    <div class="section">
                        <h3>🔄 Share Spans</h3>
                        <p><strong>Found ${debugInfo.shareSpans.length} spans containing "share"</strong></p>
                        ${debugInfo.shareSpans.map(span => `
                            <div class="item">
                                <strong>Text:</strong> "${span.text}"<br>
                                <strong>HTML:</strong> ${span.innerHTML}
                            </div>
                        `).join('')}
                    </div>

                    <div class="section">
                        <h3>🔢 All Spans with Numbers (First 20)</h3>
                        <p><strong>Found ${debugInfo.allSpansWithNumbers.length} spans with numbers</strong></p>
                        ${debugInfo.allSpansWithNumbers.map(span => `
                            <div class="item">
                                <strong>Text:</strong> "${span.text}"<br>
                                <strong>Classes:</strong> ${span.classes}
                            </div>
                        `).join('')}
                    </div>

                    <div class="section">
                        <h3>📄 Page Text Preview</h3>
                        <div class="item">
                            ${debugInfo.pageText}...
                        </div>
                    </div>
                </div>
            `;

            resultsDiv.innerHTML = html;
        }

        // Auto-focus the input
        document.getElementById('urlInput').focus();
    </script>
</body>
</html>
