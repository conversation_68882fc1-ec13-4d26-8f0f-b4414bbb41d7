<?php
// Verify Posts Data in Database
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

// Database connection
$conn = new mysqli("localhost", "root", "", "facebook_db");

$response = ['success' => false, 'data' => [], 'summary' => [], 'debug' => []];

if ($conn->connect_error) {
    $response['message'] = "Connection failed: " . $conn->connect_error;
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

try {
    $response['debug'][] = "✅ Database connection successful";

    // Get summary counts
    $membersCount = $conn->query("SELECT COUNT(*) as count FROM members")->fetch_assoc()['count'];
    $pagesCount = $conn->query("SELECT COUNT(*) as count FROM pages")->fetch_assoc()['count'];
    $postsCount = $conn->query("SELECT COUNT(*) as count FROM posts")->fetch_assoc()['count'];

    $response['summary'] = [
        'members' => $membersCount,
        'pages' => $pagesCount,
        'posts' => $postsCount
    ];

    // Get sample data with relationships
    $query = "
        SELECT 
            p.id as post_id,
            p.caption,
            p.likes_count,
            p.comments_count,
            p.views_count,
            p.shares_count,
            p.post_date,
            p.extraction_timestamp,
            pg.id as page_id,
            pg.name as page_name,
            pg.url as page_url,
            m.id as member_id,
            m.name as member_name,
            m.role as member_role
        FROM posts p
        LEFT JOIN pages pg ON p.page_id = pg.id
        LEFT JOIN members m ON p.member_id = m.id
        ORDER BY p.extraction_timestamp DESC
        LIMIT 10
    ";

    $result = $conn->query($query);
    $samplePosts = [];

    while ($row = $result->fetch_assoc()) {
        $samplePosts[] = [
            'post_id' => $row['post_id'],
            'caption' => substr($row['caption'], 0, 100) . '...',
            'engagement' => [
                'likes' => $row['likes_count'],
                'comments' => $row['comments_count'],
                'views' => $row['views_count'],
                'shares' => $row['shares_count']
            ],
            'page' => [
                'id' => $row['page_id'],
                'name' => $row['page_name'],
                'url' => $row['page_url']
            ],
            'member' => [
                'id' => $row['member_id'],
                'name' => $row['member_name'],
                'role' => $row['member_role']
            ],
            'timestamps' => [
                'post_date' => $row['post_date'],
                'extraction_timestamp' => $row['extraction_timestamp']
            ]
        ];
    }

    // Get member-page relationships
    $memberPagesQuery = "
        SELECT 
            m.id as member_id,
            m.name as member_name,
            COUNT(pg.id) as pages_count,
            COUNT(p.id) as posts_count
        FROM members m
        LEFT JOIN pages pg ON m.id = pg.member_id
        LEFT JOIN posts p ON m.id = p.member_id
        GROUP BY m.id, m.name
        ORDER BY m.name
    ";

    $memberResult = $conn->query($memberPagesQuery);
    $memberStats = [];

    while ($row = $memberResult->fetch_assoc()) {
        $memberStats[] = [
            'member_id' => $row['member_id'],
            'member_name' => $row['member_name'],
            'pages_managed' => $row['pages_count'],
            'posts_total' => $row['posts_count']
        ];
    }

    $response['data'] = [
        'sample_posts' => $samplePosts,
        'member_stats' => $memberStats
    ];

    $response['success'] = true;
    $response['debug'][] = "📊 Data verification completed";
    $response['debug'][] = "🔗 Relationships: Member → Page → Posts structure verified";

} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
    $response['debug'][] = "❌ Verification failed: " . $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
