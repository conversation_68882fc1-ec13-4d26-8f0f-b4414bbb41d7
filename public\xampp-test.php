<?php
// XAMPP Connection Test for Facebook DB
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$results = [];
$status = 'success';

function addTest($name, $result, $message, $data = null) {
    global $results, $status;
    $results[] = [
        'test' => $name,
        'status' => $result,
        'message' => $message,
        'data' => $data,
        'time' => date('H:i:s')
    ];
    if ($result === 'error') $status = 'error';
    elseif ($result === 'warning' && $status !== 'error') $status = 'warning';
}

// Test 1: XAMPP Environment
try {
    $xamppInfo = [
        'php_version' => phpversion(),
        'server' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
        'host' => $_SERVER['HTTP_HOST'] ?? 'localhost',
        'port' => $_SERVER['SERVER_PORT'] ?? '80',
        'mysqli' => extension_loaded('mysqli') ? 'Available' : 'Missing'
    ];
    
    if ($xamppInfo['mysqli'] === 'Available') {
        addTest('XAMPP Environment', 'success', 'XAMPP is running with MySQL support', $xamppInfo);
    } else {
        addTest('XAMPP Environment', 'error', 'MySQLi extension not available', $xamppInfo);
    }
} catch (Exception $e) {
    addTest('XAMPP Environment', 'error', 'XAMPP check failed: ' . $e->getMessage());
}

// Test 2: Connection.php Test
try {
    if (file_exists('connection.php')) {
        addTest('Connection File', 'success', 'connection.php exists');
        
        ob_start();
        include 'connection.php';
        $output = ob_get_clean();
        
        if (!empty($output)) {
            addTest('Connection Include', 'error', 'connection.php has errors: ' . $output);
        } else {
            addTest('Connection Include', 'success', 'connection.php loaded successfully');
            
            if (isset($conn) && $conn instanceof mysqli) {
                if ($conn->connect_error) {
                    addTest('MySQL Connection', 'error', 'Connection failed: ' . $conn->connect_error);
                } else {
                    addTest('MySQL Connection', 'success', 'Connected to MySQL successfully', [
                        'host' => 'localhost',
                        'database' => 'facebook_db',
                        'connection_id' => $conn->thread_id
                    ]);
                    
                    // Test 3: Database Info
                    $result = $conn->query("SELECT DATABASE() as db, VERSION() as version");
                    if ($result && $row = $result->fetch_assoc()) {
                        addTest('Database Info', 'success', 'Database info retrieved', $row);
                    }
                    
                    // Test 4: Tables Check
                    $tables = ['members', 'pages', 'posts'];
                    $tableData = [];
                    
                    foreach ($tables as $table) {
                        $result = $conn->query("SHOW TABLES LIKE '$table'");
                        $exists = $result && $result->num_rows > 0;
                        $tableData[$table] = ['exists' => $exists];
                        
                        if ($exists) {
                            $countResult = $conn->query("SELECT COUNT(*) as count FROM $table");
                            if ($countResult) {
                                $count = $countResult->fetch_assoc()['count'];
                                $tableData[$table]['count'] = $count;
                            }
                        }
                    }
                    
                    $allExist = $tableData['members']['exists'] && $tableData['pages']['exists'] && $tableData['posts']['exists'];
                    
                    if ($allExist) {
                        addTest('Table Check', 'success', 'All tables exist', $tableData);
                        
                        // Test 5: Data Operations
                        $testId = 'xampp_test_' . time();
                        $stmt = $conn->prepare("INSERT INTO members (id, name, email, role) VALUES (?, ?, ?, ?)");
                        if ($stmt) {
                            $name = 'XAMPP Test User';
                            $email = '<EMAIL>';
                            $role = 'Tester';
                            $stmt->bind_param("ssss", $testId, $name, $email, $role);
                            
                            if ($stmt->execute()) {
                                addTest('Data Insert', 'success', 'Test data inserted', ['id' => $testId]);
                                
                                // Test SELECT
                                $selectStmt = $conn->prepare("SELECT * FROM members WHERE id = ?");
                                $selectStmt->bind_param("s", $testId);
                                $selectStmt->execute();
                                $selectResult = $selectStmt->get_result();
                                
                                if ($selectResult && $row = $selectResult->fetch_assoc()) {
                                    addTest('Data Select', 'success', 'Test data retrieved', $row);
                                    
                                    // Cleanup
                                    $deleteStmt = $conn->prepare("DELETE FROM members WHERE id = ?");
                                    $deleteStmt->bind_param("s", $testId);
                                    $deleteStmt->execute();
                                    addTest('Data Cleanup', 'success', 'Test data cleaned up');
                                }
                            } else {
                                addTest('Data Insert', 'error', 'Insert failed: ' . $stmt->error);
                            }
                        }
                    } else {
                        addTest('Table Check', 'warning', 'Some tables missing', $tableData);
                    }
                }
            } else {
                addTest('MySQL Connection', 'error', 'No valid connection object created');
            }
        }
    } else {
        addTest('Connection File', 'error', 'connection.php not found');
    }
} catch (Exception $e) {
    addTest('Connection Test', 'error', 'Test failed: ' . $e->getMessage());
}

// Response
echo json_encode([
    'success' => $status !== 'error',
    'status' => $status,
    'xampp_url' => 'http://localhost',
    'phpmyadmin_url' => 'http://localhost/phpmyadmin',
    'total_tests' => count($results),
    'passed' => count(array_filter($results, fn($r) => $r['status'] === 'success')),
    'warnings' => count(array_filter($results, fn($r) => $r['status'] === 'warning')),
    'errors' => count(array_filter($results, fn($r) => $r['status'] === 'error')),
    'tests' => $results,
    'timestamp' => date('Y-m-d H:i:s'),
    'recommendations' => $status === 'success' ? 
        ['✅ XAMPP PHP connection working perfectly!', '✅ Ready for database operations'] :
        ['❌ Check XAMPP MySQL service', '❌ Verify connection.php settings']
], JSON_PRETTY_PRINT);
?>
