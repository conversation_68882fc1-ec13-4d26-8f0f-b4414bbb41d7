# 🗄️ MySQL Database Setup Guide

This guide will help you set up a MySQL database environment for the Facebook Scraper with auto-sync capabilities.

## 📋 Prerequisites

### Local MySQL Installation
1. **Download MySQL**: Visit [MySQL Downloads](https://dev.mysql.com/downloads/mysql/)
2. **Install MySQL Server**: Follow the installation wizard
3. **Set Root Password**: Remember your root password
4. **Start MySQL Service**: Ensure MySQL is running

### Remote/Cloud MySQL Options
- **AWS RDS**: Amazon Relational Database Service
- **Google Cloud SQL**: Google's managed MySQL service
- **DigitalOcean Managed Databases**: Simple cloud MySQL
- **PlanetScale**: Serverless MySQL platform
- **Railway**: Easy MySQL hosting

## 🚀 Quick Setup

### Step 1: Access Database Setup
1. Open your browser and go to: `http://localhost/facebook-scraper/public/database-setup.html`
2. You'll see the database configuration form

### Step 2: Configure Database Connection
Fill in your database details:

**For Local MySQL:**
- **Host**: `localhost`
- **Port**: `3306`
- **Database Name**: `facebook_db`
- **Username**: `root`
- **Password**: Your MySQL root password

**For Remote MySQL:**
- **Host**: Your remote MySQL server address
- **Port**: Usually `3306` (check with your provider)
- **Database Name**: Your database name
- **Username**: Your database username
- **Password**: Your database password

### Step 3: Test and Setup
1. Click **"Test Connection"** to verify your settings
2. Click **"Save Configuration"** to save your settings
3. Click **"Setup Database"** to create tables and initial data

## 🔧 Manual Configuration

If you prefer to configure manually, edit the `.env` file in the `public` folder:

```env
# MySQL Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=facebook_db
DB_USER=root
DB_PASSWORD=your_password_here

# For Remote MySQL
# DB_HOST=your-mysql-server.com
# DB_USER=your_username
# DB_PASSWORD=your_password

# SSL Configuration (for remote connections)
DB_SSL=false

# Auto-sync settings
AUTO_SYNC_ENABLED=true
AUTO_SYNC_INTERVAL=300000
```

## 🌐 Public MySQL Setup Examples

### AWS RDS Setup
1. Create RDS MySQL instance in AWS Console
2. Configure security groups to allow connections
3. Use these settings:
   ```
   DB_HOST=your-rds-endpoint.amazonaws.com
   DB_PORT=3306
   DB_USER=admin
   DB_PASSWORD=your-password
   DB_SSL=true
   ```

### DigitalOcean Managed Database
1. Create MySQL database in DigitalOcean
2. Download SSL certificate if required
3. Use connection details provided:
   ```
   DB_HOST=your-db-host.db.ondigitalocean.com
   DB_PORT=25060
   DB_USER=doadmin
   DB_PASSWORD=your-password
   DB_SSL=true
   ```

### PlanetScale Setup
1. Create database in PlanetScale
2. Create branch (main)
3. Get connection string:
   ```
   DB_HOST=your-host.planetscale.service.com
   DB_PORT=3306
   DB_USER=your-username
   DB_PASSWORD=your-password
   DB_SSL=true
   ```

## 🔄 Auto-Sync Features

The system includes automatic synchronization:

- **Automatic Data Sync**: Syncs JSON data to MySQL every 5 minutes
- **Real-time Updates**: Updates database when new posts are scraped
- **Offline Handling**: Pauses sync when offline, resumes when back online
- **Background Sync**: Continues syncing even when browser tab is not active
- **Error Recovery**: Automatically retries failed sync operations

### Auto-Sync Configuration
```env
AUTO_SYNC_ENABLED=true
AUTO_SYNC_INTERVAL=300000  # 5 minutes in milliseconds
```

## 📊 Database Structure

The system creates these tables:

### Members Table
- `id` - Member ID
- `name` - Member name
- `email` - Member email
- `role` - Member role
- `assigned_pages_count` - Number of assigned pages
- `total_posts_count` - Total posts count

### Pages Table
- `id` - Page ID
- `name` - Page name
- `url` - Page URL
- `member_id` - Assigned member
- `posts_count` - Number of posts

### Posts Table
- `id` - Post ID
- `page_id` - Associated page
- `member_id` - Associated member
- `caption` - Post text content
- `media_id` - Media identifier
- `likes_count` - Number of likes
- `comments_count` - Number of comments
- `views_count` - Number of views
- `shares_count` - Number of shares
- `post_url` - Original post URL
- `post_date` - When post was published
- `extraction_timestamp` - When data was scraped

## 🔍 Troubleshooting

### Connection Issues
1. **Check MySQL Service**: Ensure MySQL is running
2. **Verify Credentials**: Double-check username/password
3. **Check Firewall**: Ensure port 3306 is open
4. **Test Manually**: Try connecting with MySQL Workbench or command line

### Permission Issues
```sql
-- Grant permissions to user
GRANT ALL PRIVILEGES ON facebook_db.* TO 'your_user'@'%';
FLUSH PRIVILEGES;
```

### SSL Issues for Remote Connections
1. Download SSL certificates from your provider
2. Place them in a secure directory
3. Update .env with certificate paths:
   ```
   DB_SSL=true
   DB_SSL_CA_PATH=/path/to/ca-cert.pem
   ```

## 📈 Monitoring

### Database Status
Visit: `http://localhost/facebook-scraper/public/database-config.php?action=status`

### Sync Status
The database page shows real-time sync status in the top-right corner.

## 🔒 Security Best Practices

1. **Use Strong Passwords**: Create complex database passwords
2. **Limit Access**: Only allow necessary IP addresses
3. **Use SSL**: Enable SSL for remote connections
4. **Regular Backups**: Set up automated database backups
5. **Monitor Access**: Review database access logs regularly

## 📞 Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your .env configuration
3. Test database connection manually
4. Check MySQL error logs
5. Ensure all required PHP extensions are installed (PDO, mysqli)

The system is designed to work with both local and remote MySQL databases, providing flexibility for different deployment scenarios.
