// Test proxy rotation
const proxyManager = require('./proxy-manager');

console.log('🧪 Testing proxy rotation...\n');

console.log(`Total proxies loaded: ${proxyManager.getProxyCount()}`);
console.log(`Rotation enabled: ${proxyManager.rotationEnabled}`);

console.log('\n🔄 Testing 10 proxy rotations:');
for (let i = 0; i < 10; i++) {
  const proxy = proxyManager.getNextProxy();
  if (proxy) {
    const parts = proxy.split(':');
    console.log(`${i + 1}. ${parts[0]}:${parts[1]}:${parts[2]}:****`);
  } else {
    console.log(`${i + 1}. No proxy available`);
  }
}

console.log('\n📊 Proxy statistics:');
const stats = proxyManager.getProxyStats();
console.log(`Active proxies: ${stats.active}`);
console.log(`Failed proxies: ${stats.failed}`);
console.log(`Total proxies: ${stats.total}`);

console.log('\n🔍 First 5 unique proxies:');
const uniqueProxies = new Set();
let attempts = 0;
while (uniqueProxies.size < 5 && attempts < 100) {
  const proxy = proxyManager.getNextProxy();
  if (proxy) {
    const parts = proxy.split(':');
    const proxyId = `${parts[0]}:${parts[1]}`;
    uniqueProxies.add(proxyId);
  }
  attempts++;
}

Array.from(uniqueProxies).forEach((proxy, index) => {
  console.log(`${index + 1}. ${proxy}`);
});

console.log(`\n✅ Found ${uniqueProxies.size} unique proxy endpoints in ${attempts} attempts`);
