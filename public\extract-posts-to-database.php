<?php
// Extract Posts Data to MySQL Database
// Focus on core post data and relationships, not statistics
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

$response = ['success' => false, 'message' => '', 'results' => [], 'debug' => []];

// Database connection using existing connection file
try {
    $servername = "localhost";
    $username = "root";
    $password = "";
    $dbname = "facebook_db";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    $response['debug'][] = "✅ Database connection successful";
} catch (Exception $e) {
    $response['message'] = "Database connection failed: " . $e->getMessage();
    $response['debug'][] = "❌ Database connection failed: " . $e->getMessage();
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

try {
    $response['debug'][] = "🔄 Starting data extraction from Electron app to MySQL";

    // Create simplified tables focused on core data
    $conn->query("
        CREATE TABLE IF NOT EXISTS members (
            id VARCHAR(50) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            email VARCHAR(255),
            role VARCHAR(100) DEFAULT 'Member',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ");

    $conn->query("
        CREATE TABLE IF NOT EXISTS pages (
            id VARCHAR(100) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            url TEXT NOT NULL,
            member_id VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL
        )
    ");

    $conn->query("
        CREATE TABLE IF NOT EXISTS posts (
            id VARCHAR(100) PRIMARY KEY,
            page_id VARCHAR(100),
            member_id VARCHAR(50),
            caption TEXT,
            likes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            views_count INT DEFAULT 0,
            shares_count INT DEFAULT 0,
            post_url TEXT,
            post_date DATETIME,
            last_metrics_update DATETIME,
            extraction_timestamp DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (page_id) REFERENCES pages(id) ON DELETE SET NULL,
            FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL,
            INDEX idx_page_id (page_id),
            INDEX idx_member_id (member_id),
            INDEX idx_post_date (post_date)
        )
    ");

    // Fetch data from Electron app API
    $electronAppUrl = 'http://localhost:3000';
    $response['debug'][] = "Fetching data from Electron app: " . $electronAppUrl;

    // Function to fetch data from Electron app
    function fetchFromElectronApp($url) {
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'method' => 'GET',
                'header' => 'Content-Type: application/json'
            ]
        ]);

        $data = @file_get_contents($url, false, $context);
        if ($data === false) {
            return null;
        }

        return json_decode($data, true);
    }

    $results = [
        'posts' => 0,
        'pages' => 0,
        'members' => 0,
        'errors' => []
    ];

    // Start transaction for data consistency
    $conn->begin_transaction();

    // 1. Extract and insert Members (core data only)
    $members = fetchFromElectronApp($electronAppUrl . '/api/team');
    if ($members && is_array($members)) {
        $response['debug'][] = "✅ Fetched " . count($members) . " members from Electron app";

        $memberStmt = $conn->prepare("
                REPLACE INTO members (id, name, email, role)
                VALUES (?, ?, ?, ?)
        ");

        foreach ($members as $member) {
            $id = $member['id'] ?? $member['memberId'] ?? 'member_' . uniqid();
            $name = $member['name'] ?? 'Unknown';
            $email = $member['email'] ?? '';
            $role = $member['role'] ?? 'Member';

            $memberStmt->bind_param("ssss", $id, $name, $email, $role);
            if ($memberStmt->execute()) {
                $results['members']++;
            }
        }
        $memberStmt->close();
    } else {
        $response['debug'][] = "❌ Could not fetch members from Electron app";
    }

    // 2. Extract and insert Pages with member relationships
    $pages = fetchFromElectronApp($electronAppUrl . '/api/pages');
    if ($pages && is_array($pages)) {
        $response['debug'][] = "✅ Fetched " . count($pages) . " pages from Electron app";

        $pageStmt = $conn->prepare("
                REPLACE INTO pages (id, name, url, member_id)
                VALUES (?, ?, ?, ?)
            ");

        foreach ($pages as $page) {
            $pageId = $page['id'] ?? $page['pageId'] ?? 'unknown_page';
            $name = $page['name'] ?? 'Unknown Page';
            $url = $page['link'] ?? $page['url'] ?? '';
            $memberId = $page['member_id'] ?? $page['memberId'] ?? null;

            $pageStmt->bind_param("ssss", $pageId, $name, $url, $memberId);
            if ($pageStmt->execute()) {
                $results['pages']++;
            }
        }
        $pageStmt->close();
    } else {
        $response['debug'][] = "❌ Could not fetch pages from Electron app";
    }

    // 3. Extract and insert Posts (main focus)
    $posts = fetchFromElectronApp($electronAppUrl . '/api/posts');
    if ($posts && is_array($posts)) {
        $response['debug'][] = "✅ Fetched " . count($posts) . " posts from Electron app - EXTRACTING CORE DATA";

        $postStmt = $conn->prepare("
                REPLACE INTO posts (
                    id, page_id, member_id, caption, likes_count, comments_count,
                    views_count, shares_count, post_url, post_date,
                    last_metrics_update, extraction_timestamp
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

        foreach ($posts as $post) {
            // Extract core post data - generate IDs from pageUrl like Updates page does
            $id = $post['normalizedTextHash'] ?? $post['id'] ?? 'post_' . uniqid();
            $pageUrl = $post['pageUrl'] ?? '';
            $pageId = generatePageId($pageUrl);
            $memberId = findMemberIdForPage($pageUrl, $members);
            $caption = $post['finalFilteredText'] ?? $post['caption'] ?? '';

            // Extract engagement metrics from engagement object
            $likes = $post['engagement']['likes'] ?? 0;
            $comments = $post['engagement']['comments'] ?? 0;
            $views = $post['engagement']['views'] ?? 0;
            $shares = $post['engagement']['shares'] ?? 0;

            // Extract URLs and timestamps
            $postUrl = $post['postUrl'] ?? '';
            $postDate = $post['enhancedPostTime']['fullDate'] ?? $post['timestamp'] ?? null;
            $lastUpdate = $post['lastMetricsUpdate'] ?? $post['timestamp'] ?? null;
            $extractionTime = $post['timestamp'] ?? null;

            // Convert timestamps to MySQL format
            if ($postDate) $postDate = date('Y-m-d H:i:s', strtotime($postDate));
            if ($lastUpdate) $lastUpdate = date('Y-m-d H:i:s', strtotime($lastUpdate));
            if ($extractionTime) $extractionTime = date('Y-m-d H:i:s', strtotime($extractionTime));

            $postStmt->bind_param(
                "ssssiiiissss",
                $id, $pageId, $memberId, $caption, $likes, $comments, $views, $shares,
                $postUrl, $postDate, $lastUpdate, $extractionTime
            );

            if ($postStmt->execute()) {
                $results['posts']++;
            } else {
                $results['errors'][] = "Post insert error: " . $postStmt->error;
            }
        }
        $postStmt->close();
    } else {
        $response['debug'][] = "❌ Could not fetch posts from Electron app";
    }

    // Commit transaction
    $conn->commit();
    
    $response['success'] = true;
    $response['message'] = 'Data extraction completed successfully!';
    $response['results'] = $results;
    $response['debug'][] = "✅ Transaction committed successfully";
    $response['debug'][] = "📊 Final Results: {$results['members']} members, {$results['pages']} pages, {$results['posts']} posts";

} catch (Exception $e) {
    // Rollback on error
    $conn->rollback();
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
    $response['results'] = $results;
    $response['debug'][] = "❌ Transaction rolled back due to error";
}

echo json_encode($response, JSON_PRETTY_PRINT);

// Helper functions
function generatePageId($pageUrl) {
    if (empty($pageUrl)) return 'N/A';

    // Extract page name from URL - same logic as Updates page
    if (preg_match('/facebook\.com\/([^\/\?]+)/', $pageUrl, $matches)) {
        return $matches[1];
    }

    // Fallback to last 8 characters - same as Updates page
    return substr($pageUrl, -8);
}

function findMemberIdForPage($pageUrl, $members) {
    if (empty($pageUrl) || !is_array($members)) return 'N/A';

    foreach ($members as $member) {
        if (isset($member['assignedPages']) && is_array($member['assignedPages'])) {
            foreach ($member['assignedPages'] as $assignedPage) {
                if (is_string($assignedPage)) {
                    // Use partial matching like Updates page
                    if (strpos($pageUrl, $assignedPage) !== false) {
                        return $member['id'] ?? 'N/A';
                    }
                } elseif (is_array($assignedPage) && isset($assignedPage['link'])) {
                    // Exact match for link objects
                    if ($pageUrl === $assignedPage['link']) {
                        return $member['id'] ?? 'N/A';
                    }
                }
            }
        }
    }

    return 'N/A';
}
?>
