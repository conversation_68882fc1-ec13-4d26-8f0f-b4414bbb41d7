// Export Viewer JavaScript

// Get URL parameters
function getUrlParams() {
    const params = new URLSearchParams(window.location.search);
    return {
        file: params.get('file'),
        type: params.get('type'),
        member: params.get('member')
    };
}

// Initialize theme
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-mode');
        document.getElementById('theme-icon').className = 'fas fa-sun';
        document.getElementById('theme-text').textContent = 'Light';
    }
}

// Toggle theme
function toggleTheme() {
    const body = document.body;
    const themeIcon = document.getElementById('theme-icon');
    const themeText = document.getElementById('theme-text');
    
    if (body.classList.contains('dark-mode')) {
        body.classList.remove('dark-mode');
        themeIcon.className = 'fas fa-moon';
        themeText.textContent = 'Dark';
        localStorage.setItem('theme', 'light');
    } else {
        body.classList.add('dark-mode');
        themeIcon.className = 'fas fa-sun';
        themeText.textContent = 'Light';
        localStorage.setItem('theme', 'dark');
    }
}

// Format file size
function formatFileSize(sizeStr) {
    if (!sizeStr) return 'Unknown';
    return sizeStr;
}

// Format date
function formatDate(dateStr) {
    if (!dateStr) return 'Unknown';
    const date = new Date(dateStr);
    return date.toLocaleString();
}

// Render file information
function renderFileInfo(fileData, params) {
    const infoGrid = document.getElementById('file-info-grid');
    
    infoGrid.innerHTML = `
        <div class="info-item">
            <div class="info-label">File Name</div>
            <div class="info-value">${fileData.name || 'Unknown'}</div>
        </div>
        <div class="info-item">
            <div class="info-label">Team Member</div>
            <div class="info-value">${params.member || 'Unknown'}</div>
        </div>
        <div class="info-item">
            <div class="info-label">File Type</div>
            <div class="info-value">${fileData.type?.toUpperCase() || 'Unknown'}</div>
        </div>
        <div class="info-item">
            <div class="info-label">File Size</div>
            <div class="info-value">${formatFileSize(fileData.size)}</div>
        </div>
        <div class="info-item">
            <div class="info-label">Records Count</div>
            <div class="info-value">${fileData.rowCount || 0}</div>
        </div>
        <div class="info-item">
            <div class="info-label">Last Modified</div>
            <div class="info-value">${formatDate(fileData.lastModified)}</div>
        </div>
    `;
}

// Render JSON content as modern visual interface
function renderJsonContent(jsonData) {
    const container = document.getElementById('content-container');

    // Check if this is export data with posts
    if (jsonData.posts && Array.isArray(jsonData.posts)) {
        renderPostsInterface(jsonData, container);
    } else {
        // Fallback to code view for non-post JSON
        renderCodeView(jsonData, container);
    }
}

// Render posts in a comprehensive analytics interface
function renderPostsInterface(exportData, container) {
    const { teamMember, analytics, posts } = exportData;

    // Calculate detailed analytics
    const detailedAnalytics = calculateDetailedAnalytics(posts);

    container.innerHTML = `
        <div class="posts-interface">
            <!-- Member Header -->
            <div class="member-header">
                <div class="member-info">
                    <h2><i class="fas fa-user"></i> ${teamMember?.name || 'Unknown Member'}</h2>
                    <p class="member-role">${teamMember?.role || 'Team Member'}</p>
                </div>
                <div class="export-info">
                    <div class="export-date">
                        <i class="fas fa-calendar"></i>
                        Exported: ${new Date().toLocaleDateString()}
                    </div>
                    <div class="total-engagement-badge">
                        ${formatNumber(detailedAnalytics.totalEngagement)} total interactions
                    </div>
                </div>
            </div>

            <!-- Analytics Dashboard -->
            <div class="analytics-dashboard">
                <div class="analytics-grid">
                    <div class="analytics-card primary">
                        <div class="card-icon"><i class="fas fa-file-alt"></i></div>
                        <div class="card-content">
                            <div class="card-number">${detailedAnalytics.totalPosts}</div>
                            <div class="card-label">Total Posts</div>
                        </div>
                    </div>
                    <div class="analytics-card success">
                        <div class="card-icon"><i class="fas fa-heart"></i></div>
                        <div class="card-content">
                            <div class="card-number">${formatNumber(detailedAnalytics.totalLikes)}</div>
                            <div class="card-label">Total Likes</div>
                        </div>
                    </div>
                    <div class="analytics-card info">
                        <div class="card-icon"><i class="fas fa-comment"></i></div>
                        <div class="card-content">
                            <div class="card-number">${formatNumber(detailedAnalytics.totalComments)}</div>
                            <div class="card-label">Total Comments</div>
                        </div>
                    </div>
                    <div class="analytics-card warning">
                        <div class="card-icon"><i class="fas fa-share"></i></div>
                        <div class="card-content">
                            <div class="card-number">${formatNumber(detailedAnalytics.totalShares)}</div>
                            <div class="card-label">Total Shares</div>
                        </div>
                    </div>
                </div>

                <!-- Performance Insights -->
                <div class="insights-section">
                    <h3><i class="fas fa-chart-line"></i> Performance Insights</h3>
                    <div class="insights-grid">
                        <div class="insight-card">
                            <div class="insight-title">Best Performing Post</div>
                            <div class="insight-content">
                                <div class="insight-number">${formatNumber(detailedAnalytics.bestPost?.engagement || 0)}</div>
                                <div class="insight-label">interactions</div>
                                <div class="insight-date">${formatPostDate(detailedAnalytics.bestPost?.date)}</div>
                            </div>
                        </div>
                        <div class="insight-card">
                            <div class="insight-title">Average Engagement</div>
                            <div class="insight-content">
                                <div class="insight-number">${Math.round(detailedAnalytics.avgEngagement)}</div>
                                <div class="insight-label">per post</div>
                            </div>
                        </div>
                        <div class="insight-card">
                            <div class="insight-title">Most Active Day</div>
                            <div class="insight-content">
                                <div class="insight-number">${detailedAnalytics.mostActiveDay?.day || 'N/A'}</div>
                                <div class="insight-label">${detailedAnalytics.mostActiveDay?.count || 0} posts</div>
                            </div>
                        </div>
                        <div class="insight-card">
                            <div class="insight-title">Content Mix</div>
                            <div class="insight-content">
                                <div class="content-breakdown">
                                    <div class="content-type">
                                        <span class="type-icon video"><i class="fas fa-video"></i></span>
                                        ${detailedAnalytics.contentTypes.video}
                                    </div>
                                    <div class="content-type">
                                        <span class="type-icon photo"><i class="fas fa-image"></i></span>
                                        ${detailedAnalytics.contentTypes.photo}
                                    </div>
                                    <div class="content-type">
                                        <span class="type-icon text"><i class="fas fa-file-alt"></i></span>
                                        ${detailedAnalytics.contentTypes.text}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Controls -->
            <div class="controls-section">
                <div class="filters">
                    <div class="filter-group">
                        <label for="page-filter"><i class="fas fa-globe"></i> Filter by Page:</label>
                        <select id="page-filter" onchange="filterByPage(this.value)">
                            <option value="">All Pages</option>
                            ${generatePageOptions(posts)}
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="date-filter"><i class="fas fa-calendar"></i> Date Range:</label>
                        <select id="date-filter" onchange="filterByDate(this.value)">
                            <option value="">All Time</option>
                            <option value="today">Today</option>
                            <option value="week">This Week</option>
                            <option value="month">This Month</option>
                            <option value="3months">Last 3 Months</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="sort-filter"><i class="fas fa-sort"></i> Sort by:</label>
                        <select id="sort-filter" onchange="sortPosts(this.value)">
                            <option value="date">Date (Newest)</option>
                            <option value="engagement">Total Engagement</option>
                            <option value="likes">Most Likes</option>
                            <option value="comments">Most Comments</option>
                            <option value="shares">Most Shares</option>
                        </select>
                    </div>
                </div>
                <div class="view-controls">
                    <button class="btn-view active" onclick="toggleView('cards')" data-view="cards">
                        <i class="fas fa-th-large"></i> Cards
                    </button>
                    <button class="btn-view" onclick="toggleView('list')" data-view="list">
                        <i class="fas fa-list"></i> List
                    </button>
                    <button class="btn-view" onclick="showRawJson()">
                        <i class="fas fa-code"></i> Raw JSON
                    </button>
                </div>
            </div>

            <!-- Posts Container -->
            <div class="posts-container cards-view" id="posts-container">
                ${renderEnhancedPostCards(posts)}
            </div>

            <!-- Pagination -->
            <div class="pagination-container" id="pagination-container">
                ${renderPagination(posts.length)}
            </div>
        </div>
    `;

    // Store data globally
    window.exportData = exportData;
    window.filteredPosts = posts;
    window.currentPage = 1;
    window.postsPerPage = 10;

    // Show AI chat section for posts data
    const aiSection = document.getElementById('ai-chat-section');
    if (aiSection) {
        aiSection.style.display = 'block';

        // Initialize AI chat with a small delay to ensure DOM is ready
        setTimeout(() => {
            initializeAIChat();

            // Additional safety check to ensure input works
            setTimeout(() => {
                ensureChatInputWorks();
            }, 200);
        }, 100);
    }
}

// Render simple post cards like the image you showed
function renderSimplePostCards(posts) {
    if (!posts || posts.length === 0) {
        return '<div class="no-posts"><i class="fas fa-inbox"></i><p>No posts found</p></div>';
    }

    return posts.map(post => `
        <div class="simple-post-card">
            <!-- Post type indicator -->
            <div class="post-type-indicator">
                <i class="fas fa-${getPostIcon(post)}"></i>
                ${post.media?.postType || 'Text'}
            </div>

            <!-- Post date -->
            <div class="post-date">
                ${formatPostDate(post.timing?.postDate)}
            </div>

            <!-- Total engagement badge -->
            <div class="engagement-badge">
                ${formatNumber(post.engagement?.total || 0)} interactions
            </div>

            <!-- Post content -->
            <div class="post-content-text">
                ${post.content?.text || 'No content available'}
            </div>

            <!-- Video duration if applicable -->
            ${post.media?.videoDuration ? `
                <div class="video-duration">
                    <i class="fas fa-play"></i> ${post.media.videoDuration}
                </div>
            ` : ''}

            <!-- Engagement metrics -->
            <div class="engagement-metrics">
                <div class="metric">
                    <i class="fas fa-heart"></i>
                    <span>${post.engagement?.likes || 0}</span>
                </div>
                <div class="metric">
                    <i class="fas fa-comment"></i>
                    <span>${post.engagement?.comments || 0}</span>
                </div>
                <div class="metric">
                    <i class="fas fa-share"></i>
                    <span>${post.engagement?.shares || 0}</span>
                </div>
                ${post.engagement?.views ? `
                    <div class="metric">
                        <i class="fas fa-eye"></i>
                        <span>${formatNumber(post.engagement.views)}</span>
                    </div>
                ` : ''}
            </div>

            <!-- Page name -->
            <div class="page-name">
                <i class="fas fa-globe"></i>
                ${post.page?.name || 'Unknown Page'}
            </div>
        </div>
    `).join('');
}

// Calculate detailed analytics from posts data
function calculateDetailedAnalytics(posts) {
    if (!posts || posts.length === 0) {
        return {
            totalPosts: 0,
            totalEngagement: 0,
            totalLikes: 0,
            totalComments: 0,
            totalShares: 0,
            avgEngagement: 0,
            bestPost: null,
            mostActiveDay: null,
            contentTypes: { video: 0, photo: 0, text: 0 }
        };
    }

    let totalLikes = 0, totalComments = 0, totalShares = 0, totalViews = 0;
    let bestPost = { engagement: 0, date: null };
    const dayCount = {};
    const contentTypes = { video: 0, photo: 0, text: 0 };

    posts.forEach(post => {
        const likes = post.engagement?.likes || 0;
        const comments = post.engagement?.comments || 0;
        const shares = post.engagement?.shares || 0;
        const views = post.engagement?.views || 0;
        const totalEng = likes + comments + shares + views;

        totalLikes += likes;
        totalComments += comments;
        totalShares += shares;
        totalViews += views;

        // Track best performing post
        if (totalEng > bestPost.engagement) {
            bestPost = {
                engagement: totalEng,
                date: post.timing?.postDate || post.timing?.timestamp,
                post: post
            };
        }

        // Count posts by day
        if (post.timing?.postDate) {
            const day = new Date(post.timing.postDate).toLocaleDateString('en-US', { weekday: 'long' });
            dayCount[day] = (dayCount[day] || 0) + 1;
        }

        // Count content types
        const type = post.media?.postType?.toLowerCase();
        if (type === 'video') contentTypes.video++;
        else if (type === 'photo' || type === 'image') contentTypes.photo++;
        else contentTypes.text++;
    });

    // Find most active day
    const mostActiveDay = Object.entries(dayCount).reduce((max, [day, count]) =>
        count > (max.count || 0) ? { day, count } : max, {});

    const totalEngagement = totalLikes + totalComments + totalShares + totalViews;

    return {
        totalPosts: posts.length,
        totalEngagement,
        totalLikes,
        totalComments,
        totalShares,
        totalViews,
        avgEngagement: posts.length > 0 ? totalEngagement / posts.length : 0,
        bestPost,
        mostActiveDay,
        contentTypes
    };
}

// Generate page filter options
function generatePageOptions(posts) {
    const pages = new Set();
    posts.forEach(post => {
        if (post.page?.name) {
            pages.add(post.page.name);
        }
    });

    return Array.from(pages).map(page =>
        `<option value="${escapeHtml(page)}">${escapeHtml(page)}</option>`
    ).join('');
}

// Render enhanced post cards with more details
function renderEnhancedPostCards(posts) {
    if (!posts || posts.length === 0) {
        return '<div class="no-posts"><i class="fas fa-inbox"></i><p>No posts found</p></div>';
    }

    const startIndex = (window.currentPage - 1) * window.postsPerPage;
    const endIndex = startIndex + window.postsPerPage;
    const paginatedPosts = posts.slice(startIndex, endIndex);

    return paginatedPosts.map((post, index) => `
        <div class="enhanced-post-card" data-post-id="${post.id || index}">
            <div class="post-header-enhanced">
                <div class="post-type-badge ${getPostTypeClass(post)}">
                    <i class="fas fa-${getPostIcon(post)}"></i>
                    ${post.media?.postType || 'Text'}
                </div>
                <div class="post-date-enhanced">
                    <i class="fas fa-clock"></i>
                    ${formatPostDate(post.timing?.postDate)}
                </div>
                <div class="engagement-score">
                    <div class="score-number">${calculateEngagementScore(post)}</div>
                    <div class="score-label">Score</div>
                </div>
            </div>

            <div class="post-content-enhanced">
                <div class="post-text-preview">
                    ${truncateText(post.content?.text || '', 150)}
                    ${(post.content?.text || '').length > 150 ? `
                        <button class="read-more-btn" onclick="togglePostText(${index})">
                            <i class="fas fa-chevron-down"></i> Read More
                        </button>
                    ` : ''}
                </div>
                <div class="post-text-full" id="full-text-${index}" style="display: none;">
                    ${escapeHtml(post.content?.text || '')}
                    <button class="read-less-btn" onclick="togglePostText(${index})">
                        <i class="fas fa-chevron-up"></i> Read Less
                    </button>
                </div>

                ${post.media?.videoDuration ? `
                    <div class="video-info">
                        <i class="fas fa-play-circle"></i>
                        <span>Duration: ${post.media.videoDuration}</span>
                    </div>
                ` : ''}
            </div>

            <div class="engagement-details">
                <div class="engagement-row">
                    <div class="engagement-item likes">
                        <i class="fas fa-heart"></i>
                        <span class="count">${formatNumber(post.engagement?.likes || 0)}</span>
                        <span class="label">Likes</span>
                    </div>
                    <div class="engagement-item comments">
                        <i class="fas fa-comment"></i>
                        <span class="count">${formatNumber(post.engagement?.comments || 0)}</span>
                        <span class="label">Comments</span>
                    </div>
                    <div class="engagement-item shares">
                        <i class="fas fa-share"></i>
                        <span class="count">${formatNumber(post.engagement?.shares || 0)}</span>
                        <span class="label">Shares</span>
                    </div>
                    ${post.engagement?.views ? `
                        <div class="engagement-item views">
                            <i class="fas fa-eye"></i>
                            <span class="count">${formatNumber(post.engagement.views)}</span>
                            <span class="label">Views</span>
                        </div>
                    ` : ''}
                </div>
                <div class="engagement-bar">
                    <div class="bar-segment likes" style="width: ${getEngagementPercentage(post, 'likes')}%"></div>
                    <div class="bar-segment comments" style="width: ${getEngagementPercentage(post, 'comments')}%"></div>
                    <div class="bar-segment shares" style="width: ${getEngagementPercentage(post, 'shares')}%"></div>
                    ${post.engagement?.views ? `<div class="bar-segment views" style="width: ${getEngagementPercentage(post, 'views')}%"></div>` : ''}
                </div>
            </div>

            <div class="post-footer-enhanced">
                <div class="page-info">
                    <i class="fas fa-globe"></i>
                    <span class="page-name">${post.page?.name || 'Unknown Page'}</span>
                </div>
                <div class="post-actions">
                    <button class="action-btn" onclick="viewPostDetails(${index})" title="View Details">
                        <i class="fas fa-info-circle"></i>
                    </button>
                    <button class="action-btn" onclick="sharePost(${index})" title="Share">
                        <i class="fas fa-external-link-alt"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Helper functions
function getPostIcon(post) {
    if (post.media?.postType === 'Video') return 'video';
    if (post.media?.postType === 'Photo') return 'image';
    return 'file-alt';
}

function getPostTypeClass(post) {
    const type = post.media?.postType?.toLowerCase();
    if (type === 'video') return 'type-video';
    if (type === 'photo' || type === 'image') return 'type-photo';
    return 'type-text';
}

function formatPostDate(dateStr) {
    if (!dateStr) return 'Unknown';
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
    });
}

function truncateText(text, maxLength) {
    if (!text) return '';
    if (text.length <= maxLength) return escapeHtml(text);
    return escapeHtml(text.substring(0, maxLength)) + '...';
}

function formatNumber(num) {
    if (!num) return '0';
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
}

function calculateEngagementScore(post) {
    const likes = post.engagement?.likes || 0;
    const comments = post.engagement?.comments || 0;
    const shares = post.engagement?.shares || 0;
    const views = post.engagement?.views || 0;

    // Weighted scoring: comments and shares worth more than likes
    const score = likes + (comments * 3) + (shares * 5) + (views * 0.1);
    return Math.round(score);
}

function getEngagementPercentage(post, type) {
    const likes = post.engagement?.likes || 0;
    const comments = post.engagement?.comments || 0;
    const shares = post.engagement?.shares || 0;
    const views = post.engagement?.views || 0;
    const total = likes + comments + shares + views;

    if (total === 0) return 0;

    const value = post.engagement?.[type] || 0;
    return Math.round((value / total) * 100);
}

// Filter posts by page
function filterByPage(pageName) {
    if (!window.exportData?.posts) return;

    let filtered = window.exportData.posts;

    if (pageName) {
        filtered = filtered.filter(post => post.page?.name === pageName);
    }

    // Apply date filter if active
    const dateFilter = document.getElementById('date-filter')?.value;
    if (dateFilter) {
        filtered = applyDateFilter(filtered, dateFilter);
    }

    window.filteredPosts = filtered;
    window.currentPage = 1;
    updatePostsDisplay();
}

// Filter posts by date range
function filterByDate(dateRange) {
    if (!window.exportData?.posts) return;

    let filtered = window.exportData.posts;

    // Apply page filter if active
    const pageFilter = document.getElementById('page-filter')?.value;
    if (pageFilter) {
        filtered = filtered.filter(post => post.page?.name === pageFilter);
    }

    // Apply date filter
    if (dateRange) {
        filtered = applyDateFilter(filtered, dateRange);
    }

    window.filteredPosts = filtered;
    window.currentPage = 1;
    updatePostsDisplay();
}

// Apply date filter logic
function applyDateFilter(posts, dateRange) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    return posts.filter(post => {
        if (!post.timing?.postDate) return false;

        const postDate = new Date(post.timing.postDate);

        switch (dateRange) {
            case 'today':
                return postDate >= today;
            case 'week':
                const weekAgo = new Date(today);
                weekAgo.setDate(weekAgo.getDate() - 7);
                return postDate >= weekAgo;
            case 'month':
                const monthAgo = new Date(today);
                monthAgo.setMonth(monthAgo.getMonth() - 1);
                return postDate >= monthAgo;
            case '3months':
                const threeMonthsAgo = new Date(today);
                threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
                return postDate >= threeMonthsAgo;
            default:
                return true;
        }
    });
}

// Sort posts
function sortPosts(sortBy) {
    if (!window.filteredPosts) return;

    const posts = [...window.filteredPosts];

    posts.sort((a, b) => {
        switch (sortBy) {
            case 'date':
                return new Date(b.timing?.postDate || 0) - new Date(a.timing?.postDate || 0);
            case 'engagement':
                const aEng = (a.engagement?.likes || 0) + (a.engagement?.comments || 0) + (a.engagement?.shares || 0) + (a.engagement?.views || 0);
                const bEng = (b.engagement?.likes || 0) + (b.engagement?.comments || 0) + (b.engagement?.shares || 0) + (b.engagement?.views || 0);
                return bEng - aEng;
            case 'likes':
                return (b.engagement?.likes || 0) - (a.engagement?.likes || 0);
            case 'comments':
                return (b.engagement?.comments || 0) - (a.engagement?.comments || 0);
            case 'shares':
                return (b.engagement?.shares || 0) - (a.engagement?.shares || 0);
            default:
                return 0;
        }
    });

    window.filteredPosts = posts;
    window.currentPage = 1;
    updatePostsDisplay();
}

// Toggle view mode
function toggleView(viewMode) {
    const container = document.getElementById('posts-container');
    const buttons = document.querySelectorAll('.btn-view');

    // Update button states
    buttons.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.view === viewMode) {
            btn.classList.add('active');
        }
    });

    // Update container class
    container.className = `posts-container ${viewMode}-view`;

    // Re-render posts if needed
    if (viewMode === 'list') {
        container.innerHTML = renderListView(window.filteredPosts);
    } else {
        updatePostsDisplay();
    }
}

// Render list view
function renderListView(posts) {
    if (!posts || posts.length === 0) {
        return '<div class="no-posts"><i class="fas fa-inbox"></i><p>No posts found</p></div>';
    }

    const startIndex = (window.currentPage - 1) * window.postsPerPage;
    const endIndex = startIndex + window.postsPerPage;
    const paginatedPosts = posts.slice(startIndex, endIndex);

    return `
        <div class="list-header">
            <div class="list-col">Content</div>
            <div class="list-col">Page</div>
            <div class="list-col">Date</div>
            <div class="list-col">Engagement</div>
            <div class="list-col">Actions</div>
        </div>
        ${paginatedPosts.map((post, index) => `
            <div class="list-row">
                <div class="list-col content-col">
                    <div class="post-type-mini ${getPostTypeClass(post)}">
                        <i class="fas fa-${getPostIcon(post)}"></i>
                    </div>
                    <div class="content-preview">
                        ${truncateText(post.content?.text || '', 80)}
                    </div>
                </div>
                <div class="list-col page-col">
                    ${post.page?.name || 'Unknown'}
                </div>
                <div class="list-col date-col">
                    ${formatPostDate(post.timing?.postDate)}
                </div>
                <div class="list-col engagement-col">
                    <div class="engagement-mini">
                        <span class="mini-stat">❤️ ${formatNumber(post.engagement?.likes || 0)}</span>
                        <span class="mini-stat">💬 ${formatNumber(post.engagement?.comments || 0)}</span>
                        <span class="mini-stat">🔄 ${formatNumber(post.engagement?.shares || 0)}</span>
                    </div>
                </div>
                <div class="list-col actions-col">
                    <button class="action-btn-mini" onclick="viewPostDetails(${index})" title="View Details">
                        <i class="fas fa-eye"></i>
                    </button>
                </div>
            </div>
        `).join('')}
    `;
}

// Update posts display
function updatePostsDisplay() {
    const container = document.getElementById('posts-container');
    const paginationContainer = document.getElementById('pagination-container');

    if (container.classList.contains('list-view')) {
        container.innerHTML = renderListView(window.filteredPosts);
    } else {
        container.innerHTML = renderEnhancedPostCards(window.filteredPosts);
    }

    if (paginationContainer) {
        paginationContainer.innerHTML = renderPagination(window.filteredPosts.length);
    }
}

// Pagination functions
function renderPagination(totalPosts) {
    const totalPages = Math.ceil(totalPosts / window.postsPerPage);

    if (totalPages <= 1) return '';

    let pagination = '<div class="pagination">';

    // Previous button
    if (window.currentPage > 1) {
        pagination += `<button class="page-btn" onclick="changePage(${window.currentPage - 1})">
            <i class="fas fa-chevron-left"></i> Previous
        </button>`;
    }

    // Page numbers
    const startPage = Math.max(1, window.currentPage - 2);
    const endPage = Math.min(totalPages, window.currentPage + 2);

    if (startPage > 1) {
        pagination += `<button class="page-btn" onclick="changePage(1)">1</button>`;
        if (startPage > 2) {
            pagination += '<span class="page-dots">...</span>';
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        pagination += `<button class="page-btn ${i === window.currentPage ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            pagination += '<span class="page-dots">...</span>';
        }
        pagination += `<button class="page-btn" onclick="changePage(${totalPages})">${totalPages}</button>`;
    }

    // Next button
    if (window.currentPage < totalPages) {
        pagination += `<button class="page-btn" onclick="changePage(${window.currentPage + 1})">
            Next <i class="fas fa-chevron-right"></i>
        </button>`;
    }

    pagination += '</div>';

    // Add page info
    const startItem = (window.currentPage - 1) * window.postsPerPage + 1;
    const endItem = Math.min(window.currentPage * window.postsPerPage, totalPosts);

    pagination += `<div class="page-info">
        Showing ${startItem}-${endItem} of ${totalPosts} posts
    </div>`;

    return pagination;
}

function changePage(page) {
    window.currentPage = page;
    updatePostsDisplay();

    // Scroll to top of posts
    document.getElementById('posts-container').scrollIntoView({ behavior: 'smooth' });
}

// Post interaction functions
function togglePostText(index) {
    const preview = document.querySelector(`#posts-container .enhanced-post-card:nth-child(${index + 1}) .post-text-preview`);
    const full = document.getElementById(`full-text-${index}`);

    if (full.style.display === 'none') {
        preview.style.display = 'none';
        full.style.display = 'block';
    } else {
        preview.style.display = 'block';
        full.style.display = 'none';
    }
}

function viewPostDetails(index) {
    const post = window.filteredPosts[index];
    if (!post) return;

    // Create detailed modal
    const modal = document.createElement('div');
    modal.className = 'post-detail-modal';
    modal.innerHTML = `
        <div class="modal-content-detailed">
            <div class="modal-header-detailed">
                <h3><i class="fas fa-${getPostIcon(post)}"></i> Post Details</h3>
                <button class="modal-close" onclick="closeDetailModal()">&times;</button>
            </div>
            <div class="modal-body-detailed">
                <div class="post-detail-grid">
                    <div class="detail-section">
                        <h4><i class="fas fa-info-circle"></i> Post Information</h4>
                        <div class="detail-item">
                            <strong>Type:</strong> ${post.media?.postType || 'Text'}
                        </div>
                        <div class="detail-item">
                            <strong>Page:</strong> ${post.page?.name || 'Unknown'}
                        </div>
                        <div class="detail-item">
                            <strong>Date:</strong> ${post.timing?.postDateTime || formatPostDate(post.timing?.postDate)}
                        </div>
                        ${post.media?.videoDuration ? `
                            <div class="detail-item">
                                <strong>Duration:</strong> ${post.media.videoDuration}
                            </div>
                        ` : ''}
                        <div class="detail-item">
                            <strong>Word Count:</strong> ${(post.content?.text || '').split(' ').length} words
                        </div>
                    </div>

                    <div class="detail-section">
                        <h4><i class="fas fa-chart-bar"></i> Engagement Analytics</h4>
                        <div class="engagement-chart">
                            <div class="chart-item">
                                <div class="chart-bar likes" style="height: ${getEngagementPercentage(post, 'likes')}%"></div>
                                <div class="chart-label">Likes<br>${formatNumber(post.engagement?.likes || 0)}</div>
                            </div>
                            <div class="chart-item">
                                <div class="chart-bar comments" style="height: ${getEngagementPercentage(post, 'comments')}%"></div>
                                <div class="chart-label">Comments<br>${formatNumber(post.engagement?.comments || 0)}</div>
                            </div>
                            <div class="chart-item">
                                <div class="chart-bar shares" style="height: ${getEngagementPercentage(post, 'shares')}%"></div>
                                <div class="chart-label">Shares<br>${formatNumber(post.engagement?.shares || 0)}</div>
                            </div>
                            ${post.engagement?.views ? `
                                <div class="chart-item">
                                    <div class="chart-bar views" style="height: ${getEngagementPercentage(post, 'views')}%"></div>
                                    <div class="chart-label">Views<br>${formatNumber(post.engagement.views)}</div>
                                </div>
                            ` : ''}
                        </div>
                        <div class="engagement-score-detail">
                            <strong>Engagement Score:</strong> ${calculateEngagementScore(post)}
                        </div>
                    </div>
                </div>

                <div class="detail-section full-width">
                    <h4><i class="fas fa-file-text"></i> Full Content</h4>
                    <div class="full-content">
                        ${escapeHtml(post.content?.text || 'No content available')}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Add click outside to close
    modal.addEventListener('click', (e) => {
        if (e.target === modal) closeDetailModal();
    });
}

function closeDetailModal() {
    const modal = document.querySelector('.post-detail-modal');
    if (modal) modal.remove();
}

function sharePost(index) {
    const post = window.filteredPosts[index];
    if (!post) return;

    // Create shareable summary
    const summary = `Post by ${window.exportData.teamMember?.name || 'Team Member'}
Page: ${post.page?.name || 'Unknown'}
Date: ${formatPostDate(post.timing?.postDate)}
Engagement: ${formatNumber((post.engagement?.likes || 0) + (post.engagement?.comments || 0) + (post.engagement?.shares || 0))} interactions

Content: ${(post.content?.text || '').substring(0, 200)}${(post.content?.text || '').length > 200 ? '...' : ''}`;

    if (navigator.share) {
        navigator.share({
            title: 'Post Analytics',
            text: summary
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(summary).then(() => {
            showNotification('Post details copied to clipboard!', 'success');
        });
    }
}

// Show raw JSON view
function showRawJson() {
    if (!window.exportData) return;

    const container = document.getElementById('content-container');
    renderCodeView(window.exportData, container);
}

// Utility function for notifications (if not already defined)
function showNotification(message, type) {
    // Simple notification implementation
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideIn 0.3s ease;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Render code view (fallback)
function renderCodeView(jsonData, container) {
    const formattedJson = JSON.stringify(jsonData, null, 2);

    container.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <div style="display: flex; gap: 10px;">
                <button class="btn btn-secondary" onclick="copyJsonToClipboard()" title="Copy JSON to clipboard">
                    <i class="fas fa-copy"></i> Copy
                </button>
                ${jsonData.posts ? `
                    <button class="btn btn-secondary" onclick="renderJsonContent(window.exportData)" title="Back to visual view">
                        <i class="fas fa-eye"></i> Visual
                    </button>
                ` : ''}
            </div>
            <input type="text" id="json-search" placeholder="Search in JSON..." style="padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; background: var(--bg-tertiary); color: var(--text-primary);" oninput="searchInJson(this.value)">
        </div>
        <div class="json-viewer" id="json-content">
            <pre><code id="json-code">${syntaxHighlightJson(formattedJson)}</code></pre>
        </div>
    `;
}

// Add syntax highlighting to JSON
function syntaxHighlightJson(json) {
    json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
    return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
        let cls = 'json-number';
        if (/^"/.test(match)) {
            if (/:$/.test(match)) {
                cls = 'json-key';
            } else {
                cls = 'json-string';
            }
        } else if (/true|false/.test(match)) {
            cls = 'json-boolean';
        } else if (/null/.test(match)) {
            cls = 'json-null';
        }
        return '<span class="' + cls + '">' + match + '</span>';
    });
}

// Copy JSON to clipboard
function copyJsonToClipboard() {
    const jsonCode = document.getElementById('json-code');
    if (jsonCode) {
        const text = jsonCode.textContent;
        navigator.clipboard.writeText(text).then(() => {
            showNotification('JSON copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy:', err);
            showNotification('Failed to copy JSON', 'error');
        });
    }
}

// Simple notification system for the viewer
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 1000;
        animation: slideIn 0.3s ease;
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Search in JSON
function searchInJson(query) {
    const jsonCode = document.getElementById('json-code');
    if (!jsonCode) return;

    const originalContent = jsonCode.getAttribute('data-original') || jsonCode.innerHTML;
    if (!jsonCode.getAttribute('data-original')) {
        jsonCode.setAttribute('data-original', originalContent);
    }

    if (!query.trim()) {
        jsonCode.innerHTML = originalContent;
        return;
    }

    const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
    const highlighted = originalContent.replace(regex, '<mark style="background: #ffeb3b; color: #000;">$1</mark>');
    jsonCode.innerHTML = highlighted;
}

// Escape regex special characters
function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Render CSV content as a table
function renderCsvContent(csvContent) {
    const container = document.getElementById('content-container');

    try {
        const lines = csvContent.split('\n').filter(line => line.trim());
        if (lines.length === 0) {
            container.innerHTML = '<div class="error"><i class="fas fa-exclamation-triangle"></i><p>CSV file is empty</p></div>';
            return;
        }

        // Parse CSV (simple parsing - assumes comma-separated values)
        const headers = parseCsvLine(lines[0]);
        const rows = lines.slice(1).map(line => parseCsvLine(line)).filter(row => row.length > 0);

        // Add CSV controls
        container.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; flex-wrap: wrap; gap: 10px;">
                <div style="display: flex; gap: 10px;">
                    <button class="btn btn-secondary" onclick="copyCsvToClipboard()" title="Copy CSV to clipboard">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                    <button class="btn btn-secondary" onclick="exportCsvAsJson()" title="Convert to JSON">
                        <i class="fas fa-code"></i> JSON
                    </button>
                </div>
                <div style="display: flex; gap: 10px; align-items: center;">
                    <input type="text" id="csv-search" placeholder="Search in table..." style="padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px; background: var(--bg-tertiary); color: var(--text-primary);" oninput="searchInCsv(this.value)">
                    <span style="color: var(--text-secondary); font-size: 0.9rem;">${rows.length} rows</span>
                </div>
            </div>
            <div class="table-container" id="csv-table-container">
                <table class="csv-table" id="csv-table">
                    <thead>
                        <tr>
                            ${headers.map((header, index) => `<th onclick="sortCsvTable(${index})" style="cursor: pointer;" title="Click to sort">${escapeHtml(header)} <i class="fas fa-sort" style="opacity: 0.5;"></i></th>`).join('')}
                        </tr>
                    </thead>
                    <tbody id="csv-tbody">
                        ${rows.map(row => `
                            <tr>
                                ${headers.map((header, index) => {
                                    const cellValue = row[index] || '';
                                    return `<td title="${escapeHtml(cellValue)}">${escapeHtml(cellValue)}</td>`;
                                }).join('')}
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;

        // Store original data for sorting and searching
        window.csvData = { headers, rows };

    } catch (error) {
        console.error('Error parsing CSV:', error);
        container.innerHTML = `
            <div class="error">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Error parsing CSV file: ${error.message}</p>
            </div>
        `;
    }
}

// Copy CSV to clipboard
function copyCsvToClipboard() {
    const table = document.getElementById('csv-table');
    if (table) {
        let csvText = '';
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('th, td');
            const rowData = Array.from(cells).map(cell => `"${cell.textContent.replace(/"/g, '""')}"`);
            csvText += rowData.join(',') + '\n';
        });

        navigator.clipboard.writeText(csvText).then(() => {
            showNotification('CSV copied to clipboard!', 'success');
        }).catch(err => {
            console.error('Failed to copy:', err);
            showNotification('Failed to copy CSV', 'error');
        });
    }
}

// Convert CSV to JSON and display
function exportCsvAsJson() {
    if (!window.csvData) return;

    const { headers, rows } = window.csvData;
    const jsonData = rows.map(row => {
        const obj = {};
        headers.forEach((header, index) => {
            obj[header] = row[index] || '';
        });
        return obj;
    });

    // Open JSON in new viewer
    const jsonWindow = window.open('', '_blank', 'width=800,height=600');
    jsonWindow.document.write(`
        <html>
            <head><title>CSV as JSON</title></head>
            <body style="font-family: monospace; padding: 20px; background: #1e1e1e; color: #d4d4d4;">
                <pre>${JSON.stringify(jsonData, null, 2)}</pre>
            </body>
        </html>
    `);
}

// Search in CSV table
function searchInCsv(query) {
    const tbody = document.getElementById('csv-tbody');
    const rows = tbody.querySelectorAll('tr');

    if (!query.trim()) {
        rows.forEach(row => row.style.display = '');
        return;
    }

    const searchTerm = query.toLowerCase();
    rows.forEach(row => {
        const text = row.textContent.toLowerCase();
        row.style.display = text.includes(searchTerm) ? '' : 'none';
    });
}

// Sort CSV table by column
let sortDirection = {};
function sortCsvTable(columnIndex) {
    if (!window.csvData) return;

    const { headers, rows } = window.csvData;
    const isAscending = !sortDirection[columnIndex];
    sortDirection[columnIndex] = isAscending;

    const sortedRows = [...rows].sort((a, b) => {
        const aVal = a[columnIndex] || '';
        const bVal = b[columnIndex] || '';

        // Try to parse as numbers
        const aNum = parseFloat(aVal);
        const bNum = parseFloat(bVal);

        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        }

        // String comparison
        return isAscending ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);
    });

    // Update table
    const tbody = document.getElementById('csv-tbody');
    tbody.innerHTML = sortedRows.map(row => `
        <tr>
            ${headers.map((header, index) => {
                const cellValue = row[index] || '';
                return `<td title="${escapeHtml(cellValue)}">${escapeHtml(cellValue)}</td>`;
            }).join('')}
        </tr>
    `).join('');

    // Update sort indicators
    const ths = document.querySelectorAll('th');
    ths.forEach((th, index) => {
        const icon = th.querySelector('i');
        if (index === columnIndex) {
            icon.className = isAscending ? 'fas fa-sort-up' : 'fas fa-sort-down';
            icon.style.opacity = '1';
        } else {
            icon.className = 'fas fa-sort';
            icon.style.opacity = '0.5';
        }
    });
}

// Simple CSV line parser (handles quoted values)
function parseCsvLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current += char;
        }
    }
    
    result.push(current.trim());
    return result.map(cell => cell.replace(/^"|"$/g, '')); // Remove surrounding quotes
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Show error message
function showError(message) {
    const container = document.getElementById('content-container');
    container.innerHTML = `
        <div class="error">
            <i class="fas fa-exclamation-triangle"></i>
            <p>${escapeHtml(message)}</p>
        </div>
    `;
}

// Load and display file content
async function loadFileContent() {
    const params = getUrlParams();
    
    if (!params.file) {
        showError('No file specified');
        return;
    }
    
    try {
        console.log('Loading file:', params.file);
        
        const response = await fetch(`/api/export-file/${encodeURIComponent(params.file)}`);
        
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.error || 'Failed to load file');
        }
        
        const fileData = result.file;
        
        // Update page title
        document.title = `Export Viewer - ${fileData.name}`;
        
        // Render file information
        renderFileInfo(fileData, params);
        
        // Render content based on file type
        if (fileData.type === 'json') {
            if (fileData.parsedContent) {
                renderJsonContent(fileData.parsedContent);
            } else {
                renderJsonContent({ error: 'Could not parse JSON content', rawContent: fileData.content });
            }
        } else if (fileData.type === 'csv') {
            renderCsvContent(fileData.content);
        } else {
            showError(`Unsupported file type: ${fileData.type}`);
        }
        
    } catch (error) {
        console.error('Error loading file:', error);
        showError(`Failed to load file: ${error.message}`);
    }
}

// AI Chat System with Memory
let chatHistory = [];
let isAIThinking = false;
let currentLanguage = 'en'; // Default to English

// Localization strings
const translations = {
    en: {
        ai_title: 'AI Assistant',
        welcome_message: '👋 Hi! I\'m your AI assistant. I can help you analyze your Facebook posts data.',
        analyze_performance: 'Analyze my posts performance',
        content_tips: 'Give me content recommendations',
        best_posts: 'What are my best posts?',
        btn_analyze: '📊 Analyze Performance',
        btn_tips: '💡 Content Tips',
        btn_best: '🏆 Best Posts',
        input_placeholder: 'Ask me anything about your posts...',
        input_hint: '💡 Try asking: "How can I improve engagement?" or "What\'s my best performing content?"',
        status_ready: 'Ready',
        status_thinking: 'Thinking...',
        status_error: 'Error'
    },
    ar: {
        ai_title: 'المساعد الذكي',
        welcome_message: '👋 مرحباً! أنا مساعدك الذكي. يمكنني مساعدتك في تحليل بيانات منشورات فيسبوك.',
        analyze_performance: 'حلل أداء منشوراتي',
        content_tips: 'أعطني نصائح للمحتوى',
        best_posts: 'ما هي أفضل منشوراتي؟',
        btn_analyze: '📊 تحليل الأداء',
        btn_tips: '💡 نصائح المحتوى',
        btn_best: '🏆 أفضل المنشورات',
        input_placeholder: 'اسألني أي شيء عن منشوراتك...',
        input_hint: '💡 جرب أن تسأل: "كيف يمكنني تحسين التفاعل؟" أو "ما هو أفضل محتوى لدي؟"',
        status_ready: 'جاهز',
        status_thinking: 'يفكر...',
        status_error: 'خطأ'
    }
};

// Language switching functions
function setLanguage(lang) {
    currentLanguage = lang;

    // Update UI language
    updateUILanguage();

    // Update button states
    document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
    document.getElementById(`lang-${lang}`).classList.add('active');

    // Apply RTL for Arabic (but keep header in LTR)
    const chatSection = document.getElementById('ai-chat-section');
    if (lang === 'ar') {
        chatSection.classList.add('rtl');
    } else {
        chatSection.classList.remove('rtl');
    }

    // Ensure header and controls always stay in LTR position
    const header = document.querySelector('.ai-chat-header');
    const controls = document.querySelector('.ai-controls');
    const languageToggle = document.querySelector('.language-toggle');

    if (header) {
        header.style.direction = 'ltr';
    }
    if (controls) {
        controls.style.direction = 'ltr';
    }
    if (languageToggle) {
        languageToggle.style.direction = 'ltr';
    }

    // Save language preference
    localStorage.setItem('ai_chat_language', lang);

    // Ensure input remains functional after language change
    setTimeout(() => {
        const chatInput = document.getElementById('ai-chat-input');
        if (chatInput) {
            chatInput.focus();
            console.log('🎯 Input re-focused after language change');
        }
    }, 100);

    console.log('🌐 Language changed to:', lang);
}

function updateUILanguage() {
    const t = translations[currentLanguage];

    // Update all UI elements
    document.getElementById('ai-title').textContent = t.ai_title;
    document.getElementById('welcome-text').textContent = t.welcome_message;
    document.getElementById('btn-analyze').textContent = t.btn_analyze;
    document.getElementById('btn-tips').textContent = t.btn_tips;
    document.getElementById('btn-best').textContent = t.btn_best;
    document.getElementById('ai-chat-input').placeholder = t.input_placeholder;
    document.getElementById('input-hint').textContent = t.input_hint;

    // Update status if visible
    const statusElement = document.getElementById('ai-status');
    if (statusElement) {
        const currentStatus = statusElement.textContent;
        if (currentStatus === 'Ready' || currentStatus === 'جاهز') {
            statusElement.textContent = t.status_ready;
        } else if (currentStatus === 'Thinking...' || currentStatus === 'يفكر...') {
            statusElement.textContent = t.status_thinking;
        } else if (currentStatus === 'Error' || currentStatus === 'خطأ') {
            statusElement.textContent = t.status_error;
        }
    }
}

function getLocalizedMessage(key) {
    return translations[currentLanguage][key] || translations.en[key];
}

// Initialize AI Chat
function initializeAIChat() {
    console.log('🤖 Initializing AI Chat System');

    // Wait a bit more for DOM to be fully ready
    setTimeout(() => {
        // Load language preference
        const savedLanguage = localStorage.getItem('ai_chat_language') || 'en';
        setLanguage(savedLanguage);

        // Set up input event listeners
        const chatInput = document.getElementById('ai-chat-input');
        const sendBtn = document.getElementById('ai-send-btn');

        console.log('🔍 Chat input element:', chatInput);
        console.log('🔍 Send button element:', sendBtn);
        console.log('🔍 Input properties:', {
            disabled: chatInput?.disabled,
            readOnly: chatInput?.readOnly,
            style: chatInput?.style.cssText,
            tabIndex: chatInput?.tabIndex
        });

        if (chatInput) {
            // Force enable the input with multiple methods
            chatInput.disabled = false;
            chatInput.readOnly = false;
            chatInput.removeAttribute('disabled');
            chatInput.removeAttribute('readonly');
            chatInput.tabIndex = 0;

            // Force CSS properties
            chatInput.style.pointerEvents = 'auto';
            chatInput.style.userSelect = 'text';
            chatInput.style.webkitUserSelect = 'text';
            chatInput.style.mozUserSelect = 'text';
            chatInput.style.msUserSelect = 'text';
            chatInput.style.cursor = 'text';

            console.log('✅ Chat input force enabled');
            console.log('🔍 After enabling:', {
                disabled: chatInput.disabled,
                readOnly: chatInput.readOnly,
                tabIndex: chatInput.tabIndex
            });

            // Remove any existing event listeners and add new ones
            chatInput.removeEventListener('input', handleInputChange);
            chatInput.removeEventListener('keypress', handleKeyPress);
            chatInput.removeEventListener('click', handleInputClick);

            chatInput.addEventListener('input', handleInputChange);
            chatInput.addEventListener('keypress', handleKeyPress);
            chatInput.addEventListener('click', handleInputClick);
            chatInput.addEventListener('focus', function() {
                console.log('🎯 Input focused');
            });
            chatInput.addEventListener('blur', function() {
                console.log('😴 Input blurred');
            });

            // Test the input field
            setTimeout(() => {
                console.log('🧪 Testing input field...');
                chatInput.focus();
                chatInput.value = 'test';
                chatInput.value = '';
                console.log('✅ Input test completed');
            }, 200);

        } else {
            console.error('❌ Chat input element not found!');
        }

        if (sendBtn) {
            sendBtn.disabled = true; // Initially disabled until text is entered
            console.log('✅ Send button found and initially disabled');
        } else {
            console.error('❌ Send button element not found!');
        }

        // Load chat history from localStorage
        loadChatHistory();

        console.log('✅ AI Chat initialized successfully');
    }, 300);
}

// Separate event handler functions
function handleInputChange() {
    console.log('📝 Input event triggered, value:', this.value);
    const hasText = this.value.trim().length > 0;
    const sendBtn = document.getElementById('ai-send-btn');
    if (sendBtn) {
        sendBtn.disabled = !hasText || isAIThinking;
        console.log('🔘 Send button disabled:', sendBtn.disabled);
    }
}

function handleKeyPress(e) {
    console.log('⌨️ Key pressed:', e.key);
    if (e.key === 'Enter' && !e.shiftKey) {
        const sendBtn = document.getElementById('ai-send-btn');
        if (sendBtn && !sendBtn.disabled) {
            e.preventDefault();
            sendChatMessage();
        }
    }
}

function handleInputClick() {
    console.log('🖱️ Input clicked');
    this.focus();
}

// Test function for debugging input issues
function testChatInput() {
    console.log('🔄 REFRESHING ENTIRE AI SYSTEM...');

    try {
        // Use the ULTIMATE solution - refresh entire AI section
        refreshEntireAISection();

        // Wait for refresh to complete then test
        setTimeout(() => {
            const chatInput = document.getElementById('ai-chat-input');
            if (chatInput) {
                console.log('🔍 Testing refreshed AI system...');

                // Simulate the minimize/restore fix
                simulateMinimizeRestore();

                // Test typing
                setTimeout(() => {
                    chatInput.focus();
                    chatInput.value = 'Test message';

                    // Trigger input event manually
                    const inputEvent = new Event('input', { bubbles: true });
                    chatInput.dispatchEvent(inputEvent);

                    console.log('✅ AI system refresh completed successfully');
                    alert('🔄 AI SYSTEM REFRESHED!\n\nThe entire AI chat system has been reloaded.\nYou can now type and send messages.');

                    // Clear test value after showing success
                    setTimeout(() => {
                        chatInput.value = '';
                        const sendBtn = document.getElementById('ai-send-btn');
                        if (sendBtn) {
                            sendBtn.disabled = true;
                            sendBtn.style.background = 'var(--bg-tertiary)';
                            sendBtn.style.color = 'var(--text-secondary)';
                            sendBtn.style.cursor = 'not-allowed';
                        }
                        chatInput.focus();
                    }, 2000);
                }, 500);

            } else {
                throw new Error('Could not find input after AI refresh');
            }
        }, 300);

    } catch (error) {
        console.error('❌ AI refresh failed:', error);
        alert('❌ AI refresh failed: ' + error.message + '\n\nPlease refresh the page.');
    }
}

// Simulate the minimize/restore behavior that fixes the input
function simulateMinimizeRestore() {
    console.log('🔄 Simulating minimize/restore behavior...');

    const chatInput = document.getElementById('ai-chat-input');
    if (!chatInput) {
        console.error('❌ Input not found for simulation');
        return;
    }

    // Simulate losing focus (like minimizing)
    chatInput.blur();
    console.log('😴 Simulated blur (minimize)');

    // Wait a moment then simulate regaining focus (like restoring)
    setTimeout(() => {
        // Force re-enable the input
        chatInput.disabled = false;
        chatInput.readOnly = false;
        chatInput.removeAttribute('disabled');
        chatInput.removeAttribute('readonly');

        // Force focus and click
        chatInput.focus();
        chatInput.click();

        // Trigger focus events manually
        const focusEvent = new FocusEvent('focus', { bubbles: true });
        chatInput.dispatchEvent(focusEvent);

        const clickEvent = new MouseEvent('click', { bubbles: true });
        chatInput.dispatchEvent(clickEvent);

        console.log('👁️ Simulated focus (restore)');

        // Additional focus attempt
        setTimeout(() => {
            chatInput.focus();
            console.log('🎯 Final focus attempt after simulation');
        }, 100);

    }, 200);
}

// Universal function to ensure chat input is always working
function ensureChatInputWorks() {
    console.log('🔧 Ensuring chat input works...');

    const chatInput = document.getElementById('ai-chat-input');
    if (!chatInput) {
        console.error('❌ Chat input not found');
        return false;
    }

    // Force enable and setup
    chatInput.disabled = false;
    chatInput.readOnly = false;
    chatInput.removeAttribute('disabled');
    chatInput.removeAttribute('readonly');
    chatInput.tabIndex = 0;

    // Apply styles
    chatInput.style.pointerEvents = 'auto';
    chatInput.style.userSelect = 'text';
    chatInput.style.cursor = 'text';
    chatInput.style.opacity = '1';
    chatInput.style.visibility = 'visible';

    // Ensure event listeners are attached
    chatInput.removeEventListener('input', handleInputChange);
    chatInput.removeEventListener('keypress', handleKeyPress);
    chatInput.removeEventListener('click', handleInputClick);

    chatInput.addEventListener('input', handleInputChange);
    chatInput.addEventListener('keypress', handleKeyPress);
    chatInput.addEventListener('click', handleInputClick);

    console.log('✅ Chat input should be working now');
    return true;
}

// More aggressive input fixing function
function forceFixChatInput() {
    console.log('💪 Force fixing chat input...');

    const chatInput = document.getElementById('ai-chat-input');
    const sendBtn = document.getElementById('ai-send-btn');

    if (!chatInput) {
        console.error('❌ Chat input not found during force fix');
        return false;
    }

    console.log('🔍 Input state before force fix:', {
        disabled: chatInput.disabled,
        readOnly: chatInput.readOnly,
        value: chatInput.value,
        tabIndex: chatInput.tabIndex,
        style: chatInput.style.cssText
    });

    // Nuclear option: completely reset the input
    chatInput.disabled = false;
    chatInput.readOnly = false;
    chatInput.removeAttribute('disabled');
    chatInput.removeAttribute('readonly');
    chatInput.removeAttribute('aria-disabled');
    chatInput.tabIndex = 0;
    chatInput.value = '';

    // Force all CSS properties
    chatInput.style.cssText = `
        flex: 1;
        padding: 12px 16px;
        border: 1px solid var(--border-color);
        border-radius: 25px;
        background: var(--bg-tertiary) !important;
        color: var(--text-primary) !important;
        font-size: 0.95rem;
        outline: none;
        pointer-events: auto !important;
        user-select: text !important;
        cursor: text !important;
        z-index: 1000;
        position: relative;
        opacity: 1 !important;
        visibility: visible !important;
    `;

    // Remove ALL event listeners and add fresh ones
    const newInput = chatInput.cloneNode(true);
    chatInput.parentNode.replaceChild(newInput, chatInput);

    // Get the new input reference
    const freshInput = document.getElementById('ai-chat-input');

    if (freshInput) {
        // Add event listeners to the fresh input
        freshInput.addEventListener('input', function() {
            console.log('📝 Fresh input event triggered, value:', this.value);
            const hasText = this.value.trim().length > 0;
            const sendButton = document.getElementById('ai-send-btn');
            if (sendButton) {
                sendButton.disabled = !hasText || isAIThinking;
                console.log('🔘 Send button disabled:', sendButton.disabled);
            }
        });

        freshInput.addEventListener('keypress', function(e) {
            console.log('⌨️ Fresh key pressed:', e.key);
            if (e.key === 'Enter' && !e.shiftKey) {
                const sendButton = document.getElementById('ai-send-btn');
                if (sendButton && !sendButton.disabled) {
                    e.preventDefault();
                    sendChatMessage();
                }
            }
        });

        freshInput.addEventListener('click', function() {
            console.log('🖱️ Fresh input clicked');
            this.focus();
        });

        freshInput.addEventListener('focus', function() {
            console.log('🎯 Fresh input focused');
        });

        // Reset send button
        if (sendBtn) {
            sendBtn.disabled = true;
            sendBtn.style.cssText = `
                width: 44px;
                height: 44px;
                border-radius: 50%;
                border: none;
                background: var(--bg-tertiary);
                color: var(--text-secondary);
                cursor: not-allowed;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
        }

        console.log('✅ Chat input force fixed with fresh element');

        // Test the fresh input
        setTimeout(() => {
            freshInput.focus();
            console.log('🎯 Fresh input focused after force fix');
        }, 100);

        return true;
    }

    console.error('❌ Failed to create fresh input');
    return false;
}

// Send a chat message
async function sendChatMessage() {
    const chatInput = document.getElementById('ai-chat-input');
    const message = chatInput.value.trim();

    if (!message || isAIThinking) return;

    console.log('💬 Sending chat message:', message);

    // Clear input and disable send button
    chatInput.value = '';
    updateSendButton();

    // Add user message to chat
    addMessageToChat('user', message);

    // Show AI thinking state
    setAIStatus('thinking', getLocalizedMessage('status_thinking'));
    isAIThinking = true;

    try {
        // Send message to AI with context
        const response = await sendMessageToAI(message);

        // Add AI response to chat
        addMessageToChat('ai', response);

    } catch (error) {
        console.error('❌ Chat error:', error);
        const errorMessage = currentLanguage === 'ar'
            ? '❌ عذراً، واجهت خطأ. يرجى المحاولة مرة أخرى.'
            : '❌ Sorry, I encountered an error. Please try again.';
        addMessageToChat('ai', errorMessage);
        setAIStatus('error', getLocalizedMessage('status_error'));
        setTimeout(() => setAIStatus('ready', getLocalizedMessage('status_ready')), 3000);
    } finally {
        isAIThinking = false;
        setAIStatus('ready', getLocalizedMessage('status_ready'));
        updateSendButton();
    }
}

// Send quick action message
function sendQuickMessage(message) {
    const chatInput = document.getElementById('ai-chat-input');
    chatInput.value = message;
    updateSendButton();
    sendChatMessage();
}

// Send message to AI with context and memory
async function sendMessageToAI(userMessage) {
    console.log('🤖 Sending message to AI:', userMessage);

    // Check if we have posts data
    if (!window.exportData || !window.exportData.posts) {
        throw new Error('No posts data available for analysis');
    }

    // Prepare context with chat history, posts data, and assigned pages
    const teamMember = window.exportData.teamMember || {};
    const assignedPages = teamMember.assignedPages || [];
    const posts = window.exportData.posts;

    const context = {
        posts: posts,
        memberName: teamMember.name || 'Unknown',
        memberRole: teamMember.role || 'Member',
        assignedPages: assignedPages, // Include assigned pages information
        chatHistory: chatHistory.slice(-10), // Last 10 messages for context
        userMessage: userMessage,
        analysisType: 'chat', // New type for chat interactions
        language: currentLanguage // Include current language preference
    };

    console.log('📦 Sending context to AI:', {
        postsCount: context.posts.length,
        memberName: context.memberName,
        memberRole: context.memberRole,
        assignedPagesCount: context.assignedPages.length,
        assignedPageNames: context.assignedPages.map(p => p.name || p).join(', '),
        chatHistoryLength: context.chatHistory.length,
        userMessage: context.userMessage
    });

    const response = await fetch('/api/ai/analyze-posts', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(context)
    });

    const result = await response.json();

    if (!response.ok || !result.success) {
        throw new Error(result.error || 'AI request failed');
    }

    console.log('✅ AI response received:', result.analysis.substring(0, 100) + '...');
    return result.analysis;
}

// Add message to chat UI
function addMessageToChat(sender, message) {
    const messagesContainer = document.getElementById('ai-chat-messages');
    const messageDiv = document.createElement('div');

    const isUser = sender === 'user';
    messageDiv.className = `ai-message ${isUser ? 'user-message' : ''}`;

    const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    messageDiv.innerHTML = `
        <div class="${isUser ? 'user-avatar' : 'ai-avatar'}">
            <i class="fas fa-${isUser ? 'user' : 'robot'}"></i>
        </div>
        <div class="${isUser ? 'user-message-content' : 'ai-message-content'}">
            ${formatChatMessage(message)}
            <div style="font-size: 0.7rem; opacity: 0.7; margin-top: 5px;">${timestamp}</div>
        </div>
    `;

    messagesContainer.appendChild(messageDiv);

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;

    // Add to chat history
    chatHistory.push({
        sender: sender,
        message: message,
        timestamp: new Date().toISOString()
    });

    // Save to localStorage
    saveChatHistory();

    console.log(`💬 Added ${sender} message to chat`);
}

// Format chat message for display
function formatChatMessage(message) {
    // Simple formatting for chat messages
    return message
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>')
        .replace(/(\d+\.)/g, '<br>$1') // Add line breaks before numbered lists
        .replace(/^- /gm, '<br>• '); // Convert bullet points
}

// Update AI status indicator
function setAIStatus(status, text) {
    const statusIndicator = document.getElementById('ai-status');
    if (statusIndicator) {
        statusIndicator.textContent = text;
        statusIndicator.className = `ai-status-indicator ${status}`;
    }
}

// Update send button state
function updateSendButton() {
    const chatInput = document.getElementById('ai-chat-input');
    const sendBtn = document.getElementById('ai-send-btn');

    if (chatInput && sendBtn) {
        const hasText = chatInput.value.trim().length > 0;
        sendBtn.disabled = !hasText || isAIThinking;
    }
}

// Chat history management
function saveChatHistory() {
    try {
        const memberName = window.exportData?.teamMember?.name || 'Unknown';
        const storageKey = `ai_chat_history_${memberName}`;
        localStorage.setItem(storageKey, JSON.stringify(chatHistory));
        console.log('💾 Chat history saved for', memberName);
    } catch (error) {
        console.error('❌ Failed to save chat history:', error);
    }
}

function loadChatHistory() {
    try {
        const memberName = window.exportData?.teamMember?.name || 'Unknown';
        const storageKey = `ai_chat_history_${memberName}`;
        const saved = localStorage.getItem(storageKey);

        if (saved) {
            chatHistory = JSON.parse(saved);
            console.log('📂 Loaded chat history for', memberName, ':', chatHistory.length, 'messages');

            // Restore chat messages to UI
            const messagesContainer = document.getElementById('ai-chat-messages');
            if (messagesContainer && chatHistory.length > 0) {
                // Clear welcome message if there's history
                const welcomeMsg = messagesContainer.querySelector('.ai-welcome');
                if (welcomeMsg) welcomeMsg.style.display = 'none';

                // Add historical messages
                chatHistory.forEach(msg => {
                    addHistoricalMessageToChat(msg.sender, msg.message, msg.timestamp);
                });
            }
        } else {
            console.log('📂 No chat history found for', memberName);
        }
    } catch (error) {
        console.error('❌ Failed to load chat history:', error);
        chatHistory = [];
    }
}

function addHistoricalMessageToChat(sender, message, timestamp) {
    const messagesContainer = document.getElementById('ai-chat-messages');
    const messageDiv = document.createElement('div');

    const isUser = sender === 'user';
    messageDiv.className = `ai-message ${isUser ? 'user-message' : ''}`;

    const timeStr = new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    messageDiv.innerHTML = `
        <div class="${isUser ? 'user-avatar' : 'ai-avatar'}">
            <i class="fas fa-${isUser ? 'user' : 'robot'}"></i>
        </div>
        <div class="${isUser ? 'user-message-content' : 'ai-message-content'}">
            ${formatChatMessage(message)}
            <div style="font-size: 0.7rem; opacity: 0.7; margin-top: 5px;">${timeStr}</div>
        </div>
    `;

    messagesContainer.appendChild(messageDiv);
}

function clearChatHistory() {
    const confirmMessage = currentLanguage === 'ar'
        ? 'هل أنت متأكد من أنك تريد مسح تاريخ المحادثة؟ لا يمكن التراجع عن هذا الإجراء.'
        : 'Are you sure you want to clear the chat history? This cannot be undone.';

    if (confirm(confirmMessage)) {
        console.log('🗑️ Clearing chat with simple approach...');

        // Clear chat history
        chatHistory = [];

        // Clear from localStorage
        const memberName = window.exportData?.teamMember?.name || 'Unknown';
        const storageKey = `ai_chat_history_${memberName}`;
        localStorage.removeItem(storageKey);

        // Simple approach: Just clear messages and recreate input
        clearMessagesAndRecreateInput();

        console.log('✅ Chat cleared and input recreated');
    }
}

// Simple function to clear messages and recreate input from scratch
function clearMessagesAndRecreateInput() {
    console.log('🔄 Clearing messages and recreating input...');

    // Clear messages and show welcome
    const messagesContainer = document.getElementById('ai-chat-messages');
    if (messagesContainer) {
        const t = translations[currentLanguage];
        messagesContainer.innerHTML = `
            <div class="ai-message ai-welcome" id="ai-welcome-message">
                <div class="ai-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ai-message-content">
                    <p id="welcome-text">${t.welcome_message}</p>
                    <div class="ai-quick-actions" id="quick-actions">
                        <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('analyze_performance'))">
                            <span id="btn-analyze">${t.btn_analyze}</span>
                        </button>
                        <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('content_tips'))">
                            <span id="btn-tips">${t.btn_tips}</span>
                        </button>
                        <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('best_posts'))">
                            <span id="btn-best">${t.btn_best}</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Completely recreate the input container
    const inputContainer = document.querySelector('.ai-chat-input-container');
    if (inputContainer) {
        const t = translations[currentLanguage];

        // Create completely new input HTML
        inputContainer.innerHTML = `
            <div class="ai-chat-input-wrapper">
                <input
                    type="text"
                    id="ai-chat-input"
                    placeholder="${t.input_placeholder}"
                    maxlength="500"
                    autocomplete="off"
                    spellcheck="false"
                    tabindex="0"
                    style="pointer-events: auto !important; user-select: text !important;"
                >
                <button id="ai-send-btn" onclick="sendChatMessage()" disabled>
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="ai-input-info">
                <small id="input-hint">${t.input_hint}</small>
            </div>
        `;

        // Wait a moment then add event listeners to the new input
        setTimeout(() => {
            addInputEventListeners();
        }, 100);
    }
}

// Add event listeners to the recreated input
function addInputEventListeners() {
    console.log('🔗 Adding event listeners to new input...');

    const chatInput = document.getElementById('ai-chat-input');
    const sendBtn = document.getElementById('ai-send-btn');

    if (!chatInput || !sendBtn) {
        console.error('❌ New input elements not found');
        return;
    }

    // Reset variables
    isAIThinking = false;

    // Add input event listener
    chatInput.addEventListener('input', function() {
        console.log('📝 New input event:', this.value);
        const hasText = this.value.trim().length > 0;
        sendBtn.disabled = !hasText || isAIThinking;
    });

    // Add keypress event listener
    chatInput.addEventListener('keypress', function(e) {
        console.log('⌨️ New key event:', e.key);
        if (e.key === 'Enter' && !e.shiftKey && !sendBtn.disabled) {
            e.preventDefault();
            sendChatMessage();
        }
    });

    // Add send button click listener
    sendBtn.addEventListener('click', function() {
        if (!this.disabled) {
            sendChatMessage();
        }
    });

    // Focus the new input
    setTimeout(() => {
        chatInput.focus();
        console.log('🎯 New input focused and ready');
    }, 50);

    console.log('✅ Event listeners added to new input');
}

// Reset everything to initial state like just opening Export Viewer
function resetToInitialState() {
    console.log('🔄 Resetting to initial state...');

    // Reset all AI variables
    chatHistory = [];
    isAIThinking = false;

    // Hide AI section (like initial state)
    const aiSection = document.getElementById('ai-chat-section');
    if (aiSection) {
        aiSection.style.display = 'none';
        aiSection.classList.remove('rtl');
    }

    // Reset language to default
    currentLanguage = 'en';
    localStorage.setItem('ai_chat_language', 'en');

    // Clear any status messages
    const statusElement = document.getElementById('ai-status');
    if (statusElement) {
        statusElement.textContent = 'Ready';
    }

    // Reset the welcome message to default English
    const messagesContainer = document.getElementById('ai-chat-messages');
    if (messagesContainer) {
        const t = translations['en'];
        messagesContainer.innerHTML = `
            <div class="ai-message ai-welcome" id="ai-welcome-message">
                <div class="ai-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ai-message-content">
                    <p id="welcome-text">${t.welcome_message}</p>
                    <div class="ai-quick-actions" id="quick-actions">
                        <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('analyze_performance'))">
                            <span id="btn-analyze">${t.btn_analyze}</span>
                        </button>
                        <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('content_tips'))">
                            <span id="btn-tips">${t.btn_tips}</span>
                        </button>
                        <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('best_posts'))">
                            <span id="btn-best">${t.btn_best}</span>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Reset input field to initial state
    const chatInput = document.getElementById('ai-chat-input');
    if (chatInput) {
        chatInput.value = '';
        chatInput.disabled = false;
        chatInput.readOnly = false;
        chatInput.placeholder = translations['en'].input_placeholder;
    }

    // Reset send button to initial state
    const sendBtn = document.getElementById('ai-send-btn');
    if (sendBtn) {
        sendBtn.disabled = true;
        sendBtn.style.background = 'var(--bg-tertiary)';
        sendBtn.style.color = 'var(--text-secondary)';
        sendBtn.style.cursor = 'not-allowed';
    }

    // Reset language buttons to English active
    document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
    const enBtn = document.getElementById('lang-en');
    if (enBtn) {
        enBtn.classList.add('active');
    }

    // Update all UI text to English
    updateUILanguage();

    // Wait a moment then re-initialize the AI system like initial load
    setTimeout(() => {
        // Show AI section again (like when data is loaded)
        if (aiSection && window.exportData?.posts) {
            aiSection.style.display = 'block';

            // Re-initialize like initial load
            setTimeout(() => {
                initializeAIChat();
            }, 100);
        }
    }, 100);

    console.log('✅ Reset to initial state completed');
}

// ULTIMATE SOLUTION: Completely refresh the entire AI chat section
function refreshEntireAISection() {
    console.log('🔄 Refreshing entire AI chat section...');

    const aiSection = document.getElementById('ai-chat-section');
    if (!aiSection) {
        console.error('❌ AI section not found');
        return;
    }

    // Get current language
    const t = translations[currentLanguage];
    const isRTL = currentLanguage === 'ar';

    // Completely rebuild the entire AI section HTML
    aiSection.innerHTML = `
        <div class="ai-chat-header" style="direction: ltr;">
            <h2><i class="fas fa-robot"></i> <span id="ai-title">${t.ai_title}</span></h2>
            <div class="ai-controls" style="direction: ltr;">
                <div class="language-toggle" style="direction: ltr;">
                    <button class="lang-btn ${currentLanguage === 'en' ? 'active' : ''}" id="lang-en" onclick="setLanguage('en')" title="English">
                        🇺🇸 EN
                    </button>
                    <button class="lang-btn ${currentLanguage === 'ar' ? 'active' : ''}" id="lang-ar" onclick="setLanguage('ar')" title="العربية">
                        عربي
                    </button>
                </div>
                <div class="ai-status">
                    <span class="ai-status-indicator" id="ai-status">${t.status_ready}</span>
                    <button class="btn-clear-chat" id="clear-chat-btn" onclick="clearChatHistory()" title="Clear Chat">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <div class="ai-chat-container">
            <div class="ai-chat-messages" id="ai-chat-messages">
                <div class="ai-message ai-welcome" id="ai-welcome-message">
                    <div class="ai-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="ai-message-content">
                        <p id="welcome-text">${t.welcome_message}</p>
                        <div class="ai-quick-actions" id="quick-actions">
                            <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('analyze_performance'))">
                                <span id="btn-analyze">${t.btn_analyze}</span>
                            </button>
                            <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('content_tips'))">
                                <span id="btn-tips">${t.btn_tips}</span>
                            </button>
                            <button class="quick-action-btn" onclick="sendQuickMessage(getLocalizedMessage('best_posts'))">
                                <span id="btn-best">${t.btn_best}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="ai-chat-input-container">
                <div class="ai-chat-input-wrapper">
                    <input type="text" id="ai-chat-input" placeholder="${t.input_placeholder}" maxlength="500">
                    <button id="ai-send-btn" onclick="sendChatMessage()" disabled>
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
                <div class="ai-input-info">
                    <small id="input-hint">${t.input_hint}</small>
                    <button onclick="refreshEntireAISection()" style="margin-left: 10px; padding: 4px 8px; font-size: 0.7rem; background: var(--accent-color); color: white; border: none; border-radius: 4px; cursor: pointer;" title="Refresh AI System">
                        🔄 Refresh AI
                    </button>
                    <button onclick="simulateMinimizeRestore()" style="margin-left: 5px; padding: 4px 8px; font-size: 0.7rem; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;" title="Fix Focus (Simulate Minimize/Restore)">
                        🎯 Fix Focus
                    </button>
                </div>
            </div>
        </div>
    `;

    // Apply RTL class if Arabic
    if (isRTL) {
        aiSection.classList.add('rtl');
    } else {
        aiSection.classList.remove('rtl');
    }

    // Reset all variables
    chatHistory = [];
    isAIThinking = false;

    // Wait a moment then initialize the fresh AI system
    setTimeout(() => {
        initializeFreshAISystem();

        // After initialization, simulate the minimize/restore behavior
        setTimeout(() => {
            simulateMinimizeRestore();
        }, 200);
    }, 100);

    console.log('🔄 Entire AI section refreshed successfully');
}

// Initialize the completely fresh AI system
function initializeFreshAISystem() {
    console.log('🆕 Initializing fresh AI system...');

    const chatInput = document.getElementById('ai-chat-input');
    const sendBtn = document.getElementById('ai-send-btn');

    if (!chatInput || !sendBtn) {
        console.error('❌ Fresh AI elements not found');
        return;
    }

    console.log('✅ Fresh AI elements found');

    // Add fresh event listeners
    chatInput.addEventListener('input', function() {
        console.log('📝 FRESH AI input:', this.value);
        const hasText = this.value.trim().length > 0;
        sendBtn.disabled = !hasText || isAIThinking;

        // Update button appearance
        if (sendBtn.disabled) {
            sendBtn.style.background = 'var(--bg-tertiary)';
            sendBtn.style.color = 'var(--text-secondary)';
            sendBtn.style.cursor = 'not-allowed';
        } else {
            sendBtn.style.background = 'var(--accent-color)';
            sendBtn.style.color = 'white';
            sendBtn.style.cursor = 'pointer';
        }
    });

    chatInput.addEventListener('keypress', function(e) {
        console.log('⌨️ FRESH AI key:', e.key);
        if (e.key === 'Enter' && !e.shiftKey && !sendBtn.disabled) {
            e.preventDefault();
            sendChatMessage();
        }
    });

    chatInput.addEventListener('click', function() {
        console.log('🖱️ FRESH AI click');
        this.focus();
    });

    // Multiple focus attempts with different timings
    setTimeout(() => {
        chatInput.focus();
        console.log('🎯 First focus attempt');
    }, 50);

    setTimeout(() => {
        chatInput.focus();
        chatInput.click();
        console.log('🎯 Second focus attempt with click');
    }, 200);

    setTimeout(() => {
        chatInput.focus();
        chatInput.select();
        console.log('🎯 Third focus attempt with select');
    }, 500);

    // Add visibility change listener to handle minimize/restore
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            console.log('👁️ Page became visible, re-focusing input');
            setTimeout(() => {
                const currentInput = document.getElementById('ai-chat-input');
                if (currentInput) {
                    currentInput.focus();
                    console.log('🎯 Input re-focused after visibility change');
                }
            }, 100);
        }
    });

    // Add window focus listener
    window.addEventListener('focus', function() {
        console.log('🪟 Window focused, re-focusing input');
        setTimeout(() => {
            const currentInput = document.getElementById('ai-chat-input');
            if (currentInput) {
                currentInput.focus();
                console.log('🎯 Input re-focused after window focus');
            }
        }, 100);
    });

    console.log('🆕 Fresh AI system initialized successfully!');
}

// NUCLEAR OPTION: Completely rebuild the input container
function rebuildChatInputContainer() {
    console.log('💥 NUCLEAR: Rebuilding entire chat input container...');

    const inputContainer = document.querySelector('.ai-chat-input-container');
    if (!inputContainer) {
        console.error('❌ Input container not found');
        return;
    }

    // Get current language for placeholder
    const t = translations[currentLanguage];

    // Completely rebuild the input container HTML
    inputContainer.innerHTML = `
        <div class="ai-chat-input-wrapper">
            <input type="text" id="ai-chat-input" placeholder="${t.input_placeholder}" maxlength="500" style="
                flex: 1;
                padding: 12px 16px;
                border: 1px solid var(--border-color);
                border-radius: 25px;
                background: var(--bg-tertiary) !important;
                color: var(--text-primary) !important;
                font-size: 0.95rem;
                outline: none;
                pointer-events: auto !important;
                user-select: text !important;
                cursor: text !important;
                z-index: 1000;
                position: relative;
                opacity: 1 !important;
                visibility: visible !important;
            ">
            <button id="ai-send-btn" onclick="sendChatMessage()" disabled style="
                width: 44px;
                height: 44px;
                border-radius: 50%;
                border: none;
                background: var(--bg-tertiary);
                color: var(--text-secondary);
                cursor: not-allowed;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
            ">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
        <div class="ai-input-info">
            <small id="input-hint">${t.input_hint}</small>
            <button onclick="testChatInput()" style="margin-left: 10px; padding: 4px 8px; font-size: 0.7rem; background: var(--accent-color); color: white; border: none; border-radius: 4px; cursor: pointer;" title="Click if input is not working">
                Fix Input
            </button>
        </div>
    `;

    // Wait a moment then attach event listeners to the new elements
    setTimeout(() => {
        attachFreshEventListeners();
    }, 100);

    console.log('💥 Input container completely rebuilt');
}

// Attach fresh event listeners to the rebuilt input
function attachFreshEventListeners() {
    console.log('🔗 Attaching fresh event listeners...');

    const chatInput = document.getElementById('ai-chat-input');
    const sendBtn = document.getElementById('ai-send-btn');

    if (!chatInput) {
        console.error('❌ New chat input not found');
        return;
    }

    // Reset AI state
    isAIThinking = false;

    // Add input event listener
    chatInput.addEventListener('input', function() {
        console.log('📝 FRESH input event:', this.value);
        const hasText = this.value.trim().length > 0;
        if (sendBtn) {
            sendBtn.disabled = !hasText || isAIThinking;

            // Update button style based on state
            if (sendBtn.disabled) {
                sendBtn.style.background = 'var(--bg-tertiary)';
                sendBtn.style.color = 'var(--text-secondary)';
                sendBtn.style.cursor = 'not-allowed';
            } else {
                sendBtn.style.background = 'var(--accent-color)';
                sendBtn.style.color = 'white';
                sendBtn.style.cursor = 'pointer';
            }
        }
    });

    // Add keypress event listener
    chatInput.addEventListener('keypress', function(e) {
        console.log('⌨️ FRESH key pressed:', e.key);
        if (e.key === 'Enter' && !e.shiftKey && sendBtn && !sendBtn.disabled) {
            e.preventDefault();
            sendChatMessage();
        }
    });

    // Add click event listener
    chatInput.addEventListener('click', function() {
        console.log('🖱️ FRESH input clicked');
        this.focus();
    });

    // Add focus event listener
    chatInput.addEventListener('focus', function() {
        console.log('🎯 FRESH input focused');
    });

    // Focus the new input
    setTimeout(() => {
        chatInput.focus();
        console.log('✅ Fresh input focused and ready');
    }, 50);

    console.log('🔗 Fresh event listeners attached successfully');
}

// Function to re-initialize just the input field
function reinitializeChatInput() {
    console.log('🔄 Re-initializing chat input...');

    const chatInput = document.getElementById('ai-chat-input');
    const sendBtn = document.getElementById('ai-send-btn');

    if (chatInput) {
        // Force enable the input again
        chatInput.disabled = false;
        chatInput.readOnly = false;
        chatInput.removeAttribute('disabled');
        chatInput.removeAttribute('readonly');
        chatInput.tabIndex = 0;

        // Force CSS properties again
        chatInput.style.pointerEvents = 'auto';
        chatInput.style.userSelect = 'text';
        chatInput.style.webkitUserSelect = 'text';
        chatInput.style.mozUserSelect = 'text';
        chatInput.style.msUserSelect = 'text';
        chatInput.style.cursor = 'text';

        // Clear any existing value
        chatInput.value = '';

        // Remove existing event listeners and add new ones
        chatInput.removeEventListener('input', handleInputChange);
        chatInput.removeEventListener('keypress', handleKeyPress);
        chatInput.removeEventListener('click', handleInputClick);

        chatInput.addEventListener('input', handleInputChange);
        chatInput.addEventListener('keypress', handleKeyPress);
        chatInput.addEventListener('click', handleInputClick);

        console.log('✅ Chat input re-initialized successfully');

        // Test the input
        setTimeout(() => {
            chatInput.focus();
            console.log('🎯 Input re-focused after clear');
        }, 50);
    } else {
        console.error('❌ Chat input not found during re-initialization');
    }

    if (sendBtn) {
        sendBtn.disabled = true; // Reset to disabled state
        console.log('✅ Send button reset to disabled');
    }
}

// Initialize the viewer
// Calculate advanced analytics for AI
function calculateAdvancedAnalytics(posts) {
    if (!posts || posts.length === 0) return {};

    // Basic metrics
    const totalPosts = posts.length;
    const totalLikes = posts.reduce((sum, post) => sum + (parseInt(post.likes) || 0), 0);
    const totalComments = posts.reduce((sum, post) => sum + (parseInt(post.comments) || 0), 0);
    const totalShares = posts.reduce((sum, post) => sum + (parseInt(post.shares) || 0), 0);
    const totalEngagement = totalLikes + totalComments + totalShares;

    // Advanced metrics
    const avgEngagementPerPost = totalPosts > 0 ? Math.round(totalEngagement / totalPosts) : 0;
    const avgLikesPerPost = totalPosts > 0 ? Math.round(totalLikes / totalPosts) : 0;
    const avgCommentsPerPost = totalPosts > 0 ? Math.round(totalComments / totalPosts) : 0;
    const avgSharesPerPost = totalPosts > 0 ? Math.round(totalShares / totalPosts) : 0;

    // Engagement ratios
    const likesToCommentsRatio = totalComments > 0 ? (totalLikes / totalComments).toFixed(2) : 'N/A';
    const likesToSharesRatio = totalShares > 0 ? (totalLikes / totalShares).toFixed(2) : 'N/A';
    const commentsToSharesRatio = totalShares > 0 ? (totalComments / totalShares).toFixed(2) : 'N/A';

    // Content type analysis
    const contentTypes = {
        video: posts.filter(post => post.type === 'video' || (post.content && post.content.toLowerCase().includes('video'))).length,
        photo: posts.filter(post => post.type === 'photo' || (post.content && post.content.toLowerCase().includes('photo'))).length,
        text: posts.filter(post => !post.type || post.type === 'text').length
    };

    // Performance distribution
    const engagementValues = posts.map(post => (parseInt(post.likes) || 0) + (parseInt(post.comments) || 0) + (parseInt(post.shares) || 0));
    engagementValues.sort((a, b) => b - a);

    const topPerformers = engagementValues.slice(0, Math.ceil(totalPosts * 0.2)); // Top 20%
    const bottomPerformers = engagementValues.slice(-Math.ceil(totalPosts * 0.2)); // Bottom 20%

    const avgTopPerformers = topPerformers.length > 0 ? Math.round(topPerformers.reduce((sum, val) => sum + val, 0) / topPerformers.length) : 0;
    const avgBottomPerformers = bottomPerformers.length > 0 ? Math.round(bottomPerformers.reduce((sum, val) => sum + val, 0) / bottomPerformers.length) : 0;

    // Time-based analysis (if timestamps available)
    const postsWithTime = posts.filter(post => post.timestamp || post.time);
    let timeAnalysis = {};

    if (postsWithTime.length > 0) {
        const recentPosts = postsWithTime.slice(0, Math.ceil(postsWithTime.length * 0.3)); // Most recent 30%
        const olderPosts = postsWithTime.slice(-Math.ceil(postsWithTime.length * 0.3)); // Oldest 30%

        const recentEngagement = recentPosts.reduce((sum, post) => sum + ((parseInt(post.likes) || 0) + (parseInt(post.comments) || 0) + (parseInt(post.shares) || 0)), 0);
        const olderEngagement = olderPosts.reduce((sum, post) => sum + ((parseInt(post.likes) || 0) + (parseInt(post.comments) || 0) + (parseInt(post.shares) || 0)), 0);

        const recentAvg = recentPosts.length > 0 ? Math.round(recentEngagement / recentPosts.length) : 0;
        const olderAvg = olderPosts.length > 0 ? Math.round(olderEngagement / olderPosts.length) : 0;

        timeAnalysis = {
            recentAvgEngagement: recentAvg,
            olderAvgEngagement: olderAvg,
            trendDirection: recentAvg > olderAvg ? 'improving' : recentAvg < olderAvg ? 'declining' : 'stable',
            improvementPercentage: olderAvg > 0 ? Math.round(((recentAvg - olderAvg) / olderAvg) * 100) : 0
        };
    }

    // Engagement quality analysis
    const highEngagementPosts = posts.filter(post => {
        const engagement = (parseInt(post.likes) || 0) + (parseInt(post.comments) || 0) + (parseInt(post.shares) || 0);
        return engagement > avgEngagementPerPost * 1.5; // 50% above average
    });

    const lowEngagementPosts = posts.filter(post => {
        const engagement = (parseInt(post.likes) || 0) + (parseInt(post.comments) || 0) + (parseInt(post.shares) || 0);
        return engagement < avgEngagementPerPost * 0.5; // 50% below average
    });

    return {
        basic: {
            totalPosts,
            totalLikes,
            totalComments,
            totalShares,
            totalEngagement
        },
        averages: {
            avgEngagementPerPost,
            avgLikesPerPost,
            avgCommentsPerPost,
            avgSharesPerPost
        },
        ratios: {
            likesToCommentsRatio,
            likesToSharesRatio,
            commentsToSharesRatio
        },
        contentTypes,
        performance: {
            avgTopPerformers,
            avgBottomPerformers,
            highEngagementPostsCount: highEngagementPosts.length,
            lowEngagementPostsCount: lowEngagementPosts.length,
            consistencyScore: avgBottomPerformers > 0 ? Math.round((avgTopPerformers / avgBottomPerformers) * 10) / 10 : 'N/A'
        },
        timeAnalysis
    };
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Export Viewer loaded');

    // Initialize theme
    initializeTheme();

    // Load file content
    loadFileContent();
});
