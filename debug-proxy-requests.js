// Debug script to check proxy request counts
const fs = require('fs');
const path = require('path');

console.log('🔍 Debugging proxy request counts...\n');

try {
  // Load proxy statistics
  const statsFile = path.join(__dirname, 'proxy_statistics.json');
  if (fs.existsSync(statsFile)) {
    const statsData = JSON.parse(fs.readFileSync(statsFile, 'utf8'));
    console.log(`📊 Loaded ${Object.keys(statsData).length} proxy statistics\n`);
    
    // Group by request count
    const requestGroups = {};
    Object.entries(statsData).forEach(([proxy, stats]) => {
      const requests = stats.requests || 0;
      if (!requestGroups[requests]) {
        requestGroups[requests] = [];
      }
      requestGroups[requests].push({
        proxy,
        sessionId: proxy.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown',
        requests: stats.requests,
        successes: stats.successes,
        failures: stats.failures
      });
    });
    
    // Display groups
    const sortedCounts = Object.keys(requestGroups).map(Number).sort((a, b) => a - b);
    
    console.log('📈 Request Count Distribution:');
    sortedCounts.forEach(count => {
      const proxies = requestGroups[count];
      console.log(`\n${count} requests: ${proxies.length} proxies`);
      
      // Show first 5 proxies in each group
      proxies.slice(0, 5).forEach(p => {
        console.log(`  - Session ${p.sessionId}: ${p.requests} requests, ${p.successes} successes, ${p.failures} failures`);
      });
      
      if (proxies.length > 5) {
        console.log(`  ... and ${proxies.length - 5} more`);
      }
    });
    
    // Check for duplicates
    console.log('\n🔍 Checking for duplicate proxies...');
    const allProxies = Object.keys(statsData);
    const duplicates = allProxies.filter((proxy, index) => allProxies.indexOf(proxy) !== index);
    
    if (duplicates.length > 0) {
      console.log(`❌ Found ${duplicates.length} duplicate proxies:`);
      duplicates.forEach(dup => console.log(`  - ${dup}`));
    } else {
      console.log('✅ No duplicate proxies found');
    }
    
    // Check minimum request count
    const minRequests = Math.min(...sortedCounts);
    const proxiesWithMinRequests = requestGroups[minRequests].length;
    
    console.log(`\n🎯 Least-used proxies:`);
    console.log(`  - Minimum requests: ${minRequests}`);
    console.log(`  - Proxies with ${minRequests} requests: ${proxiesWithMinRequests}`);
    
  } else {
    console.log('❌ No proxy statistics file found');
  }
  
  // Load active proxies
  const proxiesFile = 'proxies.json';
  if (fs.existsSync(proxiesFile)) {
    const proxiesData = JSON.parse(fs.readFileSync(proxiesFile, 'utf8'));
    console.log(`\n📁 Active proxies: ${proxiesData.proxies.length}`);
    console.log(`📁 Failed proxies: ${proxiesData.permanentlyFailedProxies?.length || 0}`);
    
    // Check if all active proxies have stats
    const activeProxies = proxiesData.proxies;
    const statsFile = path.join(__dirname, 'proxy_statistics.json');
    if (fs.existsSync(statsFile)) {
      const statsData = JSON.parse(fs.readFileSync(statsFile, 'utf8'));
      const proxiesWithoutStats = activeProxies.filter(proxy => !statsData[proxy]);
      
      if (proxiesWithoutStats.length > 0) {
        console.log(`\n⚠️ ${proxiesWithoutStats.length} active proxies missing stats:`);
        proxiesWithoutStats.slice(0, 3).forEach(proxy => {
          const sessionId = proxy.split(':')[3]?.split('_session-')[1]?.substring(0, 8) || 'unknown';
          console.log(`  - Session ${sessionId}`);
        });
      } else {
        console.log('\n✅ All active proxies have statistics');
      }
    }
  }
  
} catch (error) {
  console.error('❌ Error:', error.message);
}
