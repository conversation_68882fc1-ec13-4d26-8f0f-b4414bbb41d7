<?php
// Quick Database Setup API for XAMPP
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$response = ['success' => false, 'message' => '', 'steps' => []];

try {
    // First connect without database to create it
    $conn = new mysqli("localhost", "root", "");
    
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    $response['steps'][] = "✅ Connected to MySQL server";
    
    // Create database if it doesn't exist
    $sql = "CREATE DATABASE IF NOT EXISTS facebook_db";
    if ($conn->query($sql) === TRUE) {
        $response['steps'][] = "✅ Database 'facebook_db' created/verified";
    } else {
        throw new Exception("Error creating database: " . $conn->error);
    }
    
    // Select the database
    $conn->select_db("facebook_db");
    $response['steps'][] = "✅ Selected facebook_db database";
    
    // Create members table
    $membersTable = "
    CREATE TABLE IF NOT EXISTS members (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        role VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($membersTable) === TRUE) {
        $response['steps'][] = "✅ Members table created/verified";
    } else {
        throw new Exception("Error creating members table: " . $conn->error);
    }
    
    // Create pages table (without foreign key first)
    $pagesTable = "
    CREATE TABLE IF NOT EXISTS pages (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        url TEXT,
        member_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($pagesTable) === TRUE) {
        $response['steps'][] = "✅ Pages table created/verified";
    } else {
        throw new Exception("Error creating pages table: " . $conn->error);
    }
    
    // Create posts table (without foreign keys first)
    $postsTable = "
    CREATE TABLE IF NOT EXISTS posts (
        id VARCHAR(255) PRIMARY KEY,
        page_id VARCHAR(255),
        member_id VARCHAR(255),
        caption TEXT,
        likes_count INT DEFAULT 0,
        comments_count INT DEFAULT 0,
        views_count INT DEFAULT 0,
        shares_count INT DEFAULT 0,
        post_url TEXT,
        page_url TEXT,
        page_name VARCHAR(255),
        post_date DATETIME,
        last_metrics_update DATETIME,
        extraction_timestamp DATETIME,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($postsTable) === TRUE) {
        $response['steps'][] = "✅ Posts table created/verified";
    } else {
        throw new Exception("Error creating posts table: " . $conn->error);
    }
    
    // Check table counts
    $tables = ['members', 'pages', 'posts'];
    $counts = [];
    
    foreach ($tables as $table) {
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $count = $result->fetch_assoc()['count'];
            $counts[$table] = $count;
            $response['steps'][] = "📊 $table table: $count records";
        }
    }
    
    $response['success'] = true;
    $response['message'] = 'Database setup completed successfully!';
    $response['counts'] = $counts;
    $response['database_url'] = 'http://localhost/phpmyadmin';
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = $e->getMessage();
    $response['steps'][] = "❌ Error: " . $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
