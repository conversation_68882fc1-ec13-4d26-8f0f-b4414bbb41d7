-- Migration script to add media_id field to posts table
-- Run this script to update existing databases

USE facebook_db;

-- Add media_id column to posts table if it doesn't exist
ALTER TABLE posts 
ADD COLUMN IF NOT EXISTS media_id VARCHAR(255) AFTER caption;

-- Add index for media_id for better performance
CREATE INDEX IF NOT EXISTS idx_media_id ON posts(media_id);

-- Update the posts_with_details view to include media_id
DROP VIEW IF EXISTS posts_with_details;

CREATE VIEW posts_with_details AS
SELECT 
    p.id as post_id,
    p.page_id,
    p.member_id,
    p.caption,
    p.media_id,
    p.likes_count,
    p.comments_count,
    p.views_count,
    p.shares_count,
    p.post_url,
    p.page_url,
    p.page_name,
    p.post_date,
    p.last_metrics_update,
    p.extraction_timestamp,
    pg.name as page_name_from_pages,
    pg.url as page_url_from_pages,
    m.name as member_name,
    m.email as member_email,
    m.role as member_role
FROM posts p
LEFT JOIN pages pg ON p.page_id = pg.id
LEFT JOIN members m ON p.member_id = m.id;

-- Verify the changes
DESCRIBE posts;

SELECT 'Migration completed successfully - media_id column added to posts table' as status;
