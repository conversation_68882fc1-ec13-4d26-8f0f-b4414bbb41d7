<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: http://localhost:3000');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit(0);
}

$conn = new mysqli("localhost", "root", "", "facebook_db");

$response = ['success' => false, 'message' => '', 'debug' => []];

try {
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }
    
    $response['debug'][] = "Connected to MySQL successfully";
    
    // Check if database exists
    $result = $conn->query("SHOW DATABASES LIKE 'facebook_db'");
    if ($result->num_rows > 0) {
        $response['debug'][] = "Database 'facebook_db' exists";
        
        // Select the database
        $conn->select_db("facebook_db");
        
        // Check tables
        $tables = ['members', 'pages', 'posts'];
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result->num_rows > 0) {
                $response['debug'][] = "Table '$table' exists";
                
                // Count records
                $countResult = $conn->query("SELECT COUNT(*) as count FROM $table");
                $count = $countResult->fetch_assoc()['count'];
                $response['debug'][] = "Table '$table' has $count records";
                
                // Show structure
                $structResult = $conn->query("DESCRIBE $table");
                $columns = [];
                while ($row = $structResult->fetch_assoc()) {
                    $columns[] = $row['Field'];
                }
                $response['debug'][] = "Table '$table' columns: " . implode(', ', $columns);
            } else {
                $response['debug'][] = "Table '$table' does NOT exist";
            }
        }
    } else {
        $response['debug'][] = "Database 'facebook_db' does NOT exist";
    }
    
    $response['success'] = true;
    $response['message'] = 'Database check completed';
    
} catch (Exception $e) {
    $response['success'] = false;
    $response['message'] = 'Error: ' . $e->getMessage();
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
