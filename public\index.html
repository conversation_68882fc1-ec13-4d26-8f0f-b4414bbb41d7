<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Facebook Posts App</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
  <link rel="icon" href="favicon.ico">
</head>
<body>
  <!-- Left Sidebar Navigation -->
  <nav class="left-sidebar">
    <div class="sidebar-header">
      <i class="fab fa-facebook"></i>
      <span>Facebook App</span>
    </div>
    <ul class="sidebar-menu">
      <li>
        <a href="/index.html" class="sidebar-link active">
          <i class="fas fa-home"></i>
          <span>Posts</span>
        </a>
      </li>
      <li>
        <a href="/team.html" class="sidebar-link">
          <i class="fas fa-users"></i>
          <span>Team</span>
        </a>
      </li>
      <li>
        <a href="/updates.html" class="sidebar-link">
          <i class="fas fa-database"></i>
          <span>Database</span>
        </a>
      </li>
      <li>
        <a href="/proxy-manager.html" class="sidebar-link">
          <i class="fas fa-network-wired"></i>
          <span>Proxy Manager</span>
        </a>
      </li>
    </ul>
  </nav>

  <div class="app-container" style="margin-left: 250px;">
    <!-- Admin Sidebar -->
    <div class="admin-sidebar" id="admin-sidebar">
      <div class="sidebar-header">
        <h3><i class="fas fa-cog"></i> Admin Panel</h3>
        <button id="sidebar-close" class="sidebar-close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="sidebar-content">
        <h4>Facebook Authentication</h4>
        
        <div class="admin-control">
          <p class="auth-status" id="auth-status">
            <i class="fas fa-circle-notch fa-spin"></i> Checking authentication status...
          </p>
          
          <div class="auth-buttons">
            <button id="login-button" class="btn primary">
              <i class="fab fa-facebook"></i> Login to Facebook
            </button>
            
            <button id="save-session-button" class="btn success" disabled>
              <i class="fas fa-save"></i> Save Session & Close Browser
            </button>
            
            <button id="clear-session-button" class="btn danger">
              <i class="fas fa-trash"></i> Clear Saved Session
            </button>
          </div>
          
          <div class="auth-note">
            <p><small><i class="fas fa-info-circle"></i> Manual authentication required. Complete CAPTCHA and login forms when prompted.</small></p>
            <p><small><i class="fas fa-lock"></i> Credentials are only used for login and never stored.</small></p>
          </div>
        </div>
        
        <h4>Scraper Configuration</h4>
        
        <div class="admin-control">
          <label for="headless-mode">Headless Mode (Main Scraper):</label>
          <div class="toggle-container">
            <label class="toggle-switch">
              <input type="checkbox" id="headless-mode" checked>
              <span class="toggle-slider"></span>
            </label>
            <span id="headless-mode-label" class="toggle-label">Enabled</span>
            <span class="toggle-help" title="When enabled, the browser runs invisibly in the background. When disabled, you can see the browser window while scraping.">
              <i class="fas fa-question-circle"></i>
            </span>
          </div>
        </div>

        <div class="admin-control">
          <label for="metrics-headless-mode">Headless Mode (Metrics & Team Scraping):</label>
          <div class="toggle-container">
            <label class="toggle-switch">
              <input type="checkbox" id="metrics-headless-mode">
              <span class="toggle-slider"></span>
            </label>
            <span id="metrics-headless-mode-label" class="toggle-label">Disabled</span>
            <span class="toggle-help" title="When enabled, metrics update and team member scraping runs invisibly. When disabled, you can see the browser window for debugging.">
              <i class="fas fa-question-circle"></i>
            </span>
          </div>
        </div>
        
        <div class="admin-control">
          <label for="chunk-size">Chunk Size:</label>
          <input type="number" id="chunk-size" min="1" max="50" value="10">
        </div>

        <div class="admin-control">
          <label for="parallel-pages">Parallel Pages:</label>
          <input type="number" id="parallel-pages" min="1" max="10" value="1">
          <small>Number of pages to scrape simultaneously (1-10)</small>
        </div>

        <div class="admin-control">
          <label>Cycle Delay (minutes):</label>
          <div class="range-inputs">
            <input type="number" id="min-loop-delay" min="1" max="60" value="1" placeholder="Min">
            <span>to</span>
            <input type="number" id="max-loop-delay" min="1" max="60" value="10" placeholder="Max">
          </div>
        </div>
        
        <div class="admin-control">
          <label>Search Delay (seconds):</label>
          <div class="range-inputs">
            <input type="number" id="min-search-delay" min="1" max="60" value="1" placeholder="Min">
            <span>to</span>
            <input type="number" id="max-search-delay" min="1" max="60" value="60" placeholder="Max">
          </div>
        </div>

        <div class="admin-control">
          <label for="metrics-update-delay">Metrics Update Delay (seconds):</label>
          <input type="number" id="metrics-update-delay" min="1" max="300" value="30">
          <small>Delay between each post when updating all metrics (1-300 seconds)</small>
        </div>

        <div class="admin-control">
          <label for="posts-per-page">Posts Per Page:</label>
          <input type="number" id="posts-per-page" min="1" max="50" value="10">
          <small>Number of posts to extract per page (1-50)</small>
        </div>

        <h4>Auto-Export Settings</h4>

        <div class="admin-control">
          <label for="auto-export-enabled">Enable Auto-Export:</label>
          <div class="toggle-container">
            <label class="toggle-switch">
              <input type="checkbox" id="auto-export-enabled">
              <span class="toggle-slider"></span>
            </label>
            <span id="auto-export-enabled-label" class="toggle-label">Disabled</span>
            <span class="toggle-help" title="When enabled, automatically exports team member data at the scheduled time.">
              <i class="fas fa-question-circle"></i>
            </span>
          </div>
        </div>

        <div class="admin-control">
          <label for="auto-export-time">Export Time:</label>
          <input type="time" id="auto-export-time" value="02:00">
          <small>Time to perform automatic export (24-hour format)</small>
        </div>

        <div class="admin-control">
          <label for="auto-export-frequency">Export Frequency:</label>
          <select id="auto-export-frequency">
            <option value="daily">Daily</option>
            <option value="weekly">Weekly (Sunday)</option>
            <option value="monthly">Monthly (1st day)</option>
          </select>
        </div>

        <div class="admin-control">
          <label>Export Format:</label>
          <div class="checkbox-group">
            <label class="checkbox-item">
              <input type="checkbox" id="auto-export-json" checked>
              <span>JSON Export</span>
            </label>
          </div>
        </div>

        <div class="admin-control">
          <button id="trigger-manual-export" class="btn secondary">
            <i class="fas fa-download"></i> Trigger Export Now
          </button>
          <small>Manually trigger an immediate export</small>
        </div>

        <div class="admin-control" id="auto-export-status" style="display: none;">
          <label>Next Export:</label>
          <span id="next-export-time" class="status-text">-</span>
        </div>

        <button id="save-config" class="btn primary full-width">
          <i class="fas fa-save"></i> Save Configuration
        </button>
        
        <h4>Pages Management</h4>
        <div class="pages-list" id="pages-list">
          <div class="loading-pages">Loading pages...</div>
        </div>
        
        <div class="add-page-form">
          <input type="text" id="page-name" placeholder="Page Name">
          <input type="text" id="page-url" placeholder="Page URL">
          <button id="add-page" class="btn primary full-width">
            <i class="fas fa-plus"></i> Add Page
          </button>
        </div>
      </div>
    </div>
    
    <header>
      <div class="logo">
        <i class="fab fa-facebook"></i>
        <h1>Facebook Posts</h1>
      </div>

      <div class="header-controls">
        <button id="admin-toggle" class="btn admin-btn">
          <i class="fas fa-cog"></i> Admin
        </button>


        <div class="scraper-controls">
          <button id="start-scraper" class="btn primary">
            <i class="fas fa-play"></i> Start Scraper
          </button>
          <button id="stop-scraper" class="btn danger" disabled>
            <i class="fas fa-stop"></i> Stop Scraper
          </button>
          <button id="pause-scraper" class="btn warning" disabled>
            <i class="fas fa-pause"></i> Pause & Inspect
          </button>
          <button id="resume-scraper" class="btn success" disabled>
            <i class="fas fa-sync"></i> Resume Scraping
          </button>
        </div>

        <!-- Notification Bell -->
        <div class="notification-container">
          <button class="notification-bell" id="notification-bell">
            <i class="fas fa-bell"></i>
            <span class="notification-badge" id="notification-badge">0</span>
          </button>

          <!-- Notification Dropdown -->
          <div class="notification-dropdown" id="notification-dropdown">
            <div class="notification-header">
              <h3><i class="fas fa-bell"></i> Notifications</h3>
              <div class="notification-actions">
                <button class="btn-clear-all" id="clear-all-notifications" title="Clear All">
                  <i class="fas fa-trash"></i>
                </button>
                <button class="btn-close-dropdown" id="close-notification-dropdown" title="Close">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>

            <div class="notification-list" id="notification-list">
              <div class="no-notifications" id="no-notifications">
                <i class="fas fa-bell-slash"></i>
                <p>No notifications yet</p>
                <small>System notifications will appear here</small>
              </div>
            </div>

            <div class="notification-footer">
              <button class="btn-view-all" id="view-all-notifications">
                <i class="fas fa-list"></i> View All Notifications
              </button>
            </div>
          </div>
        </div>

        <button class="theme-toggle">
          <i class="fas fa-moon"></i>
        </button>
      </div>
    </header>

    <div class="status-bar">
      <div class="status-indicator">
        <span class="status-dot offline"></span>
        <span class="status-text">Scraper</span>
      </div>
      <div id="navigation-control" class="navigation-control hidden">
        <input type="text" id="navigate-url" placeholder="Enter Facebook URL..." class="navigate-input">
        <button id="navigate-btn" class="btn primary">
          <i class="fas fa-external-link-alt"></i> Open in Browser
        </button>
      </div>
      <div id="progress-container" class="progress-container hidden">
        <div id="progress-operations-list" class="progress-operations-list">
          <!-- Multiple operations will be added here dynamically -->
        </div>
      </div>
      <div id="next-cycle" class="next-cycle hidden">
        <i class="fas fa-clock"></i>
        <span>Next cycle: <span id="next-cycle-time">--:--</span></span>
      </div>
    </div>

    <div class="filter-bar">
      <div class="search-container">
        <i class="fas fa-search"></i>
        <input type="text" id="search-input" placeholder="Search posts...">
      </div>
      <div class="filter-buttons">
        <!-- All button stays as regular button -->
        <button id="all-filter-btn" data-filter="all" class="active">All</button>

        <!-- Team members dropdown -->
        <div class="team-filter-dropdown">
          <button id="team-dropdown-btn" class="team-dropdown-trigger">
            <i class="fas fa-users"></i>
            <span id="selected-team-text">Team Members</span>
            <i class="fas fa-chevron-down dropdown-arrow"></i>
          </button>
          <div class="team-dropdown-menu" id="team-dropdown-menu">
            <!-- Team member options will be dynamically generated -->
          </div>
        </div>
      </div>
      <div class="metrics-controls">
        <button id="update-all-metrics" class="btn warning">
          <i class="fas fa-chart-line"></i> Update All Metrics
        </button>
        <button id="stop-metrics-update" class="btn danger" disabled style="display: none;">
          <i class="fas fa-stop"></i> Stop Updates
        </button>
        <button id="open-browser" class="btn secondary">
          <i class="fas fa-globe"></i> Open Browser
        </button>


        <button id="export-team-posts" class="btn success">
          <i class="fas fa-download"></i> Export Team Data
        </button>
      </div>
    </div>

    <div id="posts-container" class="posts-container">
      <div class="loading">
        <div class="spinner"></div>
        <p>Loading posts...</p>
      </div>
    </div>

    <footer>
      <p>Amir &copy; 2025</p>
    </footer>
  </div>

  <!-- Post template (hidden) -->
  <template id="post-template">
    <div class="post-card">
      <div class="post-header">
        <div class="avatar-container">
          <div class="source-avatar">F</div>
        </div>
        <div class="header-content">
          <div class="header-top-row">
            <span class="page-name">Facebook Page</span>
            <span class="post-original-time">2h</span>
          </div>
          <div class="post-time-row">
            <span class="post-time">2 hours ago</span>
            <span class="team-member-label"></span>
          </div>
        </div>
      </div>
      <div class="post-content">
        <p class="post-text">This is a sample post content that will be replaced with actual content from Facebook.</p>
      </div>
      <div class="post-footer">
        <a href="#" target="_blank" class="view-source-btn">
          <i class="fas fa-external-link-alt"></i> View Source
        </a>
        <button class="change-link-btn">
          <i class="fas fa-edit"></i> Change Link
        </button>
        <button class="update-metrics-btn">
          <i class="fas fa-chart-line"></i> Update Metrics
        </button>
        <button class="delete-post-btn">
          <i class="fas fa-trash"></i> Delete Post
        </button>
      </div>
    </div>
  </template>

  <!-- Page item template -->
  <template id="page-item-template">
    <div class="page-item">
      <div class="page-info">
        <div class="page-name"></div>
        <div class="page-url"></div>
      </div>
      <button class="btn delete-page">
        <i class="fas fa-trash"></i>
      </button>
    </div>
  </template>

  <!-- Date Filter Modal -->
  <div id="date-filter-modal" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3><i class="fas fa-calendar-alt"></i> Filter Posts by Extraction Date</h3>
        <button class="modal-close" onclick="closeDateFilterModal()">&times;</button>
      </div>
      <div class="modal-body">
        <p class="modal-description">
          Select the date range for posts based on when they were <strong>extracted/scraped</strong> from Facebook, not the original post time.
        </p>

        <div class="date-filter-options">
          <div class="filter-option">
            <input type="radio" id="filter-all" name="dateFilter" value="all" checked>
            <label for="filter-all">
              <i class="fas fa-globe"></i>
              <span>All Posts</span>
              <small>Update metrics for all posts regardless of extraction date</small>
            </label>
          </div>

          <div class="filter-option">
            <input type="radio" id="filter-today" name="dateFilter" value="today">
            <label for="filter-today">
              <i class="fas fa-calendar-day"></i>
              <span>Today Only</span>
              <small>Posts extracted today</small>
            </label>
          </div>

          <div class="filter-option">
            <input type="radio" id="filter-last-hours" name="dateFilter" value="hours">
            <label for="filter-last-hours">
              <i class="fas fa-clock"></i>
              <span>Last <input type="number" id="hours-input" value="24" min="1" max="168"> Hours</span>
              <small>Posts extracted within the specified hours</small>
            </label>
          </div>

          <div class="filter-option">
            <input type="radio" id="filter-date-range" name="dateFilter" value="range">
            <label for="filter-date-range">
              <i class="fas fa-calendar-week"></i>
              <span>Custom Date Range</span>
              <small>Select specific start and end dates</small>
            </label>
          </div>
        </div>

        <div id="date-range-inputs" class="date-range-section" style="display: none;">
          <div class="date-input-group">
            <label for="start-date">From:</label>
            <input type="datetime-local" id="start-date">
          </div>
          <div class="date-input-group">
            <label for="end-date">To:</label>
            <input type="datetime-local" id="end-date">
          </div>
        </div>

        <div id="filter-preview" class="filter-preview">
          <i class="fas fa-info-circle"></i>
          <span id="preview-text">All posts will be updated</span>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="closeDateFilterModal()">Cancel</button>
        <button class="btn primary" onclick="startFilteredBulkUpdate()">
          <i class="fas fa-chart-line"></i> Update Metrics
        </button>
      </div>
    </div>
  </div>

  <!-- Change Link Modal -->
  <div id="changeLinkModal" class="change-link-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3><i class="fas fa-edit"></i> Change Post Link</h3>
        <button class="close-modal" onclick="closeChangeLinkModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <label for="currentUrl">Current URL:</label>
          <input type="text" id="currentUrl" readonly class="readonly-input">
        </div>
        <div class="form-group">
          <label for="newUrl">New URL:</label>
          <input type="url" id="newUrl" placeholder="Enter new URL..." class="url-input">
          <small class="help-text">Enter the new URL you want to use for the "View Source" button</small>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="closeChangeLinkModal()">Cancel</button>
        <button class="btn primary" onclick="saveChangedLink()">
          <i class="fas fa-save"></i> Save Changes
        </button>
      </div>
    </div>
  </div>

  <!-- Delete Post Confirmation Modal -->
  <div id="deletePostModal" class="delete-post-modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3><i class="fas fa-exclamation-triangle"></i> Confirm Deletion</h3>
        <button class="close-modal" onclick="closeDeletePostModal()">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="warning-message">
          <i class="fas fa-exclamation-triangle"></i>
          <p>Are you sure you want to delete this post?</p>
          <p><strong>This action cannot be undone.</strong></p>
        </div>
        <div class="post-preview">
          <div class="preview-label">Post to be deleted:</div>
          <div id="deletePostPreview" class="preview-content">
            <!-- Post preview will be inserted here -->
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="closeDeletePostModal()">Cancel</button>
        <button class="btn danger" onclick="confirmDeletePost()">
          <i class="fas fa-trash"></i> Delete Post
        </button>
      </div>
    </div>
  </div>

  <!-- All Notifications Modal -->
  <div id="allNotificationsModal" class="all-notifications-modal">
    <div class="modal-content large-modal">
      <div class="modal-header">
        <h3><i class="fas fa-bell"></i> All Notifications</h3>
        <div class="modal-header-actions">
          <button class="btn secondary small" id="clear-all-notifications-modal">
            <i class="fas fa-trash"></i> Clear All
          </button>
          <button class="close-modal" onclick="closeAllNotificationsModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
      <div class="modal-body">
        <div class="notifications-filter">
          <div class="filter-buttons">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="success">Success</button>
            <button class="filter-btn" data-filter="info">Info</button>
            <button class="filter-btn" data-filter="warning">Warning</button>
            <button class="filter-btn" data-filter="error">Error</button>
          </div>
          <div class="notification-stats">
            <span id="notification-count">0 notifications</span>
          </div>
        </div>

        <div class="all-notifications-list" id="all-notifications-list">
          <div class="no-notifications-modal" id="no-notifications-modal">
            <i class="fas fa-bell-slash"></i>
            <p>No notifications found</p>
            <small>System notifications will appear here as they occur</small>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn secondary" onclick="closeAllNotificationsModal()">Close</button>
      </div>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script src="app.js"></script>
</body>
</html>