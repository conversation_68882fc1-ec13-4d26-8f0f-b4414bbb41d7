// Debug script to check proxy manager state
const axios = require('axios');

async function debugProxyState() {
  try {
    console.log('🔍 Checking proxy manager state...\n');
    
    // Check debug endpoint
    const debugResponse = await axios.get('http://localhost:3000/api/proxy/debug');
    console.log('📊 Debug Info:');
    console.log(`   Total Proxies: ${debugResponse.data.debug.totalProxies}`);
    console.log(`   Stats Count: ${debugResponse.data.debug.statsCount}`);
    console.log(`   Failed Count: ${debugResponse.data.debug.failedCount}`);
    
    if (debugResponse.data.debug.totalProxies > 0) {
      console.log('\n📋 First 5 proxies:');
      debugResponse.data.debug.proxiesList.slice(0, 5).forEach((proxy, index) => {
        const parts = proxy.split(':');
        console.log(`   ${index + 1}. ${parts[0]}:${parts[1]}:${parts[2]}:****`);
      });
      
      if (debugResponse.data.debug.totalProxies > 5) {
        console.log(`   ... and ${debugResponse.data.debug.totalProxies - 5} more`);
      }
    }
    
    // Check stats endpoint
    const statsResponse = await axios.get('http://localhost:3000/api/proxy/stats');
    console.log('\n📈 Stats Info:');
    console.log(`   Total Proxies: ${statsResponse.data.totalProxies}`);
    console.log(`   Active Proxies: ${statsResponse.data.activeProxies}`);
    
    const statsKeys = Object.keys(statsResponse.data.stats);
    console.log(`   Stats Keys Count: ${statsKeys.length}`);
    
    if (statsKeys.length !== debugResponse.data.debug.totalProxies) {
      console.log('\n⚠️ MISMATCH DETECTED!');
      console.log(`   Proxy Manager has ${debugResponse.data.debug.totalProxies} proxies`);
      console.log(`   But stats only shows ${statsKeys.length} proxies`);
    }
    
    // Test adding a new proxy
    console.log('\n🧪 Testing proxy addition...');
    const testProxy = 'proxy.packetstream.io:31112:testuser:testpass_session-test123';
    
    try {
      const addResponse = await axios.post('http://localhost:3000/api/proxy/add', {
        proxy: testProxy
      });
      
      console.log(`   Add Result: ${addResponse.data.message}`);
      console.log(`   Total After Add: ${addResponse.data.totalProxies}`);
      
      // Remove the test proxy
      await axios.post('http://localhost:3000/api/proxy/remove', {
        proxy: testProxy
      });
      console.log('   Test proxy removed');
      
    } catch (error) {
      console.log(`   Add Error: ${error.response?.data?.error || error.message}`);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the server is running on port 3000');
    }
  }
}

debugProxyState();
