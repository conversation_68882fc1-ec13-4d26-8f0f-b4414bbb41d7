<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Export Cron Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.success {
            background: #28a745;
        }
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .btn.danger {
            background: #dc3545;
        }
        .status-item {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .status-good {
            background: #d4edda;
            color: #155724;
        }
        .status-bad {
            background: #f8d7da;
            color: #721c24;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Auto-Export Cron Test</h1>
        
        <div>
            <button class="btn" onclick="checkStatus()">📊 Check Status</button>
            <button class="btn success" onclick="testSetup()">🔧 Test Cron Setup</button>
            <button class="btn warning" onclick="testCron()">⏰ Schedule Test Export (1 min)</button>
            <button class="btn danger" onclick="manualTrigger()">🚀 Manual Trigger</button>
            <button class="btn" style="background: #6f42c1;" onclick="testScheduled()">🕒 Test Scheduled Function</button>
        </div>

        <h2>📊 Current Status</h2>
        <div id="status" class="status-box">Click "Check Status" to load...</div>

        <h2>📝 Test Results</h2>
        <div id="results" class="status-box">Test results will appear here...</div>

        <h2>🕐 Current Time</h2>
        <div id="time" class="status-box"></div>
    </div>

    <script>
        // Update current time every second
        function updateTime() {
            const now = new Date();
            document.getElementById('time').textContent = 
                `Local: ${now.toLocaleString()}\nUTC: ${now.toISOString()}`;
        }
        setInterval(updateTime, 1000);
        updateTime();

        async function checkStatus() {
            try {
                const response = await fetch('/api/auto-export/status');
                const data = await response.json();
                
                if (data.success) {
                    const status = data.status;
                    let statusHtml = '';
                    
                    statusHtml += `<div class="status-item ${status.enabled ? 'status-good' : 'status-bad'}">
                        Auto-Export Enabled: ${status.enabled}
                    </div>`;
                    
                    statusHtml += `<div class="status-item ${status.jsonEnabled ? 'status-good' : 'status-bad'}">
                        JSON Export Enabled: ${status.jsonEnabled}
                    </div>`;
                    
                    statusHtml += `<div class="status-item ${status.cronJobActive ? 'status-good' : 'status-bad'}">
                        Cron Job Active: ${status.cronJobActive}
                    </div>`;
                    
                    if (status.cronJobActive) {
                        statusHtml += `<div class="status-item ${status.cronJobRunning ? 'status-good' : 'status-warning'}">
                            Cron Job Running: ${status.cronJobRunning}
                        </div>`;
                        
                        statusHtml += `<div class="status-item ${status.cronJobScheduled ? 'status-good' : 'status-bad'}">
                            Cron Job Scheduled: ${status.cronJobScheduled}
                        </div>`;
                    }
                    
                    statusHtml += `<div class="status-item">
                        Schedule: ${status.frequency} at ${status.time}
                    </div>`;
                    
                    statusHtml += `<div class="status-item">
                        Next Export: ${status.nextExportTime || 'Not scheduled'}
                    </div>`;
                    
                    statusHtml += `<div class="status-item">
                        Current Time: ${status.currentTime}
                    </div>`;
                    
                    statusHtml += `<div class="status-item ${status.teamMembersCount > 0 ? 'status-good' : 'status-bad'}">
                        Team Members: ${status.teamMembersCount}
                    </div>`;
                    
                    statusHtml += `<div class="status-item ${status.exportsDirectoryExists ? 'status-good' : 'status-bad'}">
                        Exports Directory: ${status.exportsDirectoryExists ? 'Exists' : 'Missing'}
                    </div>`;
                    
                    statusHtml += `<div class="status-item ${status.postsFileExists ? 'status-good' : 'status-bad'}">
                        Posts File: ${status.postsFileExists ? 'Exists' : 'Missing'}
                    </div>`;
                    
                    document.getElementById('status').innerHTML = statusHtml;
                } else {
                    document.getElementById('status').textContent = `Error: ${data.error}`;
                }
            } catch (error) {
                document.getElementById('status').textContent = `Error: ${error.message}`;
            }
        }

        async function testSetup() {
            try {
                const response = await fetch('/api/auto-export/test-setup', { method: 'POST' });
                const data = await response.json();
                
                document.getElementById('results').textContent = JSON.stringify(data, null, 2);
                
                // Refresh status after test
                setTimeout(checkStatus, 1000);
            } catch (error) {
                document.getElementById('results').textContent = `Error: ${error.message}`;
            }
        }

        async function testCron() {
            try {
                const response = await fetch('/api/auto-export/test-cron', { method: 'POST' });
                const data = await response.json();
                
                document.getElementById('results').textContent = 
                    `Test cron scheduled!\n\n${JSON.stringify(data, null, 2)}\n\nWatch the server console for results...`;
            } catch (error) {
                document.getElementById('results').textContent = `Error: ${error.message}`;
            }
        }

        async function manualTrigger() {
            try {
                document.getElementById('results').textContent = 'Triggering manual export...';

                const response = await fetch('/api/auto-export/trigger', { method: 'POST' });
                const data = await response.json();

                document.getElementById('results').textContent =
                    `Manual trigger result:\n\n${JSON.stringify(data, null, 2)}\n\nCheck server console for detailed logs...`;
            } catch (error) {
                document.getElementById('results').textContent = `Error: ${error.message}`;
            }
        }

        async function testScheduled() {
            try {
                document.getElementById('results').textContent = 'Testing scheduled export function...';

                const response = await fetch('/api/auto-export/test-scheduled', { method: 'POST' });
                const data = await response.json();

                document.getElementById('results').textContent =
                    `Scheduled function test result:\n\n${JSON.stringify(data, null, 2)}\n\nCheck server console for detailed logs...`;
            } catch (error) {
                document.getElementById('results').textContent = `Error: ${error.message}`;
            }
        }

        // Auto-load status on page load
        window.onload = checkStatus;
    </script>
</body>
</html>
