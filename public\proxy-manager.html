<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proxy Manager - PacketStream Integration</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Dark Mode Support */
        body.dark-mode {
            --bg-color: #1a1a1a;
            --card-bg: #2d2d2d;
            --text-color: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: #404040;
            --input-bg: #3d3d3d;
            --white: #2d2d2d;
            --header-bg: #3d3d3d;
            --primary-color: #007bff;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        /* Light Mode (Default) */
        body {
            --bg-color: #f8f9fa;
            --card-bg: #ffffff;
            --text-color: #333333;
            --text-secondary: #6c757d;
            --border-color: #dee2e6;
            --input-bg: #ffffff;
            --white: #ffffff;
            --header-bg: #f8f9fa;
            --primary-color: #007bff;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        /* Left Sidebar Navigation */
        .left-sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 250px;
            height: 100vh;
            background: var(--white);
            border-right: 1px solid var(--border-color);
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
            font-size: 18px;
            color: var(--primary-color);
        }

        .sidebar-header i {
            font-size: 24px;
        }

        .sidebar-menu {
            list-style: none;
            padding: 20px 0;
            margin: 0;
            flex: 1;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 20px;
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-link:hover {
            background: rgba(0, 123, 255, 0.1);
            color: var(--primary-color);
        }

        .sidebar-link.active {
            background: rgba(0, 123, 255, 0.1);
            color: var(--primary-color);
            border-left-color: var(--primary-color);
            font-weight: 500;
        }

        .sidebar-link i {
            width: 20px;
            text-align: center;
        }

        .proxy-container {
            width: 100%;
            max-width: none;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .proxy-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: var(--primary-color);
        }
        
        .stat-label {
            color: var(--text-secondary);
            margin-top: 5px;
        }
        
        .proxy-controls {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .control-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .proxy-table {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .proxy-table table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .proxy-table th,
        .proxy-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        /* Style for the count column */
        .proxy-table th:first-child,
        .proxy-table td:first-child {
            width: 50px;
            text-align: center;
            font-weight: bold;
        }
        
        .proxy-table th {
            background: var(--header-bg);
            font-weight: 600;
            color: var(--text-color);
        }

        .proxy-table tr:hover {
            background: rgba(0, 123, 255, 0.1);
        }

        .proxy-table tr:nth-child(even) {
            background: rgba(0, 0, 0, 0.02);
        }

        body.dark-mode .proxy-table tr:nth-child(even) {
            background: rgba(255, 255, 255, 0.02);
        }
        
        .status-active {
            color: #28a745;
        }
        
        .status-failed {
            color: #dc3545;
        }
        
        .add-proxy-form {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background: var(--input-bg);
            color: var(--text-color);
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .notification {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }
        
        .notification.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .notification.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .proxy-format-help {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-size: 0.9em;
            color: var(--text-color);
        }

        .proxy-format-help code {
            background: var(--input-bg);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .proxy-preview {
            background: var(--input-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
            color: var(--text-color);
        }

        .preview-content {
            font-family: monospace;
            font-size: 0.9em;
            line-height: 1.4;
            color: var(--text-color);
        }

        .preview-proxy {
            padding: 2px 0;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-color);
        }

        .preview-proxy:last-child {
            border-bottom: none;
        }

        .file-input {
            margin-bottom: 10px;
        }

        .file-input input[type="file"] {
            background: var(--input-bg);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 8px;
            width: 100%;
        }

        .file-input input[type="file"]::-webkit-file-upload-button {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            margin-right: 10px;
            cursor: pointer;
        }

        .upload-stats {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 0.9em;
            color: var(--text-color);
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 0.8em;
        }

        .last-used {
            font-size: 0.8em;
            color: var(--text-secondary);
        }

        /* Header with theme toggle */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .page-header h1 {
            margin: 0;
            color: var(--text-color);
        }

        .page-header p {
            margin: 5px 0 0 0;
            color: var(--text-secondary);
        }

        .theme-toggle {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .theme-toggle:hover {
            background: var(--border-color);
        }

        /* Dark mode specific styles */

        body.dark-mode .preview-proxy {
            border-bottom-color: var(--border-color);
        }

        body.dark-mode .notification.success {
            background: #1e4d3a;
            border-color: #2e5d4a;
            color: #90ee90;
        }

        body.dark-mode .notification.error {
            background: #4d1e1e;
            border-color: #5d2e2e;
            color: #ffb3b3;
        }

        body.dark-mode .notification.warning {
            background: #4d3d1e;
            border-color: #5d4d2e;
            color: #ffeb3b;
        }

        /* Table dark mode styles */
        body.dark-mode table {
            background: var(--card-bg);
            color: var(--text-color);
        }

        body.dark-mode th {
            background: #3d3d3d;
            color: var(--text-color);
            border-color: var(--border-color);
        }

        body.dark-mode td {
            border-color: var(--border-color);
            color: var(--text-color);
        }

        body.dark-mode tr:nth-child(even) {
            background: #2a2a2a;
        }

        body.dark-mode tr:hover {
            background: #3a3a3a;
        }

        /* Status indicators dark mode */
        body.dark-mode .status-active {
            color: #4ade80;
        }

        body.dark-mode .status-failed {
            color: #f87171;
        }

        /* Button dark mode styles */
        body.dark-mode .btn-primary {
            background: #0d6efd;
            border-color: #0d6efd;
        }

        body.dark-mode .btn-success {
            background: #198754;
            border-color: #198754;
        }

        body.dark-mode .btn-warning {
            background: #fd7e14;
            border-color: #fd7e14;
        }

        body.dark-mode .btn-danger {
            background: #dc3545;
            border-color: #dc3545;
        }

        /* File input dark mode */
        body.dark-mode input[type="file"] {
            background: var(--input-bg);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 8px;
        }

        /* Checkbox dark mode */
        body.dark-mode input[type="checkbox"] {
            accent-color: var(--primary-color);
        }

        /* Scrollbar dark mode */
        body.dark-mode ::-webkit-scrollbar {
            width: 8px;
        }

        body.dark-mode ::-webkit-scrollbar-track {
            background: var(--card-bg);
        }

        body.dark-mode ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 4px;
        }

        body.dark-mode ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Ensure full width coverage */
        body {
            margin: 0;
            padding: 0;
            width: 100%;
            overflow-x: hidden;
        }

        .proxy-container {
            min-height: 100vh;
        }
    </style>
</head>
<body>
    <!-- Left Sidebar Navigation -->
    <nav class="left-sidebar">
        <div class="sidebar-header">
            <i class="fab fa-facebook"></i>
            <span>Facebook App</span>
        </div>
        <ul class="sidebar-menu">
            <li>
                <a href="/index.html" class="sidebar-link">
                    <i class="fas fa-home"></i>
                    <span>Posts</span>
                </a>
            </li>
            <li>
                <a href="/team.html" class="sidebar-link">
                    <i class="fas fa-users"></i>
                    <span>Team</span>
                </a>
            </li>
            <li>
                <a href="/updates.html" class="sidebar-link">
                    <i class="fas fa-database"></i>
                    <span>Database</span>
                </a>
            </li>
            <li>
                <a href="/proxy-manager.html" class="sidebar-link active">
                    <i class="fas fa-network-wired"></i>
                    <span>Proxy Manager</span>
                </a>
            </li>
        </ul>
    </nav>

    <div class="proxy-container" style="margin-left: 250px; padding: 20px; width: calc(100% - 250px); box-sizing: border-box;">
        <!-- Page Header with Theme Toggle -->
        <div class="page-header">
            <div>
                <h1><i class="fas fa-network-wired"></i> Proxy Manager</h1>
                <p>Manage PacketStream proxies for scraping and metrics operations</p>
            </div>
            <button class="theme-toggle" onclick="toggleTheme()" title="Toggle Dark/Light Mode">
                <i class="fas fa-moon"></i>
                <span>Dark Mode</span>
            </button>
        </div>

        <div id="notification" class="notification"></div>
        
        <!-- Proxy Statistics -->
        <div class="proxy-stats">
            <div class="stat-card">
                <div class="stat-value" id="totalProxies">-</div>
                <div class="stat-label">Total Proxies</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeProxies">-</div>
                <div class="stat-label">Active Proxies</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="failedProxies">-</div>
                <div class="stat-label">Failed Proxies</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="currentIP">-</div>
                <div class="stat-label">Current IP</div>
            </div>
        </div>
        
        <!-- Proxy Controls -->
        <div class="proxy-controls">
            <h3>Proxy Controls</h3>
            <div class="control-group">
                <button class="btn btn-success" onclick="testConnectivity()">
                    <i class="fas fa-wifi"></i> Test Connectivity
                </button>
                <button class="btn btn-primary" onclick="testAllProxies()">
                    <i class="fas fa-check-circle"></i> Test All Proxies
                </button>
                <button class="btn btn-warning" onclick="resetFailedProxies()">
                    <i class="fas fa-refresh"></i> Reset Failed
                </button>
                <button class="btn btn-warning" onclick="resetAllStats()">
                    <i class="fas fa-eraser"></i> Reset All Stats
                </button>
                <button class="btn btn-primary" onclick="refreshStats()">
                    <i class="fas fa-sync"></i> Refresh Stats
                </button>
                <button class="btn btn-danger" onclick="deleteAllProxies()">
                    <i class="fas fa-trash-alt"></i> Delete All Proxies
                </button>
            </div>
            <div class="control-group">
                <label>
                    <input type="checkbox" id="rotationEnabled" onchange="toggleRotation()">
                    Enable Proxy Rotation
                </label>
            </div>
        </div>
        
        <!-- Add New Proxy -->
        <div class="add-proxy-form">
            <h3>Add Proxies</h3>

            <!-- Single Proxy Input -->
            <div class="form-group">
                <label for="newProxy">Single Proxy:</label>
                <input type="text" id="newProxy" placeholder="proxy.packetstream.io:31112:username:password_session-sessionid">
                <button class="btn btn-primary" onclick="addProxy()" style="margin-top: 10px;">
                    <i class="fas fa-plus"></i> Add Single Proxy
                </button>
            </div>

            <!-- Bulk Upload -->
            <div class="form-group">
                <label for="proxyFile">Bulk Upload (TXT file):</label>
                <input type="file" id="proxyFile" accept=".txt" onchange="handleFileSelect(event)">
                <div class="proxy-format-help">
                    <strong>File Format:</strong> One proxy per line<br>
                    <strong>Example:</strong><br>
                    <code>proxy.packetstream.io:31112:alijaafar:UexHX6M2yDw38CKd_session-JMORLeLr</code><br>
                    <code>proxy.packetstream.io:31112:alijaafar:UexHX6M2yDw38CKd_session-ckQGvkB5</code><br>
                    <code>proxy.packetstream.io:31112:alijaafar:UexHX6M2yDw38CKd_session-dIARSCKZ</code>
                </div>
                <button class="btn btn-success" onclick="uploadProxies()" id="uploadBtn" disabled>
                    <i class="fas fa-upload"></i> Upload Proxies
                </button>
            </div>

            <!-- Proxy Preview -->
            <div id="proxyPreview" class="proxy-preview" style="display: none;">
                <h4>Preview (<span id="previewCount">0</span> proxies):</h4>
                <div id="previewContent" class="preview-content"></div>
            </div>
        </div>
        
        <!-- Proxy Table -->
        <div class="proxy-table">
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Host:Port</th>
                        <th>Username</th>
                        <th>Session ID</th>
                        <th>Status</th>
                        <th>Requests</th>
                        <th>Success Rate</th>
                        <th>Avg Response Time</th>
                        <th>Last Used</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="proxyTableBody">
                    <tr>
                        <td colspan="10" style="text-align: center; padding: 40px;">
                            Loading proxy data...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Failed Proxies Section -->
        <div style="margin-top: 30px;">
            <h3 style="color: var(--danger-color); margin-bottom: 15px;">
                <i class="fas fa-exclamation-triangle"></i>
                Failed Proxies (<span id="failedProxiesCount">0</span>)
            </h3>
            <p style="color: var(--text-secondary); margin-bottom: 20px;">
                Proxies that have been permanently removed from rotation due to connection failures.
            </p>

            <!-- Diagnose Button -->
            <div style="margin-bottom: 20px;">
                <button onclick="diagnoseProxies()"
                        class="btn btn-warning"
                        style="background: var(--warning-color); color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                    <i class="fas fa-search"></i> Diagnose & Fix Missing Failed Proxies
                </button>
                <span style="margin-left: 10px; color: var(--text-secondary); font-size: 0.9em;">
                    Click this if some failed proxies are not showing in the list below
                </span>
            </div>

            <div class="proxy-table" id="failedProxiesTable" style="display: none;">
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Host:Port</th>
                            <th>Username</th>
                            <th>Total Requests</th>
                            <th>Failures</th>
                            <th>Last Failure</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="failedProxyTableBody">
                        <tr>
                            <td colspan="7" style="text-align: center; padding: 40px;">
                                Loading failed proxy data...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="noFailedProxies" style="text-align: center; padding: 40px; color: var(--text-secondary);">
                <i class="fas fa-check-circle" style="font-size: 48px; color: var(--success-color); margin-bottom: 15px;"></i>
                <p>No failed proxies! All proxies are working properly.</p>
            </div>
        </div>
    </div>

    <script>
        let proxyStats = {};
        let selectedProxies = [];

        // Load initial data
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
            loadRotationStatus();
            initializeTheme();
        });

        // Theme management
        function initializeTheme() {
            const isDarkMode = localStorage.getItem('darkMode') === 'true';
            if (isDarkMode) {
                document.body.classList.add('dark-mode');
                updateThemeToggle(true);
            }
        }

        function toggleTheme() {
            const isDarkMode = document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', isDarkMode);
            updateThemeToggle(isDarkMode);
            showNotification(isDarkMode ? 'Switched to Dark Mode 🌙' : 'Switched to Light Mode ☀️', 'success');
        }

        function updateThemeToggle(isDarkMode) {
            const themeToggle = document.querySelector('.theme-toggle');
            const icon = themeToggle.querySelector('i');
            const text = themeToggle.querySelector('span');

            if (isDarkMode) {
                icon.className = 'fas fa-sun';
                text.textContent = 'Light Mode';
            } else {
                icon.className = 'fas fa-moon';
                text.textContent = 'Dark Mode';
            }
        }
        
        async function refreshStats() {
            try {
                const response = await fetch('/api/proxy/stats');
                const data = await response.json();

                if (data.success) {
                    proxyStats = data.stats;
                    updateStatsDisplay(data);
                    updateProxyTable();
                } else {
                    showNotification('Failed to load proxy stats: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error loading proxy stats: ' + error.message, 'error');
            }

            // Load failed proxies
            await loadFailedProxies();

            // Auto-diagnose on page load to catch orphaned proxies
            await diagnoseProxiesQuietly();
        }
        
        function updateStatsDisplay(data) {
            document.getElementById('totalProxies').textContent = data.totalProxies;
            document.getElementById('activeProxies').textContent = data.activeProxies;
            document.getElementById('failedProxies').textContent = data.totalProxies - data.activeProxies;
        }
        
        function updateProxyTable() {
            const tbody = document.getElementById('proxyTableBody');

            if (Object.keys(proxyStats).length === 0) {
                tbody.innerHTML = '<tr><td colspan="10" style="text-align: center; padding: 40px;">No proxies configured</td></tr>';
                return;
            }

            tbody.innerHTML = '';

            Object.entries(proxyStats).forEach(([proxyId, stats], index) => {
                const row = document.createElement('tr');

                const lastUsedText = stats.lastUsed ?
                    formatLastUsed(new Date(stats.lastUsed)) :
                    'Never';

                // Display host:port for the main identifier, but use full proxy for actions
                const displayId = `${stats.host}:${stats.port}`;
                const shortSessionId = stats.sessionId.substring(0, 8) + '...';

                row.innerHTML = `
                    <td style="text-align: center; font-weight: bold; color: var(--primary-color);">${index + 1}</td>
                    <td>${displayId}</td>
                    <td>${stats.username}</td>
                    <td class="${stats.isFailed ? 'status-failed' : 'status-active'}">
                        <i class="fas fa-${stats.isFailed ? 'times-circle' : 'check-circle'}"></i>
                        ${stats.isFailed ? 'Failed' : 'Active'}
                    </td>
                    <td>${stats.requests}</td>
                    <td>${stats.successRate}</td>
                    <td>${stats.avgResponseTime ? Math.round(stats.avgResponseTime) + 'ms' : 'N/A'}</td>
                    <td>
                        <div>${lastUsedText}</div>
                        ${stats.lastUsed ? `<div class="last-used">${new Date(stats.lastUsed).toLocaleString()}</div>` : ''}
                        <div class="last-used">Session: ${shortSessionId}</div>
                    </td>
                    <td>
                        <button class="btn btn-success btn-sm" onclick="testSingleProxy('${stats.fullProxy}')" title="Test Proxy">
                            <i class="fas fa-vial"></i>
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="removeProxy('${stats.fullProxy}')" title="Remove Proxy">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }
        
        async function testConnectivity() {
            const btn = event.target;
            btn.classList.add('loading');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
            
            try {
                const response = await fetch('/api/proxy/test', { method: 'POST' });
                const data = await response.json();
                
                if (data.success && data.connectivity.success) {
                    document.getElementById('currentIP').textContent = data.connectivity.ip;
                    showNotification('Proxy connectivity test successful! Current IP: ' + data.connectivity.ip, 'success');
                } else {
                    showNotification('Proxy connectivity test failed: ' + (data.connectivity.error || data.error), 'error');
                }
            } catch (error) {
                showNotification('Error testing connectivity: ' + error.message, 'error');
            } finally {
                btn.classList.remove('loading');
                btn.innerHTML = '<i class="fas fa-wifi"></i> Test Connectivity';
            }
        }
        
        async function testAllProxies() {
            const btn = event.target;
            btn.classList.add('loading');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing All...';
            
            try {
                const response = await fetch('/api/proxy/test-all', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    const successful = data.results.filter(r => r.success).length;
                    const total = data.results.length;
                    showNotification(`Tested ${total} proxies. ${successful} successful, ${total - successful} failed.`, 'success');
                    refreshStats();
                } else {
                    showNotification('Failed to test proxies: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error testing proxies: ' + error.message, 'error');
            } finally {
                btn.classList.remove('loading');
                btn.innerHTML = '<i class="fas fa-check-circle"></i> Test All Proxies';
            }
        }
        
        async function resetFailedProxies() {
            try {
                const response = await fetch('/api/proxy/reset-failed', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showNotification('Failed proxies reset successfully', 'success');
                    refreshStats();
                } else {
                    showNotification('Failed to reset proxies: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error resetting proxies: ' + error.message, 'error');
            }
        }

        async function resetAllStats() {
            if (!confirm('Are you sure you want to reset ALL proxy statistics to 0? This will clear all request counts, success rates, and usage history.')) {
                return;
            }

            // Double confirmation for safety
            if (!confirm('This will reset ALL statistics for ALL proxies. Are you absolutely sure?')) {
                return;
            }

            try {
                const response = await fetch('/api/proxy/reset-stats', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showNotification('All proxy statistics reset to 0', 'success');
                    refreshStats();
                } else {
                    showNotification('Failed to reset statistics: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error resetting statistics: ' + error.message, 'error');
            }
        }

        async function resetAllStats() {
            if (!confirm('Reset ALL proxy statistics to 0? This clears all request counts and usage history.')) {
                return;
            }

            try {
                const response = await fetch('/api/proxy/reset-stats', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showNotification('All proxy statistics reset to 0', 'success');
                    refreshStats();
                } else {
                    showNotification('Failed to reset statistics: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error resetting statistics: ' + error.message, 'error');
            }
        }

        async function toggleRotation() {
            const enabled = document.getElementById('rotationEnabled').checked;
            
            try {
                const response = await fetch('/api/proxy/toggle-rotation', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ enabled })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showNotification(data.message, 'success');
                } else {
                    showNotification('Failed to toggle rotation: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error toggling rotation: ' + error.message, 'error');
            }
        }
        
        async function addProxy() {
            const proxyInput = document.getElementById('newProxy');
            const proxy = proxyInput.value.trim();
            
            if (!proxy) {
                showNotification('Please enter a proxy string', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/proxy/add', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ proxy })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showNotification('Proxy added successfully', 'success');
                    proxyInput.value = '';
                    refreshStats();
                } else {
                    showNotification('Failed to add proxy: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error adding proxy: ' + error.message, 'error');
            }
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            const uploadBtn = document.getElementById('uploadBtn');
            const previewDiv = document.getElementById('proxyPreview');

            if (!file) {
                uploadBtn.disabled = true;
                previewDiv.style.display = 'none';
                selectedProxies = [];
                return;
            }

            if (!file.name.endsWith('.txt')) {
                showNotification('Please select a .txt file', 'error');
                event.target.value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                const lines = content.split('\n').map(line => line.trim()).filter(line => line.length > 0);

                selectedProxies = lines;

                if (selectedProxies.length === 0) {
                    showNotification('No valid proxies found in file', 'error');
                    uploadBtn.disabled = true;
                    previewDiv.style.display = 'none';
                    return;
                }

                // Show preview
                document.getElementById('previewCount').textContent = selectedProxies.length;
                const previewContent = document.getElementById('previewContent');
                previewContent.innerHTML = selectedProxies.slice(0, 10).map((proxy, index) =>
                    `<div class="preview-proxy">${index + 1}. ${proxy}</div>`
                ).join('');

                if (selectedProxies.length > 10) {
                    previewContent.innerHTML += `<div class="preview-proxy"><strong>... and ${selectedProxies.length - 10} more</strong></div>`;
                }

                previewDiv.style.display = 'block';
                uploadBtn.disabled = false;
            };

            reader.readAsText(file);
        }

        async function uploadProxies() {
            if (selectedProxies.length === 0) {
                showNotification('No proxies to upload', 'error');
                return;
            }

            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.classList.add('loading');
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

            let successCount = 0;
            let errorCount = 0;

            try {
                for (const proxy of selectedProxies) {
                    try {
                        const response = await fetch('/api/proxy/add', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ proxy })
                        });

                        const data = await response.json();
                        if (data.success) {
                            successCount++;
                        } else {
                            errorCount++;
                            console.warn(`Failed to add proxy ${proxy}: ${data.error}`);
                        }
                    } catch (error) {
                        errorCount++;
                        console.error(`Error adding proxy ${proxy}:`, error);
                    }

                    // Small delay to avoid overwhelming the server
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                showNotification(`Upload completed: ${successCount} successful, ${errorCount} failed`,
                    errorCount === 0 ? 'success' : 'warning');

                // Clear file input and preview
                document.getElementById('proxyFile').value = '';
                document.getElementById('proxyPreview').style.display = 'none';
                selectedProxies = [];

                refreshStats();

            } catch (error) {
                showNotification('Error during upload: ' + error.message, 'error');
            } finally {
                uploadBtn.classList.remove('loading');
                uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload Proxies';
                uploadBtn.disabled = true;
            }
        }

        async function removeProxy(proxyId) {
            if (!confirm('Are you sure you want to remove this proxy?')) {
                return;
            }

            try {
                const response = await fetch('/api/proxy/remove', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ proxy: proxyId })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification('Proxy removed successfully', 'success');
                    refreshStats();
                } else {
                    showNotification('Failed to remove proxy: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error removing proxy: ' + error.message, 'error');
            }
        }

        async function deleteAllProxies() {
            const totalProxies = document.getElementById('totalProxies').textContent;

            if (totalProxies === '0' || totalProxies === '-') {
                showNotification('No proxies to delete', 'warning');
                return;
            }

            if (!confirm(`Are you sure you want to delete ALL ${totalProxies} proxies? This action cannot be undone.`)) {
                return;
            }

            // Double confirmation for safety
            if (!confirm('This will permanently delete all proxies. Are you absolutely sure?')) {
                return;
            }

            try {
                const response = await fetch('/api/proxy/remove-all', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();

                if (data.success) {
                    showNotification(`Successfully deleted all ${data.removedCount} proxies`, 'success');
                    refreshStats();
                } else {
                    showNotification('Failed to delete all proxies: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error deleting all proxies: ' + error.message, 'error');
            }
        }
        
        function loadRotationStatus() {
            // Default to enabled - you can add an API endpoint to get current status if needed
            document.getElementById('rotationEnabled').checked = true;
        }

        function formatLastUsed(date) {
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMs / 3600000);
            const diffDays = Math.floor(diffMs / 86400000);

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins} min${diffMins > 1 ? 's' : ''} ago`;
            if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
            if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
            return date.toLocaleDateString();
        }

        async function testSingleProxy(fullProxy) {
            const btn = event.target.closest('button');
            const originalContent = btn.innerHTML;
            btn.classList.add('loading');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            try {
                const response = await fetch('/api/proxy/test-single', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ proxy: fullProxy })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification(`Proxy test successful! Response time: ${data.responseTime}ms`, 'success');
                    refreshStats();
                } else {
                    showNotification(`Proxy test failed: ${data.error}`, 'error');
                }
            } catch (error) {
                showNotification('Error testing proxy: ' + error.message, 'error');
            } finally {
                btn.classList.remove('loading');
                btn.innerHTML = originalContent;
            }
        }
        
        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.style.display = 'block';
            
            setTimeout(() => {
                notification.style.display = 'none';
            }, 5000);
        }

        // Failed Proxies Management
        async function loadFailedProxies() {
            try {
                const response = await fetch('/api/proxy/failed');
                const data = await response.json();

                if (data.success) {
                    updateFailedProxiesDisplay(data.failedProxies);
                } else {
                    console.error('Failed to load failed proxies:', data.error);
                }
            } catch (error) {
                console.error('Error loading failed proxies:', error.message);
            }
        }

        function updateFailedProxiesDisplay(failedProxies) {
            const countElement = document.getElementById('failedProxiesCount');
            const tableElement = document.getElementById('failedProxiesTable');
            const noFailedElement = document.getElementById('noFailedProxies');
            const tbody = document.getElementById('failedProxyTableBody');

            countElement.textContent = failedProxies.length;

            if (failedProxies.length === 0) {
                tableElement.style.display = 'none';
                noFailedElement.style.display = 'block';
                return;
            }

            tableElement.style.display = 'block';
            noFailedElement.style.display = 'none';

            tbody.innerHTML = '';

            failedProxies.forEach((proxy, index) => {
                const row = document.createElement('tr');

                const lastUsedText = proxy.lastUsed ?
                    formatLastUsed(new Date(proxy.lastUsed)) :
                    'Never';

                const hostPort = proxy.proxy.split(':').slice(0, 2).join(':');
                const username = proxy.proxy.split(':')[2] || 'N/A';

                row.innerHTML = `
                    <td style="text-align: center; font-weight: bold; color: var(--danger-color);">${index + 1}</td>
                    <td>${hostPort}</td>
                    <td>${username}</td>
                    <td>${proxy.requests || 0}</td>
                    <td style="color: var(--danger-color);">${proxy.failures || 0}</td>
                    <td>${lastUsedText}</td>
                    <td>
                        <button onclick="testSingleProxy('${proxy.proxy}')"
                                class="btn btn-sm"
                                style="background: var(--primary-color); color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-wifi"></i> Test
                        </button>
                        <button onclick="restoreProxy('${proxy.proxy}')"
                                class="btn btn-sm"
                                style="background: var(--warning-color); color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; margin-left: 5px;">
                            <i class="fas fa-undo"></i> Restore
                        </button>
                        <button onclick="deleteProxy('${proxy.proxy}')"
                                class="btn btn-sm"
                                style="background: var(--danger-color); color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; margin-left: 5px;">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        async function restoreProxy(proxyString) {
            if (!confirm('Are you sure you want to restore this proxy to active rotation?')) {
                return;
            }

            try {
                const response = await fetch('/api/proxy/restore', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ proxy: proxyString })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification('Proxy restored successfully!', 'success');
                    refreshStats(); // Refresh both active and failed lists
                } else {
                    showNotification('Failed to restore proxy: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error restoring proxy: ' + error.message, 'error');
            }
        }

        async function deleteProxy(proxyString) {
            if (!confirm('Are you sure you want to permanently delete this proxy? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch('/api/proxy/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ proxy: proxyString })
                });

                const data = await response.json();

                if (data.success) {
                    showNotification('Proxy deleted permanently!', 'success');
                    loadFailedProxies(); // Refresh failed proxies list
                } else {
                    showNotification('Failed to delete proxy: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error deleting proxy: ' + error.message, 'error');
            }
        }

        async function diagnoseProxies() {
            try {
                showNotification('Diagnosing proxy state...', 'info');

                const response = await fetch('/api/proxy/diagnose', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success) {
                    const result = data.result;
                    let message = `Diagnosis completed! `;
                    message += `Active: ${result.activeProxies}, `;
                    message += `Failed: ${result.permanentlyFailed}, `;
                    message += `Fixed: ${result.orphanedFixed}`;

                    showNotification(message, result.orphanedFixed > 0 ? 'success' : 'info');

                    // Refresh both lists to show updated state
                    refreshStats();
                } else {
                    showNotification('Failed to diagnose proxies: ' + data.error, 'error');
                }
            } catch (error) {
                showNotification('Error diagnosing proxies: ' + error.message, 'error');
            }
        }

        async function diagnoseProxiesQuietly() {
            try {
                const response = await fetch('/api/proxy/diagnose', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (data.success && data.result.orphanedFixed > 0) {
                    console.log(`🔧 Auto-fixed ${data.result.orphanedFixed} orphaned proxies on page load`);
                    // Refresh lists to show the fixed proxies
                    await loadFailedProxies();
                }
            } catch (error) {
                console.error('Error in quiet diagnosis:', error.message);
            }
        }

        // Auto-refresh stats every 30 seconds
        setInterval(refreshStats, 30000);
    </script>
</body>
</html>
