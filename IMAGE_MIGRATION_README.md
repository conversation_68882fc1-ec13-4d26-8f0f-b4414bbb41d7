# Facebook Image Migration Solution

## Problem
Facebook post images become corrupted after 1-2 weeks because Facebook's `scontent` URLs contain temporary tokens and session parameters that expire.

## Solution
This solution downloads Facebook images locally when posts are scraped and stores them in the `public/images/` directory, replacing the temporary Facebook URLs with permanent local URLs.

## How It Works

### 1. Automatic Image Download (New Posts)
- When new posts are scraped, images are automatically downloaded
- Images are saved to `public/images/` with unique filenames
- Original Facebook URLs are replaced with local `/images/` URLs
- If download fails, original URL is kept as fallback

### 2. Migration Script (Existing Posts)
Run the migration script to download images for existing posts:

```bash
node migrate-images.js
```

### 3. Image Storage Structure
```
public/images/
├── postId_0_hash.jpg    # First image of a post
├── postId_1_hash.jpg    # Second image of a post
└── ...
```

## Features

### ✅ **Automatic Download**
- New posts automatically download images during scraping
- No manual intervention required

### ✅ **Unique Filenames**
- Uses post ID + image index + URL hash for unique names
- Prevents conflicts and duplicates

### ✅ **Fallback Protection**
- If download fails, keeps original URL
- Graceful error handling

### ✅ **Performance Optimized**
- Checks if image already exists before downloading
- Async downloads with proper error handling
- Small delays between downloads to avoid rate limiting

### ✅ **Migration Support**
- Script to migrate existing posts
- Progress tracking and detailed logging
- Safe operation (keeps originals if download fails)

## Usage

### For New Posts
Images are automatically downloaded when scraping. No action needed.

### For Existing Posts
1. Run the migration script:
   ```bash
   node migrate-images.js
   ```

2. Monitor the progress in the console

3. Check the summary at the end

### Manual Cleanup (Optional)
If you want to remove old corrupted images from the database:

```bash
# This will be implemented if needed
node cleanup-corrupted-images.js
```

## Technical Details

### Image Download Function
- Downloads Facebook `scontent` images
- Saves as JPG files in `public/images/`
- Uses MD5 hash for unique filenames
- 30-second timeout per download
- Proper User-Agent headers

### URL Format
- **Original**: `https://scontent.fbgw41-1.fna.fbcdn.net/v/t39.30808-6/...`
- **Local**: `/images/postId_0_hash.jpg`

### Error Handling
- Network errors: Falls back to original URL
- File system errors: Logs error and continues
- Invalid URLs: Skips non-Facebook images

## Benefits

1. **🔒 Permanent Images**: Images never expire or become corrupted
2. **⚡ Faster Loading**: Local images load faster than Facebook CDN
3. **📱 Offline Access**: Images work even when Facebook is down
4. **🛡️ Privacy**: No external requests to Facebook for images
5. **📊 Reliable Analytics**: Images always display correctly in reports

## File Structure
```
├── migrate-images.js           # Migration script for existing posts
├── public/
│   ├── images/                # Downloaded images directory
│   └── ...
├── server.js                  # Contains download functions
└── final_filtered_posts.json  # Posts with updated image URLs
```

## Monitoring
- Check console logs during migration
- Monitor `public/images/` directory size
- Verify posts display images correctly in the web interface

## Troubleshooting

### Images Not Downloading
1. Check internet connection
2. Verify Facebook URLs are accessible
3. Check disk space in `public/images/`

### Migration Script Issues
1. Ensure `final_filtered_posts.json` exists
2. Check file permissions for `public/images/`
3. Run with administrator privileges if needed

### Display Issues
1. Verify images exist in `public/images/`
2. Check browser console for 404 errors
3. Ensure static file serving is working
