// Initialize Socket.io connection
const socket = io();

// DOM Elements
const tableNavBtns = document.querySelectorAll('.table-nav-btn');
const tableSections = document.querySelectorAll('.table-section');
const searchInput = document.getElementById('table-search');
const filterSelect = document.getElementById('table-filter');
const refreshBtn = document.getElementById('refresh-all');
const exportBtn = document.getElementById('export-csv');
const loadingState = document.getElementById('loading-state');
const emptyState = document.getElementById('empty-state');
const lastUpdatedTime = document.getElementById('last-updated-time');
const themeToggle = document.querySelector('.theme-toggle');
const syncPanel = document.getElementById('sync-panel');
const headerAutoSyncIndicator = document.getElementById('header-auto-sync-indicator');
const headerSyncStatus = document.getElementById('header-sync-status');
const headerCountdown = document.getElementById('header-countdown');
const testExtractionBtn = document.getElementById('test-extraction-btn');
const syncFromJsonBtn = document.getElementById('sync-from-json');
const syncToJsonBtn = document.getElementById('sync-to-json');

// Auto-sync elements
const autoSyncToggle = document.getElementById('auto-sync-enabled');
const autoSyncStatus = document.getElementById('auto-sync-status');
const syncTimer = document.getElementById('sync-timer');
const autoSyncInfo = document.getElementById('auto-sync-info');
const countdownTimer = document.getElementById('countdown-timer');
const syncCountDisplay = document.getElementById('sync-count');

// Data storage
let postsData = [];
let pagesData = [];
let membersData = [];
let currentTable = 'posts';
let currentSort = { column: null, direction: 'asc' };
let currentPage = 1;
const itemsPerPage = 25; // Reduced for better performance and UX

// Auto-sync variables
let autoSyncEnabled = false;
let autoSyncInterval = 300000; // 5 minutes default
let autoSyncTimer = null;
let countdownInterval = null;
let nextSyncTime = null;
let totalSyncCount = 0;

// Database configuration
const USE_DATABASE = false; // Set to false to show JSON data (your Electron data)
const DATABASE_API_URL = 'http://localhost:3000/api/database';

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
  initializeTheme();
  setupEventListeners();
  initializeAutoSync();
  initializeSyncPanel();
  loadAllData();

  // Set up a periodic check to ensure auto-sync timer is running
  setInterval(() => {
    ensureAutoSyncRunning();
  }, 30000); // Check every 30 seconds
});

// Theme management
function initializeTheme() {
  if (localStorage.getItem('darkMode') === 'true') {
    document.body.classList.add('dark-mode');
    themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
  }
}

themeToggle.addEventListener('click', function() {
  document.body.classList.toggle('dark-mode');
  const isDark = document.body.classList.contains('dark-mode');
  localStorage.setItem('darkMode', isDark);
  themeToggle.innerHTML = isDark ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
});

// Event listeners
function setupEventListeners() {
  // Table navigation
  tableNavBtns.forEach(btn => {
    btn.addEventListener('click', () => switchTable(btn.dataset.table));
  });

  // Search functionality
  searchInput.addEventListener('input', debounce(handleSearch, 300));

  // Filter functionality
  filterSelect.addEventListener('change', handleFilter);

  // Refresh data - force fresh load from Electron app
  refreshBtn.addEventListener('click', () => {
    console.log('🔄 Force refreshing all data from Electron app...');
    showNotification('Refreshing data from Electron app...', 'info');
    loadAllData();
  });

  // Export functionality
  exportBtn.addEventListener('click', exportCurrentTable);

  // Database sync functionality
  testExtractionBtn.addEventListener('click', testExtraction);
  syncFromJsonBtn.addEventListener('click', syncFromJSON);
  syncToJsonBtn.addEventListener('click', syncToJSON);

  // Auto-sync functionality
  autoSyncToggle.addEventListener('change', toggleAutoSync);
  syncTimer.addEventListener('change', updateSyncInterval);

  // Sort functionality
  document.addEventListener('click', (e) => {
    if (e.target.closest('th[data-sort]')) {
      const column = e.target.closest('th[data-sort]').dataset.sort;
      handleSort(column);
    }
  });
}

// Load all data
async function loadAllData() {
  showLoading();
  try {
    console.log('🔄 Loading fresh data from Electron app...');

    // Load team data first to establish member-page relationships
    await loadMembersData();
    await loadPagesData();
    // Load posts last so member relationships can be properly assigned
    await loadPostsData();

    updateCounts();
    renderCurrentTable();
    updateLastUpdatedTime();
    hideLoading();

    // Show success notification
    showNotification('✅ Data refreshed successfully! Page assignments are now up to date.', 'success');
    console.log('✅ All data loaded successfully');

    // Ensure auto-sync timer is still running after data load
    ensureAutoSyncRunning();
  } catch (error) {
    console.error('Error loading data:', error);
    showError('Failed to load data');
    showNotification('❌ Failed to refresh data from Electron app', 'error');
    hideLoading();
  }
}

// Load posts data
async function loadPostsData() {
  try {
    let posts = [];

    if (USE_DATABASE) {
      // Try to load from database first
      try {
        const response = await fetch(`${DATABASE_API_URL}/posts`);
        if (response.ok) {
          const result = await response.json();
          posts = result.data || [];
          console.log('Posts API Response:', result);
          console.log('Loaded posts from database:', posts.length);
          console.log('Sample post data:', posts[0]);
        } else {
          throw new Error('Database not available');
        }
      } catch (dbError) {
        console.warn('Database not available, falling back to JSON:', dbError);
        // Load from Electron app API with cache-busting
        const response = await fetch(`http://localhost:3000/api/posts?t=${Date.now()}`);
        posts = await response.json();
      }
    } else {
      // Use Electron app JSON API with cache-busting
      const response = await fetch(`http://localhost:3000/api/posts?t=${Date.now()}`);
      posts = await response.json();
    }

    // Transform data based on source
    if (USE_DATABASE) {
      // Data from database - handle media_urls JSON column
      postsData = posts.map(post => ({
        ...post,
        // Parse media_urls if it's a JSON string, otherwise use as array
        media_urls: typeof post.media_urls === 'string'
          ? JSON.parse(post.media_urls || '[]')
          : (post.media_urls || [])
      }));
    } else {
      // Transform Electron app data to match database structure for display
      postsData = posts.map(post => ({
        id: post.normalizedTextHash || post.id || 'N/A',
        page_id: generatePageId(post.pageUrl),
        member_id: findMemberIdForPage(post.pageUrl),
        caption: post.finalFilteredText || post.caption || 'No caption',
        media_urls: post.mediaUrls || [],
        likes_count: post.engagement?.likes || 0,
        comments_count: post.engagement?.comments || 0,
        views_count: post.engagement?.views || 0,
        shares_count: post.engagement?.shares || 0,
        post_url: post.postUrl || '',
        post_date: post.enhancedPostTime?.fullDate || post.timestamp,
        last_metrics_update: post.lastMetricsUpdate || post.timestamp,
        extraction_timestamp: post.timestamp,
        created_at: post.timestamp
      }));
    }
  } catch (error) {
    console.error('Error loading posts:', error);
    postsData = [];
  }
}

// Load pages data
async function loadPagesData() {
  try {
    let pages = [];

    if (USE_DATABASE) {
      try {
        const response = await fetch(`${DATABASE_API_URL}/pages`);
        if (response.ok) {
          const result = await response.json();
          pages = result.data || [];
          console.log('Loaded pages from database:', pages.length);

          // Use database column names directly (no transformation needed)
          pagesData = pages;
          return;
        }
      } catch (dbError) {
        console.warn('Database not available for pages, falling back to JSON');
      }
    }

    // Load from Electron app API with cache-busting
    const timestamp = Date.now();
    const [pagesResponse, teamResponse] = await Promise.all([
      fetch(`http://localhost:3000/api/pages?t=${timestamp}`),
      fetch(`http://localhost:3000/api/team?t=${timestamp}`)
    ]);

    pages = await pagesResponse.json();
    const team = await teamResponse.json();

    pagesData = pages.map(page => {
      const member = findMemberForPage(page.link, team);

      return {
        id: generatePageId(page.link),
        name: page.name || 'Unknown',
        url: page.link,
        member_id: member?.id || null,
        created_at: new Date().toISOString()
      };
    });
  } catch (error) {
    console.error('Error loading pages:', error);
    pagesData = [];
  }
}

// Load members data
async function loadMembersData() {
  try {
    let members = [];

    if (USE_DATABASE) {
      try {
        const response = await fetch(`${DATABASE_API_URL}/members`);
        if (response.ok) {
          const result = await response.json();
          members = result.data || [];
          console.log('Loaded members from database:', members.length);

          // Also load team data for member-page relationships
          try {
            const teamResponse = await fetch('/api/team');
            window.teamData = await teamResponse.json();
          } catch (teamError) {
            console.warn('Could not load team data for relationships');
            window.teamData = [];
          }

          // Use database column names directly (no transformation needed)
          membersData = members;
          return;
        }
      } catch (dbError) {
        console.warn('Database not available for members, falling back to JSON');
      }
    }

    // Load from Electron app API with cache-busting to get fresh data
    const response = await fetch(`http://localhost:3000/api/team?t=${Date.now()}`);
    const team = await response.json();

    // Store team data globally for member-page relationship lookups
    window.teamData = team;
    console.log('Team data loaded (fresh):', team.length, 'members');

    membersData = team.map(member => {
      return {
        id: member.id,
        name: member.name,
        email: member.email || null,
        role: member.role || 'Member',
        created_at: member.createdAt || new Date().toISOString()
      };
    });
  } catch (error) {
    console.error('Error loading members:', error);
    membersData = [];
  }
}

// Helper functions
function generatePageId(pageUrl) {
  if (!pageUrl) return 'N/A';
  // Extract page name from URL or use hash
  const match = pageUrl.match(/facebook\.com\/([^\/\?]+)/);
  return match ? match[1] : pageUrl.slice(-8);
}

function findMemberIdForPage(pageUrl) {
  // Use the global teamData that should be loaded first
  if (!window.teamData) {
    console.warn('Team data not loaded yet for page:', pageUrl);
    return 'N/A';
  }

  const member = findMemberForPage(pageUrl, window.teamData);

  // Debug logging for member ID 1751121969307
  if (!member && pageUrl) {
    console.log('🔍 DEBUG: Could not find member for page:', pageUrl);
    console.log('🔍 Available team data:', window.teamData.map(m => ({
      id: m.id,
      name: m.name,
      assignedPages: m.assignedPages
    })));
  }

  return member?.id || 'N/A';
}

function findMemberForPage(pageUrl, team) {
  if (!pageUrl || !team) return null;

  return team.find(member => {
    if (!member.assignedPages) return false;

    const found = member.assignedPages.some(assignedPage => {
      if (typeof assignedPage === 'string') {
        const match = pageUrl.includes(assignedPage);
        if (match && member.id === '1751121969307') {
          console.log('🎯 DEBUG: Found match for member 1751121969307!');
          console.log('   pageUrl:', pageUrl);
          console.log('   assignedPage (string):', assignedPage);
        }
        return match;
      } else if (assignedPage.link) {
        const match = pageUrl === assignedPage.link;
        if (match && member.id === '1751121969307') {
          console.log('🎯 DEBUG: Found match for member 1751121969307!');
          console.log('   pageUrl:', pageUrl);
          console.log('   assignedPage.link:', assignedPage.link);
        }
        return match;
      }
      return false;
    });

    // Debug for member 1751121969307
    if (member.id === '1751121969307') {
      console.log(`🔍 DEBUG: Checking member ${member.name} (${member.id})`);
      console.log('   assignedPages:', member.assignedPages);
      console.log('   pageUrl:', pageUrl);
      console.log('   found match:', found);
    }

    return found;
  });
}

// extractMediaId function removed - now using actual media URLs instead

// Table switching
async function switchTable(tableName) {
  currentTable = tableName;

  // Update navigation
  tableNavBtns.forEach(btn => {
    btn.classList.toggle('active', btn.dataset.table === tableName);
  });

  // Update sections
  tableSections.forEach(section => {
    section.classList.toggle('active', section.id === `${tableName}-section`);
  });

  // Always load fresh data when switching tabs
  showLoading();
  try {
    await loadAllData();
    // Update filter options
    updateFilterOptions();
    // Render table
    renderCurrentTable();
  } catch (error) {
    console.error('Error loading data:', error);
    showError('Failed to load data');
  } finally {
    hideLoading();
  }

  // Ensure auto-sync timer is still running after tab switch
  ensureAutoSyncRunning();
}

// Update filter options based on current table
function updateFilterOptions() {
  const filterSelect = document.getElementById('table-filter');
  filterSelect.innerHTML = '<option value="">All</option>';
  
  let options = [];
  
  switch (currentTable) {
    case 'posts':
      options = [...new Set(postsData.map(post => post.pageName))].filter(Boolean);
      break;
    case 'pages':
      options = [...new Set(pagesData.map(page => page.memberName))].filter(Boolean);
      break;
    case 'members':
      options = [...new Set(membersData.map(member => member.role))].filter(Boolean);
      break;
  }
  
  options.sort().forEach(option => {
    const optionElement = document.createElement('option');
    optionElement.value = option;
    optionElement.textContent = option;
    filterSelect.appendChild(optionElement);
  });
}

// Render current table
function renderCurrentTable() {
  let fullData = [];
  let renderFunction = null;

  // Get the full dataset first
  switch (currentTable) {
    case 'posts':
      fullData = postsData;
      renderFunction = renderPostsTable;
      break;
    case 'pages':
      fullData = pagesData;
      renderFunction = renderPagesTable;
      break;
    case 'members':
      fullData = membersData;
      renderFunction = renderMembersTable;
      break;
  }

  // Get filtered data (without pagination) for count
  const filteredData = getFilteredDataWithoutPagination(fullData);

  // Get paginated data for display
  const paginatedData = getFilteredData(fullData);

  if (filteredData.length === 0) {
    showEmptyState();
  } else {
    hideEmptyState();
    renderFunction(paginatedData);
  }

  // Use the full filtered count for pagination, not the paginated count
  updatePagination(filteredData.length);
}

// Get filtered and sorted data WITHOUT pagination (for counting)
function getFilteredDataWithoutPagination(data) {
  let filtered = [...data];

  // Apply search filter
  const searchTerm = searchInput.value.toLowerCase().trim();
  if (searchTerm) {
    filtered = filtered.filter(item => {
      return Object.values(item).some(value => {
        if (value === null || value === undefined) return false;
        return String(value).toLowerCase().includes(searchTerm);
      });
    });
  }

  // Apply dropdown filter
  const filterValue = filterSelect.value;
  if (filterValue) {
    filtered = filtered.filter(item => {
      switch (currentTable) {
        case 'posts':
          return item.pageName === filterValue;
        case 'pages':
          return item.memberName === filterValue;
        case 'members':
          return item.role === filterValue;
        default:
          return true;
      }
    });
  }

  // Apply sorting
  if (currentSort.column) {
    filtered.sort((a, b) => {
      let aVal = a[currentSort.column];
      let bVal = b[currentSort.column];

      // Handle numbers
      if (typeof aVal === 'number' && typeof bVal === 'number') {
        return currentSort.direction === 'asc' ? aVal - bVal : bVal - aVal;
      }

      // Handle dates
      if (currentSort.column.includes('Date') || currentSort.column.includes('Update')) {
        aVal = new Date(aVal);
        bVal = new Date(bVal);
        return currentSort.direction === 'asc' ? aVal - bVal : bVal - aVal;
      }

      // Handle strings
      aVal = String(aVal).toLowerCase();
      bVal = String(bVal).toLowerCase();

      if (currentSort.direction === 'asc') {
        return aVal.localeCompare(bVal);
      } else {
        return bVal.localeCompare(aVal);
      }
    });
  }

  // Return without pagination
  return filtered;
}

// Get filtered and sorted data WITH pagination (for display)
function getFilteredData(data) {
  const filtered = getFilteredDataWithoutPagination(data);

  // Apply pagination
  const startIndex = (currentPage - 1) * itemsPerPage;
  return filtered.slice(startIndex, startIndex + itemsPerPage);
}

// Utility functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Simple notification system
function showNotification(message, type = 'info') {
  // Remove existing notifications
  const existing = document.querySelector('.notification');
  if (existing) existing.remove();

  // Create notification element
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    ${type === 'success' ? 'background: #10b981;' : ''}
    ${type === 'error' ? 'background: #ef4444;' : ''}
    ${type === 'info' ? 'background: #3b82f6;' : ''}
  `;
  notification.textContent = message;

  // Add to page
  document.body.appendChild(notification);

  // Auto remove after 4 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.remove();
    }
  }, 4000);
}

function formatDate(dateString) {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

function formatNumber(num) {
  if (typeof num !== 'number') return '0';
  return num.toLocaleString();
}

function truncateText(text, maxLength = 100) {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

// Update counts in navigation
function updateCounts() {
  document.getElementById('posts-count').textContent = postsData.length;
  document.getElementById('pages-count').textContent = pagesData.length;
  document.getElementById('members-count').textContent = membersData.length;
}

// Update last updated time
function updateLastUpdatedTime() {
  lastUpdatedTime.textContent = new Date().toLocaleString();
}

// State management
function showLoading() {
  loadingState.classList.remove('hidden');
  tableSections.forEach(section => section.style.display = 'none');
}

function hideLoading() {
  loadingState.classList.add('hidden');
  tableSections.forEach(section => {
    if (section.classList.contains('active')) {
      section.style.display = 'block';
    }
  });
}

function showEmptyState() {
  emptyState.classList.remove('hidden');
}

function hideEmptyState() {
  emptyState.classList.add('hidden');
}

function showError(message) {
  // You can implement a toast notification system here
  console.error(message);
}

// Search and filter handlers
function handleSearch() {
  currentPage = 1;

  // Add visual indicator for active search
  const searchTerm = searchInput.value.trim();
  if (searchTerm) {
    searchInput.style.borderColor = '#3b82f6';
    searchInput.style.backgroundColor = '#eff6ff';
  } else {
    searchInput.style.borderColor = '';
    searchInput.style.backgroundColor = '';
  }

  renderCurrentTable();
}

function handleFilter() {
  currentPage = 1;
  renderCurrentTable();
}

// Sort handler
function handleSort(column) {
  if (currentSort.column === column) {
    currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
  } else {
    currentSort.column = column;
    currentSort.direction = 'asc';
  }
  
  // Update sort indicators
  document.querySelectorAll('th[data-sort]').forEach(th => {
    th.classList.remove('sorted-asc', 'sorted-desc');
  });
  
  const currentTh = document.querySelector(`th[data-sort="${column}"]`);
  if (currentTh) {
    currentTh.classList.add(`sorted-${currentSort.direction}`);
  }
  
  renderCurrentTable();
}

// Export functionality
function exportCurrentTable() {
  let data = [];
  let filename = '';
  
  switch (currentTable) {
    case 'posts':
      data = postsData;
      filename = 'posts_data.csv';
      break;
    case 'pages':
      data = pagesData;
      filename = 'pages_data.csv';
      break;
    case 'members':
      data = membersData;
      filename = 'members_data.csv';
      break;
  }
  
  if (data.length === 0) {
    showError('No data to export');
    return;
  }
  
  const csv = convertToCSV(data);
  downloadCSV(csv, filename);
}

function convertToCSV(data) {
  if (data.length === 0) return '';
  
  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    )
  ].join('\n');
  
  return csvContent;
}

function downloadCSV(csv, filename) {
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// Enhanced Pagination with better UX
function updatePagination(totalItems) {
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const paginationInfo = document.getElementById('pagination-info-text');
  const pageNumbers = document.getElementById('page-numbers');
  const prevBtn = document.getElementById('prev-page');
  const nextBtn = document.getElementById('next-page');

  // Update info text with better formatting
  const startItem = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);
  paginationInfo.innerHTML = `
    <strong>${startItem}-${endItem}</strong> of <strong>${totalItems}</strong> entries
    <span style="margin-left: 10px; opacity: 0.7;">Page ${currentPage} of ${totalPages}</span>
  `;

  // Update buttons with enhanced styling
  prevBtn.disabled = currentPage === 1;
  nextBtn.disabled = currentPage === totalPages || totalPages === 0;

  // Add visual feedback for disabled state
  if (prevBtn.disabled) {
    prevBtn.style.opacity = '0.5';
    prevBtn.style.cursor = 'not-allowed';
  } else {
    prevBtn.style.opacity = '1';
    prevBtn.style.cursor = 'pointer';
  }

  if (nextBtn.disabled) {
    nextBtn.style.opacity = '0.5';
    nextBtn.style.cursor = 'not-allowed';
  } else {
    nextBtn.style.opacity = '1';
    nextBtn.style.cursor = 'pointer';
  }

  // Generate page numbers with enhanced logic
  pageNumbers.innerHTML = '';

  if (totalPages <= 9) {
    // Show all pages if 9 or fewer
    for (let i = 1; i <= totalPages; i++) {
      pageNumbers.appendChild(createPageButton(i));
    }
  } else {
    // Enhanced pagination for many pages
    pageNumbers.appendChild(createPageButton(1));

    if (currentPage > 4) {
      pageNumbers.appendChild(createEllipsis());
    }

    const start = Math.max(2, currentPage - 2);
    const end = Math.min(totalPages - 1, currentPage + 2);

    for (let i = start; i <= end; i++) {
      pageNumbers.appendChild(createPageButton(i));
    }

    if (currentPage < totalPages - 3) {
      pageNumbers.appendChild(createEllipsis());
    }

    if (totalPages > 1) {
      pageNumbers.appendChild(createPageButton(totalPages));
    }
  }
  
  // Add event listeners
  prevBtn.onclick = () => {
    if (currentPage > 1) {
      currentPage--;
      renderCurrentTable();
    }
  };
  
  nextBtn.onclick = () => {
    if (currentPage < totalPages) {
      currentPage++;
      renderCurrentTable();
    }
  };
}

function createPageButton(pageNum) {
  const button = document.createElement('a');
  button.href = '#';
  button.className = `page-number ${pageNum === currentPage ? 'active' : ''}`;
  button.textContent = pageNum;
  button.setAttribute('aria-label', `Go to page ${pageNum}`);
  button.setAttribute('role', 'button');

  // Add enhanced styling
  button.style.transition = 'all 0.3s ease';

  button.onclick = (e) => {
    e.preventDefault();
    if (pageNum !== currentPage) {
      currentPage = pageNum;
      renderCurrentTable();

      // Add visual feedback
      button.style.transform = 'scale(0.95)';
      setTimeout(() => {
        button.style.transform = 'scale(1)';
      }, 150);
    }
  };

  // Add hover effects
  button.onmouseenter = () => {
    if (pageNum !== currentPage) {
      button.style.transform = 'translateY(-2px)';
    }
  };

  button.onmouseleave = () => {
    if (pageNum !== currentPage) {
      button.style.transform = 'translateY(0)';
    }
  };

  return button;
}

function createEllipsis() {
  const span = document.createElement('span');
  span.textContent = '...';
  span.className = 'page-ellipsis';
  span.style.padding = '10px 8px';
  span.style.color = 'var(--text-color)';
  span.style.opacity = '0.6';
  span.style.fontSize = '14px';
  span.style.fontWeight = 'bold';
  return span;
}

// Table rendering functions
function renderPostsTable(data) {
  const tbody = document.getElementById('posts-tbody');
  tbody.innerHTML = '';

  console.log('Rendering posts table with data:', data);
  console.log('Data length:', data.length);
  console.log('Sample post for rendering:', data[0]);

  data.forEach(post => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td><span class="cell-id">${truncateText(post.id || 'N/A', 20)}</span></td>
      <td><span class="cell-id">${post.page_id || 'N/A'}</span></td>
      <td><span class="cell-id">${post.member_id || 'N/A'}</span></td>
      <td><div class="cell-caption" title="${post.caption || ''}">${truncateText(post.caption || '', 80)}</div></td>
      <td class="cell-media-urls">
        ${post.media_urls && post.media_urls.length > 0
          ? post.media_urls.map(url => `<a href="${url}" target="_blank" class="media-link" title="${url}">${truncateText(url, 30)}</a>`).join('<br>')
          : 'No media'
        }
      </td>
      <td class="cell-number">${formatNumber(post.likes_count || 0)}</td>
      <td class="cell-number">${formatNumber(post.comments_count || 0)}</td>
      <td class="cell-number">${formatNumber(post.views_count || 0)}</td>
      <td class="cell-number">${formatNumber(post.shares_count || 0)}</td>
      <td class="cell-url">
        ${post.post_url ? `<a href="${post.post_url}" target="_blank" class="url-btn">View</a>` : 'No URL'}
      </td>
      <td class="cell-date">${formatDate(post.post_date)}</td>
      <td class="cell-date">${formatDate(post.last_metrics_update)}</td>
      <td class="cell-date">${formatDate(post.extraction_timestamp)}</td>
      <td class="cell-date">${formatDate(post.created_at)}</td>
    `;
    tbody.appendChild(row);
  });
}

function renderPagesTable(data) {
  const tbody = document.getElementById('pages-tbody');
  tbody.innerHTML = '';

  data.forEach(page => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td><span class="cell-id">${page.id || 'N/A'}</span></td>
      <td>${page.name || 'N/A'}</td>
      <td class="cell-url"><a href="${page.url || '#'}" target="_blank" title="${page.url || ''}">${truncateText(page.url || '', 40)}</a></td>
      <td><span class="cell-id">${page.member_id || 'N/A'}</span></td>
      <td class="cell-date">${formatDate(page.created_at)}</td>
    `;
    tbody.appendChild(row);
  });
}

function renderMembersTable(data) {
  const tbody = document.getElementById('members-tbody');
  tbody.innerHTML = '';

  data.forEach(member => {
    const row = document.createElement('tr');
    row.innerHTML = `
      <td><span class="cell-id">${member.id || 'N/A'}</span></td>
      <td>${member.name || 'N/A'}</td>
      <td>${member.email || 'N/A'}</td>
      <td><span class="cell-badge">${member.role || 'Member'}</span></td>
      <td class="cell-date">${formatDate(member.created_at)}</td>
    `;
    tbody.appendChild(row);
  });
}

// Database Sync Functions - Panel is now always visible
async function initializeSyncPanel() {
  await checkSyncStatus();
}

// Test extraction connections
async function testExtraction() {
  try {
    showLoading();
    console.log('Testing extraction connections...');

    const response = await fetch('http://localhost:3000/api/database/sync-status', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    const result = await response.json();

    console.log('Test results:', result);

    // Show detailed results
    let message = result.message + '\n\n';
    message += 'Debug Info:\n';
    result.debug.forEach(debug => {
      message += debug + '\n';
    });

    if (result.success) {
      alert('✅ Connection Test Passed!\n\n' + message);
    } else {
      alert('❌ Connection Test Failed!\n\n' + message);
    }

  } catch (error) {
    console.error('Test error:', error);
    alert('❌ Test failed: ' + error.message);
  } finally {
    hideLoading();
  }
}



async function checkSyncStatus() {
  const statusDiv = document.getElementById('sync-status');
  const actionsDiv = document.getElementById('sync-actions');

  try {
    statusDiv.innerHTML = '<div class="status-loading"><i class="fas fa-spinner fa-spin"></i> Checking sync status...</div>';

    const response = await fetch(`http://localhost:3000/api/database/sync-status`);
    if (!response.ok) {
      throw new Error('Database not available');
    }

    const result = await response.json();
    const stats = result.data || {};

    // Display database stats
    statusDiv.innerHTML = `
      <div class="status-grid">
        <div class="status-item">
          <h4><i class="fas fa-file-alt"></i> Posts</h4>
          <div class="status-counts">
            <span class="status-label">Database:</span>
            <span class="status-number">${stats.posts_count || 0}</span>
          </div>
          <div class="status-sync-needed synced">
            Data Available
          </div>
        </div>
        <div class="status-item">
          <h4><i class="fas fa-globe"></i> Pages</h4>
          <div class="status-counts">
            <span class="status-label">Database:</span>
            <span class="status-number">${stats.pages}</span>
          </div>
          <div class="status-sync-needed synced">
            Data Available
          </div>
        </div>
        <div class="status-item">
          <h4><i class="fas fa-users"></i> Members</h4>
          <div class="status-counts">
            <span class="status-label">Database:</span>
            <span class="status-number">${stats.members}</span>
          </div>
          <div class="status-sync-needed synced">
            Data Available
          </div>
        </div>
      </div>
    `;

    actionsDiv.classList.remove('hidden');

  } catch (error) {
    console.error('Error checking sync status:', error);
    statusDiv.innerHTML = `
      <div class="status-error">
        <i class="fas fa-exclamation-triangle"></i>
        <p>Database connection failed. Please check your database configuration.</p>
        <p>Error: ${error.message}</p>
      </div>
    `;
    actionsDiv.classList.add('hidden');
  }
}

async function syncFromJSON() {
  const button = document.getElementById('sync-from-json');
  const resultsDiv = document.getElementById('sync-results');
  const resultsContent = document.getElementById('sync-results-content');

  try {
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Extracting Posts Data...';

    const response = await fetch('http://localhost:3000/api/database/sync', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const result = await response.json();

    if (result.success) {
      resultsContent.innerHTML = `
        <div class="sync-success">
          <i class="fas fa-check-circle"></i>
          <h4>✅ Posts Data Extraction Completed!</h4>
          <p class="sync-description">Core post data and relationships extracted to MySQL database</p>
          <div class="sync-result-item">
            <span class="sync-result-label">📝 Posts Extracted:</span>
            <span class="sync-result-count success">${result.results.posts}</span>
          </div>
          <div class="sync-result-item">
            <span class="sync-result-label">📄 Pages Linked:</span>
            <span class="sync-result-count success">${result.results.pages}</span>
          </div>
          <div class="sync-result-item">
            <span class="sync-result-label">👥 Members Linked:</span>
            <span class="sync-result-count success">${result.results.members}</span>
          </div>
          ${result.results.errors && result.results.errors.length > 0 ? `
            <div class="sync-result-item">
              <span class="sync-result-label">Warnings:</span>
              <span class="sync-result-count error">${result.results.errors.length}</span>
            </div>
          ` : ''}
          <p><strong>Database:</strong> localhost/facebook_db</p>
        </div>
      `;

      // Refresh data after successful sync
      await loadAllData();
      await checkSyncStatus();

    } else {
      resultsContent.innerHTML = `
        <div class="sync-error">
          <i class="fas fa-exclamation-triangle"></i>
          <h4>❌ Sync Failed</h4>
          <p>${result.message || 'Unknown error'}</p>
          <p><strong>Make sure XAMPP MySQL is running!</strong></p>
        </div>
      `;
    }

    resultsDiv.classList.remove('hidden');

  } catch (error) {
    console.error('Error syncing from JSON:', error);
    resultsContent.innerHTML = `
      <div class="sync-error">
        <i class="fas fa-exclamation-triangle"></i>
        <h4>❌ Connection Error</h4>
        <p>Could not connect to database: ${error.message}</p>
        <p><strong>Check if XAMPP is running on localhost</strong></p>
      </div>
    `;
    resultsDiv.classList.remove('hidden');
  } finally {
    button.disabled = false;
    button.innerHTML = '<i class="fas fa-upload"></i> Extract Posts → Database';
  }
}

async function syncToJSON() {
  const button = document.getElementById('sync-to-json');

  try {
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';

    // This would require additional backend implementation
    // For now, show a message that this feature is coming soon
    alert('Sync Database → JSON feature is coming soon!');

  } catch (error) {
    console.error('Error syncing to JSON:', error);
    alert('Error: ' + error.message);
  } finally {
    button.disabled = false;
    button.innerHTML = '<i class="fas fa-download"></i> Sync Database → JSON';
  }
}

// Auto-Sync Timer Functions
function initializeAutoSync() {
  // Load saved settings from localStorage
  const savedEnabled = localStorage.getItem('autoSyncEnabled') === 'true';
  const savedInterval = localStorage.getItem('autoSyncInterval') || '300000';
  const savedCount = parseInt(localStorage.getItem('autoSyncCount')) || 0;
  const savedNextSyncTime = localStorage.getItem('autoSyncNextTime');

  autoSyncEnabled = savedEnabled;
  autoSyncInterval = parseInt(savedInterval);
  totalSyncCount = savedCount;

  // Restore next sync time if it exists and is in the future
  if (savedNextSyncTime) {
    const savedTime = parseInt(savedNextSyncTime);
    if (savedTime > Date.now()) {
      nextSyncTime = savedTime;
    }
  }

  // Update UI
  autoSyncToggle.checked = autoSyncEnabled;
  syncTimer.value = autoSyncInterval;
  syncCountDisplay.textContent = totalSyncCount;

  if (autoSyncEnabled) {
    startAutoSync();
  }

  updateAutoSyncStatus();

  console.log('🔄 Auto-sync initialized:', {
    enabled: autoSyncEnabled,
    interval: formatInterval(autoSyncInterval),
    nextSync: nextSyncTime ? new Date(nextSyncTime).toLocaleTimeString() : 'none'
  });
}

function toggleAutoSync() {
  autoSyncEnabled = autoSyncToggle.checked;
  localStorage.setItem('autoSyncEnabled', autoSyncEnabled);

  if (autoSyncEnabled) {
    startAutoSync();
    showNotification('Auto-sync enabled! Will run every ' + formatInterval(autoSyncInterval), 'success');
  } else {
    stopAutoSync();
    showNotification('Auto-sync disabled', 'info');
  }

  updateAutoSyncStatus();
}

function updateSyncInterval() {
  autoSyncInterval = parseInt(syncTimer.value);
  localStorage.setItem('autoSyncInterval', autoSyncInterval);

  if (autoSyncEnabled) {
    // Restart with new interval
    stopAutoSync();
    startAutoSync();
    showNotification('Auto-sync interval updated to ' + formatInterval(autoSyncInterval), 'info');
  }
}

function startAutoSync() {
  if (autoSyncTimer) {
    clearInterval(autoSyncTimer);
  }
  if (countdownInterval) {
    clearInterval(countdownInterval);
  }

  // Calculate next sync time (if not already set from previous session)
  if (!nextSyncTime || nextSyncTime <= Date.now()) {
    nextSyncTime = Date.now() + autoSyncInterval;
  }

  // Save next sync time to localStorage
  localStorage.setItem('autoSyncNextTime', nextSyncTime.toString());

  // Calculate remaining time until next sync
  const remainingTime = nextSyncTime - Date.now();

  // Start the main timer for the remaining time, then repeat at intervals
  autoSyncTimer = setTimeout(() => {
    performAutoSync();
    // After first sync, set up regular interval
    autoSyncTimer = setInterval(() => {
      performAutoSync();
    }, autoSyncInterval);
  }, Math.max(0, remainingTime));

  // Start countdown display
  startCountdown();

  autoSyncInfo.style.display = 'block';
  console.log(`🔄 Auto-sync started - next sync in ${formatTime(remainingTime)}, then every ${formatInterval(autoSyncInterval)}`);
}

function stopAutoSync() {
  if (autoSyncTimer) {
    clearTimeout(autoSyncTimer);
    clearInterval(autoSyncTimer);
    autoSyncTimer = null;
  }
  if (countdownInterval) {
    clearInterval(countdownInterval);
    countdownInterval = null;
  }

  autoSyncInfo.style.display = 'none';
  nextSyncTime = null;

  // Clear saved next sync time
  localStorage.removeItem('autoSyncNextTime');

  console.log('⏹️ Auto-sync stopped');
}

function startCountdown() {
  if (countdownInterval) {
    clearInterval(countdownInterval);
  }

  countdownInterval = setInterval(() => {
    if (!nextSyncTime) return;

    const remaining = nextSyncTime - Date.now();
    const timeText = remaining <= 0 ? formatInterval(autoSyncInterval) : formatTime(remaining);

    if (remaining <= 0) {
      nextSyncTime = Date.now() + autoSyncInterval;
      localStorage.setItem('autoSyncNextTime', nextSyncTime.toString());
    }

    // Update both panel and header countdown
    if (countdownTimer) countdownTimer.textContent = timeText;
    if (headerCountdown) headerCountdown.textContent = `Next: ${timeText}`;
  }, 1000);
}

async function performAutoSync() {
  try {
    console.log('🔄 Auto-sync triggered - running "Extract Posts → Database"');

    // Simulate clicking the "Extract Posts → Database" button
    await syncFromJSON();

    // Update sync count
    totalSyncCount++;
    syncCountDisplay.textContent = totalSyncCount;
    localStorage.setItem('autoSyncCount', totalSyncCount);

    // Set next sync time and save it
    nextSyncTime = Date.now() + autoSyncInterval;
    localStorage.setItem('autoSyncNextTime', nextSyncTime.toString());

    console.log(`✅ Auto-sync completed successfully (${totalSyncCount} total syncs)`);
    showNotification(`Auto-sync completed! (${totalSyncCount} total syncs)`, 'success');

  } catch (error) {
    console.error('❌ Auto-sync failed:', error);
    showNotification('Auto-sync failed: ' + error.message, 'error');

    // Still set next sync time even if this one failed
    nextSyncTime = Date.now() + autoSyncInterval;
    localStorage.setItem('autoSyncNextTime', nextSyncTime.toString());
  }
}

function updateAutoSyncStatus() {
  if (autoSyncEnabled) {
    // Panel status
    autoSyncStatus.textContent = 'ON';
    autoSyncStatus.style.color = '#4CAF50';

    // Header status
    headerSyncStatus.textContent = `Auto-sync: ON (${formatInterval(autoSyncInterval)})`;
    headerAutoSyncIndicator.classList.add('active');
    headerCountdown.style.display = 'inline';
  } else {
    // Panel status
    autoSyncStatus.textContent = 'OFF';
    autoSyncStatus.style.color = '#ff6b6b';

    // Header status
    headerSyncStatus.textContent = 'Auto-sync: OFF';
    headerAutoSyncIndicator.classList.remove('active');
    headerCountdown.style.display = 'none';
  }
}

function formatInterval(ms) {
  const minutes = Math.floor(ms / 60000);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''}`;
  } else {
    return `${minutes} minute${minutes > 1 ? 's' : ''}`;
  }
}

function formatTime(ms) {
  const totalSeconds = Math.floor(ms / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m ${seconds}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds}s`;
  } else {
    return `${seconds}s`;
  }
}

// Ensure auto-sync timer is running (call this from any function that might reset state)
function ensureAutoSyncRunning() {
  const savedEnabled = localStorage.getItem('autoSyncEnabled') === 'true';
  if (savedEnabled && !autoSyncTimer) {
    console.log('🔄 Restoring auto-sync timer after state reset');
    autoSyncEnabled = true;
    autoSyncInterval = parseInt(localStorage.getItem('autoSyncInterval')) || 300000;
    totalSyncCount = parseInt(localStorage.getItem('autoSyncCount')) || 0;

    const savedNextSyncTime = localStorage.getItem('autoSyncNextTime');
    if (savedNextSyncTime) {
      const savedTime = parseInt(savedNextSyncTime);
      if (savedTime > Date.now()) {
        nextSyncTime = savedTime;
      }
    }

    startAutoSync();
    updateAutoSyncStatus();
  }
}

// Panel toggle function
function toggleSyncPanel() {
  syncPanel.classList.toggle('minimized');
  const icon = document.getElementById('sync-toggle-icon');
  if (syncPanel.classList.contains('minimized')) {
    icon.className = 'fas fa-chevron-down';
  } else {
    icon.className = 'fas fa-chevron-up';
  }
}

// Make sync functions available globally
window.toggleSyncPanel = toggleSyncPanel;
